### 📄 Description

<!-- Provide a brief explanation of the change. Include relevant context or background if needed. -->

---

### 🧩 Type of Change

<!-- Select one by marking [x] -->

- [ ] Feature
- [ ] Bug Fix
- [ ] Infrastructure Change
- [ ] CI/CD Pipeline Update
- [ ] Documentation
- [ ] Refactor / Cleanup
- [ ] Security Patch
- [ ] Other: <!-- Specify -->

---

### 🔗 Task Reference

- **Task ID:** <!-- e.g., JIRA-123, GH-456 -->
- **Task Name:** <!-- e.g., "Enable lifecycle policy for ECR cleanup" -->

---

### 📝 Summary of Changes

<!-- List key changes, configurations, modules, or resources impacted -->

- <!-- Change 1 -->
- <!-- Change 2 -->
- <!-- Change 3 -->

---

### ✅ Checklist

- [ ] Code compiles and passes linting
- [ ] Tests added or updated (if applicable)
- [ ] Verified in staging or test environment
- [ ] Infra changes documented (if applicable)
- [ ] All reviewers assigned and notified

---

### 📎 Additional Notes (Optional)

<!-- Add screenshots, logs, rollback plan, or anything else useful -->