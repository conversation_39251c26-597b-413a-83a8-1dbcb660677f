from enum import Enum
import logging
from BromptonPythonUtilities.enums import Aggregate, BucketSize

# Initialize logger
logger = logging.getLogger(__name__)

class ThresholdType(str, Enum):
    NOMINAL = 'NOMINAL'
    HIGH = 'HIGH'
    LOW = 'LOW'
    STALE = 'STALE'
    DEAD = 'DEAD'
    ANOMALY = 'ANOMALY'  # Added for future use


class Comparison(str, Enum):
    LT = 'LT'
    LE = 'LE'
    GT = 'GT'
    GE = 'GE'
    EQ = 'EQ'


class AggregatePeriod(str, Enum):
    _1M = '1min'
    _2M = '2min'
    _5M = '5min'
    _10M = '10min'
    _15M = '15min'
    _20M = '20min'
    _30M = '30min'
    _1H = '1hr'
    _2H = '2hr'
    _4H = '4hr'
    _6H = '6hr'
    _8H = '8hr'
    _12H = '12hr'
    DAILY = 'DAILY'
    WEEKLY = 'WEEKLY'
    MONTHLY = 'MONTHLY'
    NONE = 'none'
    raw = 'raw'


# Create backwards-compatible BucketSize dictionary
BucketSize_Dict = {}
for size in BucketSize:
    BucketSize_Dict[size.name] = BucketSize(size.value)
    # Add hr suffix versions for backward compatibility
    if size.name in ['_1H', '_2H', '_4H', '_6H', '_8H', '_12H']:
        hr_name = f"{size.name[:-1]}hr"
        BucketSize_Dict[hr_name] = BucketSize(size.value)

# Task is given a list of meas_ids to read requested agg and period
# Reads at least the largest of now-MINSPAN minutes or now-2 periods and uses the last value given
# Agg can also be none in which case it reads now-MINSPAN
# measurements are AssetPath -> {Tag: id}
# api = url to api
MINSPAN = 300000  # in milliseconds


class LimitState(Enum):
    NORMAL = 0
    EXCEEDED = 1
    STALE = 2
    DEAD = 3


class CompareOperation(Enum):
    EQ = 0
    LT = 1
    LE = 2
    GT = 3
    GE = 4


class LimitSetting(Enum):
    LIMIT = 0
    DEADBAND = 1


CompareOperation_Dict = {}
for member in CompareOperation:
    CompareOperation_Dict[member.name] = member
