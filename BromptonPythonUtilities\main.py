from Logging_Utility.logging_config import setup_logging
from Mqtt_Utility import mqtt_objects, mqtt_db_utils
from Calculations_Utility import calc_utils
import psycopg2
import time

logger = setup_logging()
def main():
    """
    Main function to execute the script.
    This function is called when the script is run directly.
    """
    # # Initialize logging configuration
    # logger = logging_config.setup_logging()

    # # Your main code logic goes here
    # logger.info("Hello, World!")
    
    # Create PostgreSQL connection
    db_hook = psycopg2.connect(
        host="dataloggertest.clclbj3j3ehf.us-east-1.rds.amazonaws.com",
        database="dataloggertest",
        user="postgres",
        password="Br0mpt0n!0T",
        port=5432
    )

    # topics = mqtt_db_utils.extract_topics_from_db("mqtt1", hook=db_hook)

    # logger.info(f"Topics from DB: {topics}")

    # print("-----------------------")
    # messages = {'spBv1/0/TACW_MicroGrid/NDATA/MICROTURBINES_520': [
    #         {'name': 'OutputPowerGridConnectGroup', 'timestamp': '1744114820315', 'datatype': 10, 'doubleValue': 639.809
    #         }],
    # }
    # data = mqtt_db_utils.extract_data_from_spB_messages(messages, hook=db_hook)

    # logger.info(f"Data extracted from messages: {data}")

    # print("-----------------------")

    # # Initialize MQTT session publisher and listener
    # client_id = "mqtt_client"
    # broker = 'bromptonenergy.io'
    # port= 8883
    # mqtt_username = 'test'
    # mqtt_password = 'Br0mpt0n!0T'
    # topics=['spBv1.0/Brenes_Gateway/NDATA/#', 'spBv1.0/Brompton_Simulation/NDATA/#']
    # listener = mqtt_objects.MQTTSessionListener(client_id=client_id, broker=broker, port=port, topics=topics, mqtt_username=mqtt_username, mqtt_password=mqtt_password)
    # time.sleep(30)
    # listener.stop()
    # time.sleep(5) # wait for listener to fully stop
    # messages = listener.get_messages(client_id=client_id)
    # logger.info(f"Messages received: {messages}")
    
    #TODO: Need to Validate the publisher working
    # now = round(time.time())
    # topics_needing_rebirth=[]
    # publisher = mqtt_objects.MQTTSessionPublisher(client_id=client_id, broker=broker, port=port, mqtt_username=mqtt_username, mqtt_password=mqtt_password)
    # publisher.publish_ncmd_for_rebirth(topics_needing_rebirth, now)

    print("-----------------------")
    period = calc_utils.get_poll_in_minutes("5min") 
    logger.info(f"Poll period in minutes: {period}")

    print("-----------------------")
    offline_calc = calc_utils.get_offline_calcs(db_hook)
    logger.info(f"Offline calculations: {offline_calc}")

    print("-----------------------")
    calc_graphs= [22583]
    graphs = calc_utils.get_calcs_graph(db_hook, ids=calc_graphs)
    logger.info(f"Graphs: {graphs}")


if __name__ == "__main__":
    main()