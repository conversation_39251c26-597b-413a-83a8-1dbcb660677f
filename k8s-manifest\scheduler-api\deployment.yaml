apiVersion: apps/v1
kind: Deployment
metadata:
  name: scheduler-api
  namespace: application
  labels:
    app: scheduler-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: scheduler-api
  template:
    metadata:
      labels:
        app: scheduler-api
      annotations:
        instrumentation.opentelemetry.io/inject-python: openobserve-collector/openobserve-python
    spec:
      volumes:
        - name: config-volume
          configMap:
            name: celery-config-${ENV}
      containers:
      - name: scheduler-api-${ENV}
        image: ${ECR_REGISTRY}/${ECR_REPO_NAME}:${IMAGE_TAG}
        command:
          - uvicorn
          - app.main:app
          - '--host'
          - 0.0.0.0
          - '--port'
          - '8000'
        ports:
        - containerPort: 8000
        env:
        - name: sentinel
          value: 'false'  
        volumeMounts:
        - name: config-volume
          mountPath: /app/config.ini
          subPath: config.ini
        resources:
          limits:
            cpu: 500m
            memory: 500Mi
          requests:
            cpu: 200m
            memory: 200Mi
        
     
