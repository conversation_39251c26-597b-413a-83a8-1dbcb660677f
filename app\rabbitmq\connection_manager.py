import pika
import threading
import atexit
from app.config import config
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
import pika.exceptions

logger = setup_logging()

# RabbitMQ Configuration
def get_rabbitmq_config():
    return {
        'host': config.get('rabbitmq', 'host', fallback='bromptonenergy.io'),
        'port': config.getint('rabbitmq', 'port', fallback=5672),
        'username': config.get('rabbitmq', 'username', fallback='guest'),
        'password': config.get('rabbitmq', 'password', fallback='guest'),
        'exchange': config.get('rabbitmq', 'notifications_topic_exchange', fallback='notifications_topic_exchange'),
        'queue': config.get('rabbitmq', 'alert.consumer.queue', fallback='alert.consumer.queue'),
    }

class RabbitMQConnectionManager:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(RabbitMQConnectionManager, cls).__new__(cls)
                cls._instance.connection = None
                cls._instance.channel = None
                cls._instance.config = get_rabbitmq_config()
        return cls._instance

    def get_channel(self):
        with self._lock:
            if self.connection is None or self.channel is None or self.connection.is_closed or self.channel.is_closed:
                self._connect()
            return self.channel

    def _connect(self):
        cfg = self.config
        try:
            logger.debug(f"Attempting to connect to RabbitMQ at {cfg['host']}:{cfg['port']}")
            credentials = pika.PlainCredentials(cfg['username'], cfg['password'])
            parameters = pika.ConnectionParameters(
                host=cfg['host'],
                port=cfg['port'],
                credentials=credentials,
                heartbeat=20,
                blocked_connection_timeout=20,
                connection_attempts=3,
                retry_delay=5,
                socket_timeout=10
            )
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()
            self.channel.exchange_declare(exchange=cfg['exchange'], exchange_type='topic', durable=True)
            self.channel.queue_declare(queue=cfg['queue'], durable=True)
            self.channel.queue_bind(exchange=cfg['exchange'], queue=cfg['queue'], routing_key='alert.*')
            logger.info(f"Connected to RabbitMQ at {cfg['host']}:{cfg['port']}")
        except Exception as e:
            logger.error(f"Failed to connect to RabbitMQ at {cfg['host']}:{cfg['port']}: {e}")
            raise

    def close(self):
        with self._lock:
            try:
                if self.connection and not self.connection.is_closed:
                    self.connection.close()
                    logger.info("RabbitMQ connection closed.")
            except Exception as e:
                logger.error(f"Failed to close RabbitMQ connection: {e}")

# Register graceful shutdown for RabbitMQ connection
atexit.register(RabbitMQConnectionManager().close)
