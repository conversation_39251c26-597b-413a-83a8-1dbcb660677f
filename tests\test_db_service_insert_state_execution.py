import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime
from app.db import db_service

class DummyEnum:
    def __init__(self, value, label=None):
        self.value = value
        self.label = label or str(value)

class DummyAlert:
    def __init__(self, aggregate_enum=None, aggregate_period_enum=None, measurement_id=1, customer_id=1):
        self.aggregate_enum = aggregate_enum
        self.aggregate_period_enum = aggregate_period_enum
        self.measurement_id = measurement_id
        self.customer_id = customer_id

class DummyEvent:
    def __init__(self, timestamp):
        self.timestamp = timestamp

@pytest.mark.parametrize("agg_enum, period_enum, expect_agg", [
    (DummyEnum("AVG", "avg"), DummyEnum("15M", "15m"), True),
    (None, DummyEnum("15M", "15m"), False),
    (DummyEnum("AVG", "avg"), None, False),
    (None, None, False),
    (Dummy<PERSON>num(None), DummyEnum("15M", "15m"), False),
    (DummyEnum("AVG", "avg"), DummyEnum(None), False),
])
def test_insert_state_execution_url(monkeypatch, agg_enum, period_enum, expect_agg):
    # Patch DB session and models
    dummy_alert = DummyAlert(aggregate_enum=agg_enum, aggregate_period_enum=period_enum)
    dummy_event = DummyEvent(timestamp=datetime.now())
    
    class DummySession:
        def query(self, model):
            class DummyQuery:
                def filter(self, *a, **k):
                    class DummyOrder:
                        def order_by(self, *a, **k):
                            class DummyFirst:
                                def first(self):
                                    if model.__name__ == "Events":
                                        return dummy_event
                                    if model.__name__ == "Alerts":
                                        return dummy_alert
                            return DummyFirst()
                        def all(self):
                            return []
                        def first(self):  # Support .first() directly after filter
                            if model.__name__ == "Events":
                                return dummy_event
                            if model.__name__ == "Alerts":
                                return dummy_alert
                    return DummyOrder()
            return DummyQuery()
        def close(self):
            pass
        def add(self, obj):
            pass
        def commit(self):
            pass
        def rollback(self):
            pass
    
    monkeypatch.setattr(db_service.database, "get_db", lambda: iter([DummySession()]))
    monkeypatch.setattr(db_service.models, "ExcursionStates", lambda **kwargs: MagicMock(id=1))
    monkeypatch.setattr(db_service.requests, "get", lambda *a, **k: MagicMock(status_code=200, json=lambda: []))
    monkeypatch.setattr(db_service.logger, "info", lambda *a, **k: None)
    monkeypatch.setattr(db_service.logger, "debug", lambda *a, **k: None)
    monkeypatch.setattr(db_service.logger, "error", lambda *a, **k: None)
    monkeypatch.setattr(db_service.logger, "exception", lambda *a, **k: None)
    monkeypatch.setattr(db_service.statistics, "mean", lambda x: 0)
    
    # Patch f-string url construction to capture the url
    captured = {}
    def fake_logger_info(msg, *args, **kwargs):
        if "Sending GET request to" in msg:
            captured["url"] = msg.split("Sending GET request to ")[1].split(" for")[0]
    monkeypatch.setattr(db_service.logger, "info", fake_logger_info)

    db_service.insert_state_execution(1, datetime.now().timestamp() * 1000)
    url = captured.get("url", "")
    if expect_agg:
        assert "/agg/" in url and "agg=" in url and "agg_period=" in url
    else:
        assert "/history/" in url and "agg=" not in url and "agg_period=" not in url
