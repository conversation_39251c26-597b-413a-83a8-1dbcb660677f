# Alert Service Refactor: Full-Window Evaluation & Timestamp Tracking

## Overview

This document describes the major refactor to the alert evaluation logic, enabling robust, stateful, and accurate alerting by:

- Evaluating all data points in the window (not just the latest)
- Handling delayed and out-of-order data
- Preventing duplicate or misleading notifications
- Tracking the last processed timestamp for each alert

## Key Changes

### 1. Full-Window Evaluation

- The alert logic now processes **all** data points in the evaluation window for each measurement.
- This ensures that no data point is missed, even if it arrives late or out of order.

### 2. Delayed/Out-of-Order Data Handling

- Only data points with a timestamp **greater than the last processed timestamp** for each alert are considered.
- This prevents duplicate notifications and ensures correct state transitions.

### 3. Timestamp Tracking

- A new column `last_processed_ts` (integer, ms since epoch) was added to the `alerts` table.
- For each alert, the last processed timestamp is fetched before evaluation and updated after processing new data points.
- Functions: `fetch_last_processed_timestamp(alert_id)` and `update_last_processed_timestamp(alert_id, new_ts)` in `db_service.py`.

### 4. Database Migration Required

- The `last_processed_ts` column has been added to the `alerts` table in the dev database (BromptonDash repo) using a MikroORM migration:

```typescript
import { Migration } from '@mikro-orm/migrations';

export class Migration20250702093957_alertTb_add_lastProcessedTs extends Migration {
  async up(): Promise<void> {
    this.addSql('ALTER TABLE alerts ADD COLUMN last_processed_ts INTEGER NULL;');
  }
  async down(): Promise<void> {
    this.addSql('ALTER TABLE alerts DROP COLUMN last_processed_ts;');
  }
}
```

- The ORM model (`models.py`) has been updated accordingly (already done in this refactor).

### 5. Test Coverage

- Tests should cover:
  - Windowed evaluation (multiple data points per window)
  - Delayed/out-of-order data
  - Prevention of duplicate/misleading notifications
  - State transitions for all alert types (NOMINAL, DEAD, STALE)

### 6. Documentation

- This logic is now described in this file. Update your main `README.md` and `tests/README.md` as needed.

## Example Workflow

1. Fetch all data points for each measurement in the window.
2. For each alert, fetch its `last_processed_ts`.
3. For each data point (sorted by timestamp):
   - If `timestamp` > `last_processed_ts`, evaluate the alert logic.
   - If a state change or notification is triggered, update the alert state and send notification.
   - After processing, update `last_processed_ts` to the latest processed timestamp.

## Why This Matters

- **Reliability:** No missed or duplicate alerts, even with network or data delays.
- **Accuracy:** State transitions and notifications reflect the true state of the data.
- **Scalability:** Supports high-frequency and high-volume data streams robustly.

## Migration Steps

1. Apply the DB migration to add `last_processed_ts`.
2. Deploy the updated codebase.
3. Ensure tests pass for all alert types and edge cases.

## References

- See `tasks.py`, `TSDBReaderService.py`, `db_service.py`, and `models.py` for implementation details.
- See `tests/` for test cases covering the new logic.

---

**Contact the engineering team for questions or support regarding this refactor.**
