# Brompton Python Utilities - Centralized Enums

This package provides centralized enum management for the BromptonPythonUtilities project. It serves as a single source of truth for all enumerations used across different modules.

## Purpose

The enums package is designed to:
- Provide a central location for all enumerations
- Ensure consistency across different modules
- Make enums easily shareable among projects
- Reduce code duplication
- Simplify maintenance and updates

## Structure

```
enums/
├── __init__.py          # Exports all enums
├── tsdb_enums.py        # Time Series Database related enums
├── tests/               # Test directory
│   ├── __init__.py     # Test package init
│   └── test_tsdb_enums.py  # TSDB enum tests
└── README.md            # This file
```

## Available Enums

### TSDB Enums (`tsdb_enums.py`)

1. `Aggregate`: Aggregation types for time series data
   - TWA: Time-weighted average
   - MAX: Maximum value
   - MIN: Minimum value
   - AVG: Simple average
   - STD: Population standard deviation
   - RATETOTAL: Rate calculation (twa * factor)
   - Various DELTA aggregations (TWA, AVG, MAX, MIN, STD)

2. `BucketSize`: Time bucket sizes for aggregation
   - Various time intervals from 1 minute to 1 month
   - All values are in milliseconds
   - Examples: _1M (1 minute), _1H (1 hour), DAILY, WEEKLY, MONTHLY

## Usage

```python
# Import specific enums
from BromptonPythonUtilities.enums.tsdb_enums import Aggregate, BucketSize

# Or import all enums
from BromptonPythonUtilities.enums import *

# Example usage
bucket_size = BucketSize._1H  # 1 hour bucket
agg_type = Aggregate.TWA      # Time-weighted average
```

## Testing

### Running Tests

```bash
# Run all tests with verbose output
pytest -v

# Run specific test file
pytest tests/test_tsdb_enums.py -v

# Run specific test class
pytest tests/test_tsdb_enums.py::TestAggregate -v

# Run specific test function
pytest tests/test_tsdb_enums.py::TestAggregate::test_aggregate_values -v

# Run with coverage report
pytest --cov=BromptonPythonUtilities.enums --cov-report=html
```

### Test Structure

The test suite includes:
- Value validation for all enums
- Type checking and inheritance verification
- Import path validation
- Usage examples and patterns
- String representation tests
- Arithmetic and comparison operations (where applicable)

### Adding New Tests

When adding new enums:
1. Create corresponding test file in `tests/`
2. Follow existing test patterns
3. Include both positive and negative test cases
4. Test all enum values and operations
5. Verify import paths work correctly

## Adding New Enums

When adding new enums:
1. Create a new file in the enums package if it's a new category
2. Update `__init__.py` to export the new enums
3. Document the enums in this README
4. Follow the established naming and documentation patterns
5. Add corresponding tests in the tests directory

## Best Practices

1. Always import enums from this central package
2. Document any new enums thoroughly
3. Keep enum values consistent across the project
4. Use descriptive names for enum values
5. Group related enums in appropriate files
6. Write comprehensive tests for new enums