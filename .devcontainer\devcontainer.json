// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/python
{
  "name": "Python 3",
  "dockerComposeFile": [
    "docker-compose.yml"
  ],
  //  runServices: ["api", "celery_worker", "celery_beat"],
  "service": "celery_worker", // specify the main service
  // Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
  //  "image": "mcr.microsoft.com/devcontainers/python:1-3.11-bullseye",
  // Features to add to the dev container. More info: https://containers.dev/features.
  // "features": {},
  "workspaceFolder": "/app",
  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  "forwardPorts": [
    8000,
    6379
  ],
  // Use 'postCreateCommand' to run commands after the container is created.
  "postCreateCommand": "pip install --user -r requirements.txt",
  // Configure tool-specific properties.
  "customizations": {
    "jetbrains": {
      "backend": "PyCharm",
      "settings": {
        "com.intellij:app:BuiltInServerOptions.builtInServerPort": 65418,
        "Docker:app:DockerSettings.dockerComposePath": "docker",
        "Docker:app:DockerSettings.dockerPath": "docker"
      }
    },
    "vscode": {
      "extensions": [
        "ms-vscode-remote.remote-containers",
        "ms-python.python",
      ],
      "settings": {
        "remote.extensionKind": {
          "ms-vscode-remote.remote-containers": "workspace"
        }
      }
    }
  },
  // Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
  "remoteUser": "root"
}