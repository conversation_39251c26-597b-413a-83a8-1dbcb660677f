# Use the official Python image
FROM python:3.11

# Set working directory
WORKDIR /app

# Install system dependencies for psycopg2 and starkbank-ecdsa
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    gcc \
    libssl-dev \
    libffi-dev \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install them with wheel support
COPY requirements.txt .

# Upgrade pip and install build tools
RUN pip install --upgrade pip setuptools wheel \
    && pip install -r requirements.txt

# Copy application files
COPY __init__.py .
COPY ./app ./app

# Copy utility module and set PYTHONPATH
COPY ./BromptonPythonUtilities ./BromptonPythonUtilities
ENV PYTHONPATH="/app:${PYTHONPATH}"

# Handle environment-specific config
# ARG ENV
# ENV ENV $ENV
# COPY config_${ENV}.ini ./config.ini

# Expose the FastAPI port
EXPOSE 8000

# Start the FastAPI app
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]