from .asyncevent import *
from enum import Enum
from typing import Union

class LimitState(Enum):
    EXCEEDED=1
    NORMAL=0

class CompareOperation(Enum):
    EQ=0
    LT=1
    LE=2
    GT=3
    GE=4

class LimitSetting(Enum):
    LIMIT=0
    DEADBAND=1

CompareOperation_Dict={}
for member in CompareOperation:
    CompareOperation_Dict[member.name]=member

# Return event with new value when value changes outside a deadband
class AsyncStateChangeTask(AsyncTaskWithEvent):
    def __init__(self,event_context:str,deadband:Union[float,int]=0,completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        super().__init__(event_context=event_context, completion_callback=completion_callback, timeout=timeout,
                         persist_obj=persist_obj)
        self.__deadband__=None
        self.deadband=deadband
        self.__prev_value__=None

    def __log_task_deadband_change__(self,deadband:Union[float,int],newDeadband:Union[float,int]):
        self.__logger__.info(f"Deadband changed from {deadband} to {newDeadband}")

    @property
    def deadband(self):
        return self.__deadband__

    @deadband.setter
    def deadband(self, deadband: Union[float, int]):
        if (deadband<=0):
            raise ValueError("Deadband must be a positive number greater than or equal to zero")
        self.__log_task_deadband_change__(self.__deadband__,deadband)
        self.__deadband__ = deadband
        # force re-execution with prior input, using a separate thread
        if(self.input):
            self.__pool__.submit(asyncio.run,self.execute(self.input)).result()

    async def __execute__(self, state:EventState, *args, **kwargs):
        # Case when args are passed these are used as input
        if(args):
            input=args[0] if not isinstance(args[0],EventState) else args[0].state
        else:
            input=state.state
        if(input is None or self.deadband is None):
            return (state,False)
        if(not(isinstance(input,float) or isinstance(input,int) or isinstance(input,bool) or isinstance(input,str))):
            raise ValueError("Input passed is not a number, bool or string")
        self.input=state # track previous input
        previousValue=self.__prev_value__
        newValue=None
        if(isinstance(input,bool) or isinstance(input,str) or self.deadband==0):
            if(input!=previousValue):
                newValue=input
        else:
            if(abs(previousValue-input)>self.deadband):
                newValue=input
        output_state = EventState()
        output_state.timestamp = datetime.now().timestamp()
        output_state.source = self.__persist_obj__  if self.__persist_obj__ else state
        if(newValue):  # Only trigger event if significant change, passing the new value as the state
            self.__prev_value___=newValue
            output_state.state=newValue
            await self.__trigger_event__(output_state)
        else:
            output_state.state=self.__prev_value__
        return output_state

class AsyncLimitCheckerTask(AsyncTaskWithEvent):

    def __init__(self,event_context:str,limit:Union[float,int,bool],comparator:CompareOperation,completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        super().__init__(event_context=event_context, completion_callback=completion_callback, timeout=timeout,
                         persist_obj=persist_obj)
        self.input=None # add tracking input state
        self.__limit__=None
        self.limit=limit
        self.__comparator__=comparator
        self.__limit_state__=LimitState.NORMAL

    def __log_task_limit_change__(self,limit:Union[float,int,bool],newLimit:Union[float,int,bool]):
        self.__logger__.info(f"Limit changed from {limit} to {newLimit}")

    # NOTE: Compararator is immutable
    # def __log_task_comparator_change__(self,comparator:CompareOperation,newComparator:CompareOperation):
    #     self.__logger__.info(f"Comparator changed from {comparator.name if comparator else None} to {newComparator.name}")

    @property
    def limit(self):
        return self.__limit__

    @limit.setter
    def limit(self,limit:Union[float,int,bool]):
        if(not(limit) or not(isinstance(limit,float) or isinstance(limit,int) or isinstance(limit,bool))):
            raise ValueError("Limit must be numeric or bool")
        self.__log_task_limit_change__(self.__limit__,limit)
        self.__limit__=limit
        # force re-execution with prior input, using a separate thread
        if(self.input):
            self.__pool__.submit(asyncio.run,self.execute(self.input)).result()

    @property
    def comparator(self):
        return self.__comparator__

    # NOTE: Compararator is immutable
    # @comparator.setter
    # def comparator(self, comparator:CompareOperation):
    #     if(not(comparator) or not(isinstance(comparator,CompareOperation))):
    #         raise ValueError("Comparator must be a valid Compare Operation")
    #     self.__log_task_comparator_change__(self.__comparator__,comparator)
    #     self.__comparator__=comparator
    #     # force re-execution with prior input, using a separate thread
    #     if(self.input):
    #         self.__pool__.submit(asyncio.run,self.execute(self.input)).result()

    @property
    def state(self):
        return {"state":self.__limit_state__,"limit":self.limit,"comparator":self.comparator}

    # overriden execute method
    async def __execute__(self, state:EventState, *args, **kwargs):
        # Case when args are passed these are used as input
        if(args):
            input=args[0] if not isinstance(args[0],EventState) else args[0].state
        else:
            input=state.state
        if(input is None or self.limit is None):
            return (state,False)
        if(not(isinstance(input,float) or isinstance(int,int) or isinstance(int,bool))):
            raise ValueError("Input passed is not a number or bool")
        self.input=state # track previous input
        current_state=self.__limit_state__
        limit_state=None
        if(isinstance(input,bool)):
            if(input==self.limit):
                limit_state=LimitState.EXCEEDED
        elif(self.comparator==CompareOperation.EQ):
            if(input==self.limit):
                limit_state=LimitState.EXCEEDED
        elif(self.comparator==CompareOperation.GT):
            if(input>self.limit):
                limit_state=LimitState.EXCEEDED
        elif(self.comparator==CompareOperation.GE):
            if(input>=self.limit):
                limit_state=LimitState.EXCEEDED
        elif(self.comparator==CompareOperation.LT):
            if(input<self.limit):
                limit_state=LimitState.EXCEEDED
        elif(self.comparator==CompareOperation.LE):
            if(input<=self.limit):
                limit_state=LimitState.EXCEEDED
        output_state = EventState()
        output_state.timestamp = datetime.now().timestamp()
        output_state.source = self.__persist_obj__  if self.__persist_obj__ else state
        if(limit_state and current_state!=limit_state):  # limit_state is none if in between alert and reset
            self.__limit_state__=limit_state
            ret = self.state
            ret['input'] = input
            output_state.state=ret
            await self.__trigger_event__(output_state)
        else:
            ret = self.state
            ret['input'] = input
            output_state.state = ret
        return output_state

class AsyncLimitCheckerWithResetTask(AsyncLimitCheckerTask):
    def __init__(self, event_context: str, limit: Union[float, int, bool], deadband: Union[float, int],
                 comparator: CompareOperation, completion_callback=None, timeout: float = None,
                 persist_obj: PersistentObject = None):
        super().__init__(event_context=event_context,limit=limit,comparator=comparator,completion_callback=completion_callback, timeout=timeout,
                         persist_obj=persist_obj)
        self.__deadband__=None
        self.deadband = deadband

    def __log_task_deadband_change__(self,deadband:Union[float,int],newDeadband:Union[float,int]):
        self.__logger__.info(f"Deadband changed from {deadband} to {newDeadband}")

    @property
    def deadband(self):
        return self.__deadband__

    @deadband.setter
    def deadband(self, deadband: Union[float, int]):
        if (not(isinstance(self.limit,bool) or self.comparator==CompareOperation.EQ) and deadband<=0):
            raise ValueError("Deadband must be a positive number greater than or equal to zero")
        self.__log_task_deadband_change__(self.__deadband__,deadband)
        self.__deadband__ = deadband
        # force re-execution with prior input, using a separate thread
        if(self.input):
            self.__pool__.submit(asyncio.run,self.execute(self.input)).result()

    @property
    def state(self):
        return {"state":self.__limit_state__,"limit":self.limit,"comparator":self.comparator,"deadband":self.deadband}

    # overriden execute method
    async def __execute__(self, state: EventState, *args, **kwargs):
        print("limit checker")
        # Case when args are passed these are used as input
        if(args):
            input=args[0] if not isinstance(args[0],EventState) else args[0].state
        else:
            input=state.state
        if(input is None or self.limit is None or self.deadband is None):
            return (state,False)
        if(not(isinstance(input,float) or isinstance(input,int) or isinstance(input,bool))):
            raise ValueError("Input passed is not a number or bool")
        self.input=state # track previous input
        current_state=self.__limit_state__
        limit_state=None
        if(isinstance(input,bool)):
            if(input==self.limit):
                limit_state=LimitState.EXCEEDED
        elif(self.comparator==CompareOperation.EQ):
            if(input==self.limit):
                limit_state=LimitState.EXCEEDED
        elif(self.comparator==CompareOperation.GT):
            if(input>self.limit):
                limit_state=LimitState.EXCEEDED
            elif(input<=(self.limit-self.deadband)):
                limit_state = LimitState.NORMAL
        elif(self.comparator==CompareOperation.GE):
            if(input>=self.limit):
                limit_state=LimitState.EXCEEDED
            elif(input<=(self.limit-self.deadband)):
                limit_state = LimitState.NORMAL
        elif(self.comparator==CompareOperation.LT):
            if(input<self.limit):
                limit_state=LimitState.EXCEEDED
            elif (input >= (self.limit + self.deadband)):
                limit_state = LimitState.NORMAL
        elif(self.comparator==CompareOperation.LE):
            if(input<=self.limit):
                limit_state=LimitState.EXCEEDED
            elif (input >= (self.limit + self.deadband)):
                limit_state = LimitState.NORMAL
        output_state = EventState()
        output_state.timestamp = datetime.now().timestamp()
        output_state.source = self.__persist_obj__  if self.__persist_obj__ else state
        if(limit_state and current_state!=limit_state):  # limit_state is none if in between alert and reset
            self.__limit_state__=limit_state
            ret = self.state
            ret['input'] = input
            output_state.state=ret
            await self.__trigger_event__(output_state)
        else:
            ret = self.state
            ret['input'] = input
            output_state.state = ret
        return output_state



