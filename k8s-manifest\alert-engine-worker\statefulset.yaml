apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: alert-engine-worker
  namespace: application
  labels:
    app: alert-engine-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: alert-engine-worker
  serviceName: alert-engine-worker
  template:
    metadata:
      labels:
        app: alert-engine-worker
      annotations:
        instrumentation.opentelemetry.io/inject-python: openobserve-collector/openobserve-python
    spec:
      volumes:
        - name: config-volume
          configMap:
            name: celery-config-${ENV}
      containers:
        - name: celery-worker-${ENV}
          image: ${ECR_REGISTRY}/${ECR_REPO_NAME}:${IMAGE_TAG}
          command:
            - celery
            - '-A'
            - app.celery_app
            - worker
            - '-c'
            - '2000'
            - '-E'
            - '--loglevel=info'
          ports:
            - containerPort: 5555
              protocol: TCP
          env:
            - name: sentinel
              value: 'false'   
          volumeMounts:
            - name: config-volume
              mountPath: /app/config.ini
              subPath: config.ini       
          resources:
            limits:
              cpu: 250m
              memory: 250Mi
            requests:
              cpu: 200m
              memory: 200Mi
     
