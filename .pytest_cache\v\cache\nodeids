["app/tests/UT-Bug1573-None-Agg-Error.py::test_create_dead_alert", "app/tests/UT-Bug1573-None-Agg-Error.py::test_create_nominal_alert_no_aggregation", "app/tests/UT-Bug1573-None-Agg-Error.py::test_create_nominal_alert_with_twa", "app/tests/UT-Bug1573-None-Agg-Error.py::test_create_stale_alert", "app/tests/UT-Bug1573-None-Agg-Error.py::test_invalid_aggregation", "app/tests/UT-Bug1573-None-Agg-Error.py::test_invalid_threshold_type", "app/tests/test_alert_creation.py::test_create_dead_alert", "app/tests/test_alert_creation.py::test_create_nominal_alert_no_aggregation", "app/tests/test_alert_creation.py::test_create_nominal_alert_with_twa", "app/tests/test_alert_creation.py::test_create_stale_alert", "app/tests/test_alert_creation.py::test_invalid_aggregation", "app/tests/test_alert_creation.py::test_invalid_threshold_type", "app/tests/test_bug_1573_alert_creation.py::test_create_dead_alert", "app/tests/test_bug_1573_alert_creation.py::test_create_nominal_alert_no_aggregation", "app/tests/test_bug_1573_alert_creation.py::test_create_nominal_alert_with_twa", "app/tests/test_bug_1573_alert_creation.py::test_create_stale_alert", "app/tests/test_bug_1573_alert_creation.py::test_invalid_aggregation", "app/tests/test_bug_1573_alert_creation.py::test_invalid_threshold_type", "tests/rabbit_mq/test_rabbitmq_service.py::test_send_rabbitmq_notification_publishes_message", "tests/rabbit_mq/test_rabbitmq_service.py::test_setup_rabbitmq_creates_connection", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_10m_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_12h_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_15m_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_1h_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_1m_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_20m_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_2h_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_2m_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_30m_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_4h_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_5m_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_6h_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_8h_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_daily_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_monthly_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_none_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_raw_period", "tests/test_aggregate_periods.py::TestAggregatePeriods::test_weekly_period", "tests/test_aggregates.py::TestAggregates::test_avg_aggregate", "tests/test_aggregates.py::TestAggregates::test_deltaavg_aggregate", "tests/test_aggregates.py::TestAggregates::test_deltamax_aggregate", "tests/test_aggregates.py::TestAggregates::test_deltamin_aggregate", "tests/test_aggregates.py::TestAggregates::test_deltastd_aggregate", "tests/test_aggregates.py::TestAggregates::test_deltatwa_aggregate", "tests/test_aggregates.py::TestAggregates::test_max_aggregate", "tests/test_aggregates.py::TestAggregates::test_min_aggregate", "tests/test_aggregates.py::TestAggregates::test_none_aggregate", "tests/test_aggregates.py::TestAggregates::test_ratetotal_aggregate", "tests/test_aggregates.py::TestAggregates::test_std_aggregate", "tests/test_aggregates.py::TestAggregates::test_total_aggregate", "tests/test_aggregates.py::TestAggregates::test_twa_aggregate", "tests/test_alert_integration.py::test_dead_alert_lifecycle", "tests/test_alert_integration.py::test_nominal_alert_lifecycle", "tests/test_alert_integration.py::test_stale_alert_lifecycle", "tests/test_alert_limit_field.py::test_dead_alert_functionality_with_none_limit", "tests/test_alert_limit_field.py::test_dead_alert_limit_field", "tests/test_alert_limit_field.py::test_stale_alert_functionality_with_none_limit", "tests/test_alert_limit_field.py::test_stale_alert_limit_field", "tests/test_alert_performance.py::TestAlertPerformance::test_concurrent_processing_simulation", "tests/test_alert_performance.py::TestAlertPerformance::test_dead_alert_processing_performance", "tests/test_alert_performance.py::TestAlertPerformance::test_memory_usage_stability", "tests/test_alert_performance.py::TestAlertPerformance::test_mixed_alert_types_performance", "tests/test_alert_performance.py::TestAlertPerformance::test_nominal_alert_processing_performance", "tests/test_alert_performance.py::TestAlertPerformance::test_stale_alert_processing_performance", "tests/test_db_service_insert_state_execution.py::test_insert_state_execution_url[None-None-False]", "tests/test_db_service_insert_state_execution.py::test_insert_state_execution_url[None-period_enum1-False]", "tests/test_db_service_insert_state_execution.py::test_insert_state_execution_url[agg_enum0-period_enum0-True]", "tests/test_db_service_insert_state_execution.py::test_insert_state_execution_url[agg_enum2-None-False]", "tests/test_db_service_insert_state_execution.py::test_insert_state_execution_url[agg_enum4-period_enum4-False]", "tests/test_db_service_insert_state_execution.py::test_insert_state_execution_url[agg_enum5-period_enum5-False]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_alert_api", "tests/test_dead_alert.py::TestDeadAlert::test_dead_alert_create", "tests/test_dead_alert.py::TestDeadAlert::test_dead_alert_creation", "tests/test_dead_alert.py::TestDeadAlert::test_dead_alert_creation_failures", "tests/test_dead_alert.py::TestDeadAlert::test_dead_edge_cases[test_scenario0]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_edge_cases[test_scenario1]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_edge_cases[test_scenario2]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_no_last_seen", "tests/test_dead_alert.py::TestDeadAlert::test_dead_recovery", "tests/test_dead_alert.py::TestDeadAlert::test_dead_state_transition", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[11-10-LimitState.DEAD]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[15-10-LimitState.DEAD]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[15-LimitState.DEAD]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[5-10-LimitState.NORMAL]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[5-LimitState.NORMAL]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[60-10-LimitState.DEAD]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[9-10-LimitState.NORMAL]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[test_case0]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[test_case1]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[test_case2]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[test_case3]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[test_case4]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_evaluation[15-LimitState.DEAD]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_evaluation[5-LimitState.NORMAL]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_evaluation[60-LimitState.DEAD]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_timestamp_handling", "tests/test_dead_alert.py::TestDeadAlert::test_dead_timestamp_validation[test_case0]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_timestamp_validation[test_case1]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_timestamp_validation[test_case2]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_timestamp_validation[test_case3]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_value_validation[test_case0]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_value_validation[test_case1]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_value_validation[test_case2]", "tests/test_dead_alert.py::TestDeadAlert::test_dead_value_validation[test_case3]", "tests/test_dead_alert.py::TestDeadAlerts::test_basic_dead_detection", "tests/test_dead_alert.py::TestDeadAlerts::test_edge_cases", "tests/test_dead_alert.py::TestDeadAlerts::test_state_transitions", "tests/test_dead_alert.py::TestDeadAlerts::test_timestamp_tracking", "tests/test_dead_alert.py::test_dead_alert_api", "tests/test_dead_alert.py::test_dead_alert_input_values", "tests/test_dead_alert.py::test_dead_alert_scenarios", "tests/test_dead_alert.py::test_dead_measurement_detection[0-60-DEAD-Current measurement should be DEAD (within threshold)]", "tests/test_dead_alert.py::test_dead_measurement_detection[0-60000-NORMAL-Current measurement should be NORMAL (within threshold)]", "tests/test_dead_alert.py::test_dead_measurement_detection[2001-60-DEAD-Current measurement should be DEAD due to < threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2001-60-DEAD-Current measurement should be dead (< threshold)]", "tests/test_dead_alert.py::test_dead_measurement_detection[2001-60-NORMAL-Current measurement (0s old) should be NORMAL with 60s threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2001-60-NORMAL-Current measurement should be NORMAL]", "tests/test_dead_alert.py::test_dead_measurement_detection[2001-dead_duration0-DEAD-Current measurement (0s old) should be DEAD with 60s threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2001-dead_duration0-NORMAL-Current measurement (0s old) should be NORMAL with 60s threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2002-60-DEAD-5 min old measurement should be DEAD when threshold is 1 min]", "tests/test_dead_alert.py::test_dead_measurement_detection[2002-60-DEAD-5 min old measurement should be DEAD with 1 min threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2002-60-NORMAL-5 min old measurement should be NORMAL when threshold is 1 min]", "tests/test_dead_alert.py::test_dead_measurement_detection[2002-60-NORMAL-5 min old measurement should be normal (> threshold)]", "tests/test_dead_alert.py::test_dead_measurement_detection[2002-600-DEAD-5 min old measurement should be DEAD when threshold is 10 min]", "tests/test_dead_alert.py::test_dead_measurement_detection[2002-600-DEAD-5 min old measurement should be dead with 10 min threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2002-600-NORMAL-5 min old measurement should be NORMAL when threshold is 10 min]", "tests/test_dead_alert.py::test_dead_measurement_detection[2002-600-NORMAL-5 min old measurement should be NORMAL with 10 min threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2002-dead_duration1-DEAD-5 min old measurement should be DEAD with 1 min threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2002-dead_duration1-NORMAL-5 min old measurement should be NORMAL with 1 min threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2002-dead_duration2-DEAD-5 min old measurement should be DEAD with 10 min threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2002-dead_duration2-NORMAL-5 min old measurement should be NORMAL with 10 min threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2003-300-DEAD-10 min old measurement should be DEAD when threshold is 5 min]", "tests/test_dead_alert.py::test_dead_measurement_detection[2003-300-DEAD-10 min old measurement should be DEAD with 5 min threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2003-300-NORMAL-10 min old measurement should be NORMAL when threshold is 5 min]", "tests/test_dead_alert.py::test_dead_measurement_detection[2003-300-NORMAL-10 min old measurement should be normal with 5 min threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2003-dead_duration3-DEAD-10 min old measurement should be DEAD with 5 min threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2003-dead_duration3-NORMAL-10 min old measurement should be NORMAL with 5 min threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2004-60-DEAD-Never seen measurement should be DEAD]", "tests/test_dead_alert.py::test_dead_measurement_detection[2004-60-DEAD-Never seen measurement should be dead]", "tests/test_dead_alert.py::test_dead_measurement_detection[2004-dead_duration4-DEAD-Never seen measurement should be DEAD]", "tests/test_dead_alert.py::test_dead_measurement_detection[2005-60-DEAD-Recent measurement should be dead (< threshold)]", "tests/test_dead_alert.py::test_dead_measurement_detection[2005-60-DEAD-Recently seen measurement should be DEAD due to < threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2005-60-NORMAL-Recent measurement (30s old) should be NORMAL with 60s threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2005-60-NORMAL-Recently seen measurement should be NORMAL]", "tests/test_dead_alert.py::test_dead_measurement_detection[2005-dead_duration5-DEAD-Recent measurement (30s old) should be DEAD with 60s threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[2005-dead_duration5-NORMAL-Recent measurement (30s old) should be NORMAL with 60s threshold]", "tests/test_dead_alert.py::test_dead_measurement_detection[30-60-DEAD-Recent measurement should be DEAD (within threshold)]", "tests/test_dead_alert.py::test_dead_measurement_detection[300-60-NORMAL-5 min old measurement should be NORMAL (beyond threshold)]", "tests/test_dead_alert.py::test_dead_measurement_detection[300-600-DEAD-5 min old measurement should be DEAD (within 10 min threshold)]", "tests/test_dead_alert.py::test_dead_measurement_detection[30000-60000-NORMAL-Recent measurement should be NORMAL (within threshold)]", "tests/test_dead_alert.py::test_dead_measurement_detection[300000-60000-DEAD-5 min old measurement should be DEAD (beyond threshold)]", "tests/test_dead_alert.py::test_dead_measurement_detection[300000-600000-NORMAL-5 min old measurement should be NORMAL (within 10 min threshold)]", "tests/test_dead_alert.py::test_dead_measurement_detection[600-300-NORMAL-10 min old measurement should be NORMAL (beyond 5 min threshold)]", "tests/test_dead_alert.py::test_dead_measurement_detection[600000-300000-DEAD-10 min old measurement should be DEAD (beyond 5 min threshold)]", "tests/test_dead_alert.py::test_dead_measurement_detection[None-60-DEAD-Never seen measurement should be DEAD]", "tests/test_dead_alert.py::test_dead_measurement_detection[None-60000-DEAD-Never seen measurement should be DEAD]", "tests/test_dead_alert.py::test_edge_cases", "tests/test_dead_alert.py::test_empty_values_scenario", "tests/test_dead_alert.py::test_multiple_measurements", "tests/test_dead_alert.py::test_none_last_seen", "tests/test_dead_alert.py::test_state_transitions", "tests/test_dead_alert_sequence.py::test_dead_alert_custom", "tests/test_dead_alert_sequence.py::test_dead_alert_sequence", "tests/test_dead_alert_state_events_data.py::TestDeadAlertStateTransitions::test_dead_alert_events_with_measurement_data", "tests/test_dead_alert_state_events_data.py::TestDeadAlertStateTransitions::test_dead_alert_state_transitions_with_events", "tests/test_dead_alert_state_events_data.py::TestDeadAlertStateTransitions::test_dead_alert_with_longer_periods", "tests/test_dead_alert_state_transitions.py::TestDeadAlertStateTransitions::test_dead_alert_events_with_measurement_data", "tests/test_dead_alert_state_transitions.py::TestDeadAlertStateTransitions::test_dead_alert_state_transitions", "tests/test_dead_alert_state_transitions.py::TestDeadAlertStateTransitions::test_dead_alert_state_transitions_with_events", "tests/test_dead_alert_state_transitions.py::TestDeadAlertStateTransitions::test_dead_alert_with_longer_periods", "tests/test_dead_alert_state_transitions.py::test_dead_alert_data_recovery", "tests/test_dead_alert_state_transitions.py::test_millisecond_precision", "tests/test_dead_alert_state_transitions.py::test_threshold_boundary_conditions", "tests/test_dead_alert_timestamp.py::test_measurement_sequence", "tests/test_dead_alert_timestamp.py::test_timestamp_handling", "tests/test_dead_alert_value.py::test_dead_alert_state_transitions_with_values", "tests/test_dead_alert_value.py::test_dead_alert_value_handling", "tests/test_dead_alert_values.py::test_dead_alert_values[0-120-60-NORMAL-0-NORMAL state should show 0 when current value is 0]", "tests/test_dead_alert_values.py::test_dead_alert_values[0-30-60-DEAD-0-DEAD state should show 0 when current value is 0]", "tests/test_dead_alert_values.py::test_dead_alert_values[100.5-120-60-NORMAL-100.5-NORMAL state should show actual current value]", "tests/test_dead_alert_values.py::test_dead_alert_values[100.5-30-60-DEAD-0-DEAD state should show 0 even with non-zero current value]", "tests/test_dead_alert_values.py::test_dead_alert_values[None-120-60-NORMAL-0-NORMAL state should show 0 when no current value]", "tests/test_dead_alert_values.py::test_dead_alert_values[None-30-60-DEAD-0-DEAD state should show 0 when no current value]", "tests/test_dead_alert_values.py::test_missing_data_value", "tests/test_dead_alert_values.py::test_state_transition_value_changes", "tests/test_dead_e2e.py::test_evaluate_alert_task_dead_state_transition", "tests/test_enums.py::test_aggregate_imports", "tests/test_enums.py::test_aggregate_period_alignment", "tests/test_enums.py::test_aggregate_period_enum", "tests/test_enums.py::test_bucket_size_dict_compatibility", "tests/test_enums.py::test_bucket_size_imports", "tests/test_enums.py::test_compare_operation_enum", "tests/test_enums.py::test_comparison_enum", "tests/test_enums.py::test_limit_setting_enum", "tests/test_enums.py::test_limit_state_enum", "tests/test_enums.py::test_threshold_type_enum", "tests/test_event_id_none_handling.py::test_evaluate_alert_task_async_event_id_none", "tests/test_event_id_none_handling.py::test_send_rabbitmq_notification_event_id_none", "tests/test_event_id_none_handling.py::test_send_rabbitmq_notification_event_id_present", "tests/test_event_id_none_handling.py::test_send_rabbitmq_notification_returns_false_on_event_id_none", "tests/test_event_id_none_handling.py::test_send_rabbitmq_notification_returns_true_on_success", "tests/test_event_id_none_handling.py::test_send_test_alert_endpoint_returns_alert_sent", "tests/test_event_id_none_handling.py::test_send_test_alert_endpoint_returns_notification_not_sent", "tests/test_insert_state_execution.py::test_insert_state_execution_dead", "tests/test_insert_state_execution.py::test_insert_state_execution_dead_uses_event_timestamp", "tests/test_insert_state_execution.py::test_insert_state_execution_exceeded", "tests/test_insert_state_execution.py::test_insert_state_execution_no_alert", "tests/test_insert_state_execution.py::test_insert_state_execution_no_data", "tests/test_insert_state_execution.py::test_insert_state_execution_no_event", "tests/test_insert_state_execution.py::test_insert_state_execution_stale", "tests/test_mark_alerts_deleted.py::test_mark_alerts_deleted_exception", "tests/test_mark_alerts_deleted.py::test_mark_alerts_deleted_success", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_alert_creation", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_alert_creation_failures", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_comparisons[CompareOperation.EQ-50.0-50.0-LimitState.EXCEEDED]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_comparisons[CompareOperation.GE-50.0-50.0-LimitState.EXCEEDED]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_comparisons[CompareOperation.GT-50.0-55.0-LimitState.EXCEEDED]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_comparisons[CompareOperation.LE-50.0-50.0-LimitState.EXCEEDED]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_comparisons[CompareOperation.LT-50.0-45.0-LimitState.EXCEEDED]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_comparisons[test_case0]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_comparisons[test_case1]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_comparisons[test_case2]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_comparisons[test_case3]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_comparisons[test_case4]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_deadband_behavior", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_deadband_validation[test_case0]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_deadband_validation[test_case1]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_deadband_validation[test_case2]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_deadband_validation[test_case3]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_invalid_combinations[test_case0]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_invalid_combinations[test_case1]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_invalid_combinations[test_case2]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_invalid_combinations[test_case3]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_invalid_inputs", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[50.0-45.0-1.0-LimitState.NORMAL]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[50.0-48.5-1.0-LimitState.NORMAL]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[50.0-49.5-1.0-LimitState.EXCEEDED]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[50.0-50.0-1.0-LimitState.EXCEEDED]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[50.0-50.0-1.0-LimitState.NORMAL]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[50.0-50.5-1.0-LimitState.EXCEEDED]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[50.0-51.5-1.0-LimitState.EXCEEDED]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[50.0-55.0-1.0-LimitState.EXCEEDED]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[test_case0]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[test_case1]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[test_case2]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[test_case3]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[test_case4]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[test_case5]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[test_case6]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_create", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_evaluation[45.0-50.0-LimitState.NORMAL]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_evaluation[50.0-50.0-LimitState.NORMAL]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_evaluation[55.0-50.0-LimitState.EXCEEDED]", "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_with_deadband", "tests/test_nominal_alert.py::TestNominalAlerts::test_basic_threshold_checks", "tests/test_nominal_alert.py::TestNominalAlerts::test_comparison_operations", "tests/test_nominal_alert.py::TestNominalAlerts::test_deadband_handling", "tests/test_nominal_alert.py::TestNominalAlerts::test_numeric_ranges", "tests/test_nominal_alert.py::TestNominalAlerts::test_state_transitions", "tests/test_nominal_alert.py::TestNominalAlerts::test_timestamp_tracking", "tests/test_nominal_alert.py::test_absolute_value_comparison", "tests/test_nominal_alert.py::test_comparison_operations", "tests/test_nominal_alert.py::test_data_persistence", "tests/test_nominal_alert.py::test_deadband", "tests/test_nominal_alert.py::test_invalid_key", "tests/test_nominal_alert.py::test_nominal_alert_api", "tests/test_nominal_alert.py::test_nominal_alert_range_query", "tests/test_nominal_alert.py::test_nominal_alert_scenarios", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1001-10.0-NORMAL-Normal positive value within threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1001-10.0-NORMAL-Value below threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1001-10.0-True-Normal positive value within threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1002-10.0-EXCEEDED-Above threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1002-10.0-EXCEEDED-Value beyond positive threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1002-10.0-EXCEEDED-Value well beyond positive threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1002-10.0-False-Value well beyond positive threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1003-10.0-NORMAL-Negative value below threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1003-10.0-NORMAL-Normal negative value within threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1003-10.0-True-Normal negative value within threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1004--20.0-EXCEEDED-Below negative threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1004-10.0-EXCEEDED-Value beyond negative threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1004-10.0-EXCEEDED-Value well beyond negative threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1004-10.0-False-Value well beyond negative threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1005-10.0-NORMAL-Zero is always nominal]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1005-10.0-NORMAL-Zero value within threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1005-10.0-NORMAL-Zero value]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1005-10.0-True-Zero is always nominal]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1006-10.0-NORMAL-Just below threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1006-10.0-NORMAL-Value just under threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1006-10.0-True-Value just under threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1007-10.0-EXCEEDED-Just above threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1007-10.0-EXCEEDED-Value just over threshold]", "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1007-10.0-False-Value just over threshold]", "tests/test_nominal_alert.py::test_nominal_alerts[-15.0--10.0-EXCEEDED-Below negative threshold]", "tests/test_nominal_alert.py::test_nominal_alerts[-15.0-10.0-EXCEEDED-Negative value above absolute threshold]", "tests/test_nominal_alert.py::test_nominal_alerts[-15.0-10.0-NORMAL-Negative value within positive threshold]", "tests/test_nominal_alert.py::test_nominal_alerts[-5.0-10.0-NORMAL-Negative value below absolute threshold]", "tests/test_nominal_alert.py::test_nominal_alerts[-5.0-10.0-NORMAL-Negative value below threshold]", "tests/test_nominal_alert.py::test_nominal_alerts[-5.0-10.0-NORMAL-Negative value within absolute threshold]", "tests/test_nominal_alert.py::test_nominal_alerts[0.0-10.0-NORMAL-Zero value]", "tests/test_nominal_alert.py::test_nominal_alerts[10.001-10.0-EXCEEDED-Just above threshold]", "tests/test_nominal_alert.py::test_nominal_alerts[15.0-10.0-EXCEEDED-Above threshold]", "tests/test_nominal_alert.py::test_nominal_alerts[5.0-10.0-NORMAL-Value below threshold]", "tests/test_nominal_alert.py::test_nominal_alerts[9.999-10.0-NORMAL-Just below threshold]", "tests/test_nominal_alert.py::test_numeric_range", "tests/test_nominal_alert.py::test_sequential_measurements", "tests/test_nominal_alert.py::test_state_transitions", "tests/test_nominal_alert_flow.py::TestNominalAlerts::test_nominal_alert_deadband", "tests/test_nominal_alert_flow.py::TestNominalAlerts::test_nominal_alert_exceeded_state", "tests/test_nominal_alert_flow.py::TestNominalAlerts::test_nominal_alert_no_data", "tests/test_nominal_alert_flow.py::TestNominalAlerts::test_nominal_alert_return_to_normal", "tests/test_nominal_alert_flow.py::test_nominal_alert_deadband", "tests/test_nominal_alert_flow.py::test_nominal_alert_exceeded_state", "tests/test_nominal_alert_flow.py::test_nominal_alert_no_data", "tests/test_nominal_alert_flow.py::test_nominal_alert_return_to_normal", "tests/test_nominal_alert_sequence.py::test_nominal_alert_sequence", "tests/test_nominal_e2e.py::test_evaluate_alert_task_nominal_state_transition", "tests/test_none_aggregate_fix.py::TestNoneAggregateFix::test_check_limit_with_none_aggregate", "tests/test_none_aggregate_fix.py::TestNoneAggregateFix::test_get_measurements_and_alerts_new_with_none_aggregate", "tests/test_none_aggregate_fix.py::TestNoneAggregateFix::test_read_data_with_none_aggregate", "tests/test_rabbitmq_notification.py::test_send_rabbitmq_notification_event_id_none", "tests/test_rabbitmq_notification.py::test_send_rabbitmq_notification_event_id_present", "tests/test_rabbitmq_service.py::test_logging_on_connection_and_publish", "tests/test_rabbitmq_service.py::test_send_rabbitmq_notification_event_id_none", "tests/test_rabbitmq_service.py::test_send_rabbitmq_notification_general_exception", "tests/test_rabbitmq_service.py::test_send_rabbitmq_notification_stream_lost_error", "tests/test_rabbitmq_service.py::test_send_rabbitmq_notification_success", "tests/test_rabbitmq_service.py::test_thread_safety_on_get_channel", "tests/test_stale_alert.py::TestStaleAlert::test_stale_alert_create", "tests/test_stale_alert.py::TestStaleAlert::test_stale_alert_creation", "tests/test_stale_alert.py::TestStaleAlert::test_stale_alert_creation_failures", "tests/test_stale_alert.py::TestStaleAlert::test_stale_edge_cases[test_scenario0]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_edge_cases[test_scenario1]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_edge_cases[test_scenario2]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_no_data", "tests/test_stale_alert.py::TestStaleAlert::test_stale_redis_failures[test_case0]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_redis_failures[test_case1]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_redis_failures[test_case2]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_redis_interactions", "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_check[15-30-LimitState.NORMAL]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_check[29-30-LimitState.NORMAL]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_check[31-30-LimitState.STALE]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_check[35-30-LimitState.STALE]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_check[60-30-LimitState.STALE]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_check[test_case0]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_check[test_case1]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_check[test_case2]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_check[test_case3]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_check[test_case4]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_evaluation[15-LimitState.NORMAL]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_evaluation[35-LimitState.STALE]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_evaluation[60-LimitState.STALE]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_timestamp_validation[test_case0]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_timestamp_validation[test_case1]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_timestamp_validation[test_case2]", "tests/test_stale_alert.py::TestStaleAlert::test_stale_value_change_reset", "tests/test_stale_alert.py::TestStaleAlert::test_stale_value_changes", "tests/test_stale_alert.py::TestStaleAlerts::test_basic_stale_detection", "tests/test_stale_alert.py::TestStaleAlerts::test_edge_cases", "tests/test_stale_alert.py::TestStaleAlerts::test_state_transitions", "tests/test_stale_alert.py::TestStaleAlerts::test_timestamp_tracking", "tests/test_stale_alert.py::test_comparison_operations", "tests/test_stale_alert.py::test_edge_cases", "tests/test_stale_alert.py::test_stale_measurement_detection[0-10.0-10.0-15-CompareOperation.GT-NORMAL-Current measurement should be NORMAL]", "tests/test_stale_alert.py::test_stale_measurement_detection[0-10.0-15-CompareOperation.GT-NORMAL-Current measurement should be NORMAL]", "tests/test_stale_alert.py::test_stale_measurement_detection[1800000-10.0-15-CompareOperation.GT-STALE-30 min old unchanged value should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[1800000-10.0-30-CompareOperation.GE-STALE-Exactly at threshold with GE should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[30-5.0-5.0-15-CompareOperation.GT-STALE-30 min old unchanged value should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[30-5.0-5.0-30-CompareOperation.GE-STALE-Exactly at threshold with GE should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[300000-10.0-15-CompareOperation.GT-NORMAL-Recent measurement should be NORMAL]", "tests/test_stale_alert.py::test_stale_measurement_detection[300000-10.0-5-CompareOperation.GE-STALE-Just at threshold with GE should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[3001-15-CompareOperation.GT-NORMAL-Current measurement should be NORMAL]", "tests/test_stale_alert.py::test_stale_measurement_detection[3002-15-CompareOperation.GT-STALE-30 min old unchanged value should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[3002-30-CompareOperation.GE-STALE-Exactly at threshold with GE should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[3003-15-CompareOperation.GT-STALE-Never seen measurement should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[3004-15-CompareOperation.GT-NORMAL-Recent measurement should be NORMAL]", "tests/test_stale_alert.py::test_stale_measurement_detection[3004-5-CompareOperation.GE-STALE-Just at threshold with GE should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[3005-30-CompareOperation.GT-STALE-Old measurement should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[3005-30-CompareOperation.GT-STALE-Old unchanged measurement should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[3600000-10.0-30-CompareOperation.GT-STALE-Old unchanged measurement should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[5-7.0-7.0-15-CompareOperation.GT-NORMAL-Recent measurement should be NORMAL]", "tests/test_stale_alert.py::test_stale_measurement_detection[5-7.0-7.0-5-CompareOperation.GE-STALE-Just at threshold with GE should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[60-7.0-7.0-30-CompareOperation.GT-STALE-Old unchanged measurement should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[None-None-15-CompareOperation.GT-STALE-Never seen measurement should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[None-None-None-15-CompareOperation.GT-STALE-Never seen measurement should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff0-10.0-15-CompareOperation.GT-NORMAL-Current measurement should be NORMAL]", "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff1-10.0-15-CompareOperation.GT-STALE-30 min old unchanged value should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff1-5.0-15-CompareOperation.GT-STALE-30 min old unchanged value should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff3-10.0-15-CompareOperation.GT-NORMAL-Recent measurement should be NORMAL]", "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff3-7.0-15-CompareOperation.GT-NORMAL-Recent measurement should be NORMAL]", "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff4-10.0-30-CompareOperation.GT-STALE-Old unchanged measurement should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff4-7.0-30-CompareOperation.GT-STALE-Old unchanged measurement should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff5-10.0-30-CompareOperation.GE-STALE-Exactly at threshold with GE should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff5-5.0-30-CompareOperation.GE-STALE-Exactly at threshold with GE should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff6-10.0-5-CompareOperation.GE-STALE-Just at threshold with GE should be STALE]", "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff6-7.0-5-CompareOperation.GE-STALE-Just at threshold with GE should be STALE]", "tests/test_stale_alert.py::test_value_change_resets_stale", "tests/test_stale_alert_sequence.py::test_stale_alert_sequence", "tests/test_stale_alert_with_band.py::test_mixed_stale_conditions", "tests/test_stale_alert_with_band.py::test_stale_alert_with_band", "tests/test_stale_alert_with_band.py::test_stale_alert_without_band", "tests/test_stale_band_alert.py::test_redis_storage", "tests/test_stale_band_alert.py::test_stale_band_basic_functionality", "tests/test_stale_band_alert.py::test_stale_band_edge_cases", "tests/test_stale_band_alert.py::test_stale_band_with_different_sizes", "tests/test_stale_e2e.py::test_evaluate_alert_task_stale_state_transition", "tests/test_stale_logic_paths.py::test_band_based_stale_logic", "tests/test_stale_logic_paths.py::test_both_comparison_operators", "tests/test_stale_logic_paths.py::test_comparison_operator_handling", "tests/test_stale_logic_paths.py::test_complete_stale_workflow", "tests/test_stale_logic_paths.py::test_original_stale_logic", "tests/test_stale_logic_paths.py::test_state_transitions", "tests/test_tasks_logging_and_anomaly.py::test_anomaly_threshold_type_skipped", "tests/test_tasks_logging_and_anomaly.py::test_no_error_for_missing_comparison_enum_on_anomaly", "tests/test_tasks_logging_and_anomaly.py::test_warning_logged_for_missing_measurement_data", "tests/test_timestamp_tracking.py::TestTimestampTracking::test_last_processed_logic", "tests/test_timestamp_tracking.py::TestTimestampTracking::test_timestamp_order_validation", "tests/test_timestamp_tracking.py::TestTimestampTracking::test_timestamp_race_conditions", "tests/test_timestamp_tracking.py::TestTimestampTracking::test_timestamp_update_verification", "tests/test_tsdb_reader_service.py::test_read_data_new_all_aggregates[avg]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_aggregates[deltaavg]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_aggregates[deltamax]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_aggregates[deltamin]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_aggregates[deltastd]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_aggregates[deltatwa]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_aggregates[max]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_aggregates[min]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_aggregates[ratetotal]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_aggregates[std.p]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_aggregates[total]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_aggregates[twa]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[10min]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[12hr]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[15min]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[1hr]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[1min]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[20min]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[2hr]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[2min]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[30min]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[4hr]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[5min]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[6hr]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[8hr]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[DAILY]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[MONTHLY]", "tests/test_tsdb_reader_service.py::test_read_data_new_all_periods[WEEKLY]", "tests/test_tsdb_reader_service.py::test_read_data_new_makes_correct_api_call", "tests/test_tsdb_reader_service.py::test_read_data_new_max_1h_aggregate", "tests/test_tsdb_reader_service.py::test_read_data_new_none_aggregate_history", "tests/test_tsdb_reader_service.py::test_read_data_new_raw_period_history", "tests/test_window_evaluation.py::TestWindowEvaluation::test_multiple_points_in_window", "tests/test_window_evaluation.py::TestWindowEvaluation::test_window_boundary_conditions", "tests/test_window_evaluation.py::TestWindowEvaluation::test_window_overlap_handling"]