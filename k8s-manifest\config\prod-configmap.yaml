apiVersion: v1
kind: ConfigMap
metadata:
  name: celery-config-prod
  namespace: application
  labels:
    environment: prod
data:
  config.ini: |
    [celery]
    redis_password=test@123
    redis_sentinel_master=mymaster
    broker_host=redis.redis.svc.cluster.local
    broker_port=6379
    broker_db=0
    backend_host=redis.redis.svc.cluster.local
    backend_port=6379
    backend_db=0
    retry_enabled = False
    retry_max_retries = 5
    retry_countdown = 60

    [api]
    api_host = http://timeseries-api-service.application.svc.cluster.local/api/v1_0
    csrf_token = Lm6Kbx6xXhlnraTe0LpAgxjg0VWp1Od9SGIsJmPZZXc=
    cookie = BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImRldiIsInN1YiI6Niwicm9sZXMiOnsiQURNSU4iOlsxMDksMTA4LDExNiw4LDExOSwxMjAsMTE1LDEwNiwxMDcsMTIxLDg2LDExNCwxMjIsODUsODQsMTE3LDExMCwxMjQsMTIzLDExOCwxMjUsMTExLDksODJdLCJVU0VSIjpbMTA5LDEwOCwxMTYsOCwxMTksMTIwLDExNSwxMDYsMTA3LDEyMSw4NiwxMTQsMTIyLDg1LDg0LDExNywxMTAsMTI0LDEyMywxMTgsMTI1LDExMSw5LDgyXSwiUE9XRVJfVVNFUiI6WzEwOSwxMDgsMTE2LDgsMTE5LDEyMCwxMTUsMTA2LDEwNywxMjEsODYsMTE0LDEyMiw4NSw4NCwxMTcsMTEwLDEyNCwxMjMsMTE4LDEyNSwxMTEsOSw4Ml19LCJpYXQiOjE3MjQxNjE4MDYsImV4cCI6MTc1NTY5NzgwNn0.roR6lVxwnMowbW_V4wwben-PD90HhjfIdV6-aB3cbZo; BE-CSRFToken=Lm6Kbx6xXhlnraTe0LpAgxjg0VWp1Od9SGIsJmPZZXc=

    [database]
    url = *****************************************************************************************************************
    db_name=dataloggeraws
    db_host=dataloggeraws.clclbj3j3ehf.us-east-1.rds.amazonaws.com
    db_port=5432
    db_user=eks_rw 
    db_password=QnJvbXB0b25HcnAyMDI0Cg==
    db_ssl=true

    [kafka]
    broker=strimzi-cluster-kafka-brokers.kafka-strimzi.svc.cluster.local:9092
    alert_topic=be-alert-dev-new

    [jwt]
    jwt_algorithm = HS256
    jwt_secret = this is a very secret secret


