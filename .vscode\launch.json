{"version": "0.2.0", "configurations": [{"name": "main", "type": "debugpy", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["app.main:app", "--reload"], "jinja": true}, {"name": "worker", "type": "python", "request": "launch", "module": "celery", "console": "integratedTerminal", "args": ["-A", "app.celery_app", "worker", "-E", "--loglevel=INFO"], "cwd": "${workspaceFolder}", "justMyCode": true}]}