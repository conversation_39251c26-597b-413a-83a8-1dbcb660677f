import pytest
from datetime import datetime
from app.LimitCheckerService import check_limit
from app.enums import CompareOperation, LimitState

# Test data with expected values
TEST_VALUES = [
    (5.0,     10.0,  "NORMAL",   "Value below threshold"),
    (-5.0,    10.0,  "NORMAL",   "Negative value within absolute threshold"),
    (0.0,     10.0,  "NORMAL",   "Zero value"),
    (9.999,   10.0,  "NORMAL",   "Just below threshold"),
    (10.001,  10.0,  "EXCEEDED", "Just above threshold"),
    (15.0,    10.0,  "EXCEEDED", "Above threshold"),
    # Testing negative values against positive threshold
    (-15.0,   10.0,  "NORMAL",   "Negative value within positive threshold"),
]

@pytest.mark.parametrize("value, threshold, expected_state, description", TEST_VALUES)
def test_nominal_alerts(value, threshold, expected_state, description):
    """Test nominal alert detection with various values"""
    result = check_limit(
        alert_id=1000,
        measurement_id=1001,
        limit=threshold,
        deadband=0.0,
        comparator=CompareOperation.GT,
        timestamp=datetime.now(),
        input_value=value,
        aggregate="avg",
        period="1m",
        asset_id=1
    )
    
    state = result.state or "NORMAL"  # Default to NORMAL if None
    assert state == expected_state, \
        f"{description}: value={value}, threshold={threshold}, expected={expected_state}"

def test_state_transitions():
    """Test state transitions between NORMAL and EXCEEDED"""
    threshold = 10.0
    transitions = [
        (5.0,  "NORMAL",   "Initial normal state"),
        (15.0, "EXCEEDED", "Transition to exceeded state"),
        (8.0,  "NORMAL",   "Return to normal state"),
        (12.0, "EXCEEDED", "Back to exceeded state"),
        (0.0,  "NORMAL",   "Return to normal with zero")
    ]
    
    for value, expected_state, description in transitions:
        result = check_limit(
            alert_id=1100,
            measurement_id=1002,
            limit=threshold,
            deadband=0.0,
            comparator=CompareOperation.GT,
            timestamp=datetime.now(),
            input_value=value,
            aggregate="avg",
            period="1m",
            asset_id=1
        )
        
        state = result.state or "NORMAL"
        assert state == expected_state, \
            f"{description}: value={value}, got={state}, expected={expected_state}"

def test_comparison_operations():
    """Test different comparison operations"""
    value = 10.0
    test_cases = [
        (CompareOperation.EQ, 10.0, "EXCEEDED", "Equal values"),
        (CompareOperation.GT, 9.0,  "EXCEEDED", "Greater than"),
        (CompareOperation.GE, 10.0, "EXCEEDED", "Greater or equal"),
        (CompareOperation.LT, 11.0, "EXCEEDED", "Less than"),
        (CompareOperation.LE, 10.0, "EXCEEDED", "Less or equal")
    ]
    
    for comparator, threshold, expected_state, description in test_cases:
        result = check_limit(
            alert_id=1200,
            measurement_id=1003,
            limit=threshold,
            deadband=0.0,
            comparator=comparator,
            timestamp=datetime.now(),
            input_value=value,
            aggregate="avg",
            period="1m",
            asset_id=1
        )
        
        state = result.state or "NORMAL"
        assert state == expected_state, \
            f"{description}: value={value}, threshold={threshold}, comparator={comparator.name}, got={state}, expected={expected_state}"

def test_deadband():
    """Test deadband functionality"""
    threshold = 10.0
    deadband = 2.0
    test_cases = [
        (8.0,  "NORMAL",   "Well within normal range"),
        (12.0, "EXCEEDED", "Well beyond threshold"),
        (9.5,  "NORMAL",   "Within deadband below threshold"),
        (10.5, "EXCEEDED", "Within deadband above threshold")
    ]
    
    for value, expected_state, description in test_cases:
        result = check_limit(
            alert_id=1300,
            measurement_id=1004,
            limit=threshold,
            deadband=deadband,
            comparator=CompareOperation.GT,
            timestamp=datetime.now(),
            input_value=value,
            aggregate="avg",
            period="1m",
            asset_id=1
        )
        
        state = result.state or "NORMAL"
        assert state == expected_state, \
            f"{description}: value={value}, threshold={threshold}, deadband={deadband}, got={state}, expected={expected_state}"

def test_numeric_range():
    """Test handling of different numeric ranges"""
    test_cases = [
        (0.0001,  0.001, "NORMAL",   "Small positive values"),
        (0.002,   0.001, "EXCEEDED", "Small positive values exceeded"),
        (-0.0001, 0.001, "NORMAL",   "Small negative values"),
        (-0.002,  0.001, "NORMAL",   "Small negative values within threshold"),
        (1000.0,  100.0, "EXCEEDED", "Large positive values"),
        (-1000.0, 100.0, "NORMAL",   "Large negative values within positive threshold")
    ]
    
    for value, threshold, expected_state, description in test_cases:
        result = check_limit(
            alert_id=1400,
            measurement_id=1005,
            limit=threshold,
            deadband=0.0,
            comparator=CompareOperation.GT,
            timestamp=datetime.now(),
            input_value=value,
            aggregate="avg",
            period="1m",
            asset_id=1
        )
        
        state = result.state or "NORMAL"
        assert state == expected_state, \
            f"{description}: value={value}, threshold={threshold}, expected={expected_state}"