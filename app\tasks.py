from asyncio import TimeoutError
from json import JSONDecodeError, loads as json_loads
import logging
from sys import platform
from collections import defaultdict
from contextlib import contextmanager
from typing import List, Dict, Tuple

from aiohttp import ClientError
from pydantic import BaseModel
from redis.exceptions import LockError

from .LimitCheckerService import check_limit
from .TSDBReaderService import read_data_new
from .celery_app import app as celery_app
from .config import config
from .db.db_service import update_alert_state, insert_event, fetch_alert_state, fetch_alerts_by_ids, insert_state_execution
from .db.models import Alerts
from .enums import Aggregate, AggregatePeriod, ThresholdType, Comparison, CompareOperation, \
    LimitState
from .models import EvaluateAlertTask
from .redis import master
from .rabbitmq.rabbitmq_service import send_rabbitmq_notification
from app.prometheus_metrices import TASKS_CREATED, TASK_EXECUTION_TIME, EVALUATE_ALERT_OUTCOMES,EVALUATE_ALERT_STATE_CHANGED, EVALUATE_ALERT_DURATION
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
from app.enums import ThresholdType
from app.DeadCheckerService import check_dead_measurement
from datetime import datetime
from typing import Optional
from app.StaleCheckerService import check_stale_measurement



logger= setup_logging()
# Initialize logger
# logger = logging.getLogger(__name__)
# logging.basicConfig(level=logging.INFO)


# === Alert Processing Helper Functions ===
def _get_event_timestamp(ts: Optional[int], alert_type: str) -> int:
    """
    Standardize timestamp handling across all alert types.

    Args:
        ts: Provided timestamp in milliseconds (can be None)
        alert_type: Type of alert for logging purposes

    Returns:
        int: Timestamp in milliseconds
    """
    if ts is not None:
        return ts
    else:
        # Use current time as fallback
        current_ts = int(datetime.utcnow().timestamp() * 1000)
        logger.warning(f"[{alert_type}] No timestamp provided, using current time: {current_ts}")
        return current_ts


def _get_event_input_value(alert_type: str, measurement_value: Optional[float],
                          current_state: str, last_known_value: Optional[float] = None) -> float:
    """
    Standardize input value handling across all alert types.

    Args:
        alert_type: Type of alert (NOMINAL, DEAD, STALE)
        measurement_value: Current measurement value
        current_state: Current alert state
        last_known_value: Last known good value (for DEAD alerts)

    Returns:
        float: Value to store in event
    """
    # If we have a current measurement value, use it
    if measurement_value is not None:
        logger.debug(f"[{alert_type}] Using measurement value: {measurement_value}")
        return measurement_value

    # For DEAD alerts, try to preserve last known value
    elif current_state == "DEAD" and last_known_value is not None:
        logger.info(f"[{alert_type}] DEAD state - using last known value: {last_known_value}")
        return last_known_value

    # For DEAD alerts with no data at all, use 0
    elif current_state == "DEAD":
        logger.info(f"[{alert_type}] DEAD state - no data available, using 0")
        return 0.0

    # For other states with no measurement value, use 0 as fallback
    else:
        logger.warning(f"[{alert_type}] No measurement value available for state {current_state}, using 0")
        return 0.0


def _log_event_creation(alert_type: str, event_id: Optional[int], alert_id: int,
                       state: str, value: float, timestamp: int) -> None:
    """
    Standardize event creation logging across all alert types.

    Args:
        alert_type: Type of alert
        event_id: Created event ID (can be None if creation failed)
        alert_id: Alert ID
        state: Alert state
        value: Input value
        timestamp: Event timestamp
    """
    if event_id is not None:
        logger.info(
            f"[EVENT-INSERT] type={alert_type} event_id={event_id} alert_id={alert_id} "
            f"state={state} value={value} timestamp={timestamp}"
        )
    else:
        logger.error(
            f"[EVENT-INSERT-FAILED] type={alert_type} alert_id={alert_id} "
            f"state={state} value={value} timestamp={timestamp}"
        )


def _log_excursion_creation(alert_type: str, alert_id: int, end_time: int) -> None:
    """
    Standardize excursion creation logging across all alert types.

    Args:
        alert_type: Type of alert
        alert_id: Alert ID
        end_time: Excursion end time
    """
    logger.info(f"[EXCURSION-INSERT] type={alert_type} alert_id={alert_id} end_time={end_time}")


def _log_notification_sent(alert_type: str, event_id: int, alert_id: int,
                          state: str, timestamp: int) -> None:
    """
    Standardize notification logging across all alert types.

    Args:
        alert_type: Type of alert
        event_id: Event ID
        alert_id: Alert ID
        state: Alert state
        timestamp: Event timestamp
    """
    logger.info(
        f"[NOTIFICATION-SENT] type={alert_type} event_id={event_id} alert_id={alert_id} "
        f"state={state} timestamp={timestamp}"
    )

# Read Celery configuration from config.ini
CELERY_RETRY_ENABLED = config.getboolean('celery', 'retry_enabled', fallback=False)
CELERY_RETRY_MAX_RETRIES = config.getint('celery', 'retry_max_retries', fallback=1)
CELERY_RETRY_COUNTDOWN = config.getint('celery', 'retry_countdown', fallback=60)
CELERY_TASK_EXPIRES = config.getint('celery', 'task_expires', fallback=1800)  # 30 minutes default

# Read MQTT topic for alert notifications
ALERT_TOPIC = config.get('mqtt', 'alert_topic', fallback='alerts/topic')

# if platform.startswith("win"):
#     set_event_loop_policy(WindowsProactorEventLoopPolicy())

class AlertConfig(BaseModel):
    alert_id: int
    asset_path: str
    asset_id: int
    measurement_tag: str
    measurement_id: int
    aggregate: Aggregate
    aggregate_period: AggregatePeriod
    threshold_type: ThresholdType
    comparison: Comparison
    threshold_value: float
    reset_deadband: float
    frequency: int = 20  # Default to 20 seconds
    cust_id: int  # Add customer ID

    class Config:
        json_schema_extra = {
            "example": {
                "alert_id": 1,
                "asset_path": "California:Weather",
                "asset_id": 351,
                "measurement_tag": "California\\Weather\\DEW_POINT_TEMPERATURE (°F)",
                "measurement_id": 18580,
                "aggregate": "max",
                "aggregate_period": "2hr",
                "threshold_type": "NOMINAL",
                "comparison": "LT",
                "threshold_value": 65,
                "reset_deadband": 1,
                "notification_recipients": ["<EMAIL>", "+19492281834"],
                "frequency": 20,
                "cust_id": 8
            }
        }


def get_measurements_and_alerts(configs: List[AlertConfig]) -> Dict[
    Tuple[str, str], Dict[Tuple[str, int], Dict[Tuple[str, int], List[Tuple[float, Comparison, float, List[str]]]]]]:
    measurements_and_alerts = {}  # (agg,period)->{(assettag,assetid)->{(meastag,measid)->[(limit,comparator,deadband,recipients)]}}

    for alertConfig in configs:
        poll_group = (alertConfig.aggregate, alertConfig.aggregate_period)
        if poll_group not in measurements_and_alerts:
            measurements_and_alerts[poll_group] = {}

        asset_key = (alertConfig.asset_path, alertConfig.asset_id)
        if asset_key not in measurements_and_alerts[poll_group]:
            measurements_and_alerts[poll_group][asset_key] = {}

        measurement_key = (alertConfig.measurement_tag, alertConfig.measurement_id)
        if measurement_key not in measurements_and_alerts[poll_group][asset_key]:
            measurements_and_alerts[poll_group][asset_key][measurement_key] = []

        measurements_and_alerts[poll_group][asset_key][measurement_key].append(
            (alertConfig.threshold_value, alertConfig.comparison, alertConfig.reset_deadband, [])
        )

    return measurements_and_alerts


from asgiref.sync import async_to_sync

@contextmanager
def redis_lock(lock_name, expire_time=60):
    lock = master.lock(lock_name, timeout=expire_time, blocking_timeout=1)
    acquired = False
    try:
        acquired = lock.acquire(blocking=False)  # Non-blocking acquisition
        if acquired:
            yield True
        else:
            yield False
    finally:
        if acquired:  # Only release if the lock was successfully acquired
            try:
                lock.release()
            except LockError:
                logger.error(f"Error releasing lock: {lock_name}. It might not have been held.")


@celery_app.task(
    queue='evaluate_alert_task',
    expires=CELERY_TASK_EXPIRES
)
def evaluate_alert_task(*args, **kwargs):
    task_name, task_json = args
    task_dict = json_loads(task_json)
    customer_id = task_dict.get("customer_id", "unknown")
    alert_ids = task_dict.get("alert_ids", [])
    # Log customer and alert context
    logger.info(f"Processing task for customer_id: {customer_id}, alert_ids: {alert_ids}")

    logger.info(f"Starting task {task_name}-{evaluate_alert_task.request.id}")
    TASKS_CREATED.labels(task_name=task_name, status='started').inc()
    task_lock_name = f"lock:{task_name}"
    with redis_lock(task_lock_name, expire_time=300) as lock_acquired:
        if lock_acquired:
            try:
                # extract the task parameters from args
                task_dict = json_loads(task_json)
                logger.info(f"Task JSON: {task_dict}")
                start_time = TASK_EXECUTION_TIME.labels(task_name=task_name).time()
                task = EvaluateAlertTask(**task_dict)
                result = async_to_sync(evaluate_alert_task_async)(task)
                TASKS_CREATED.labels(task_name=task_name, status='completed').inc()
                return result
            except Exception as e:
                TASKS_CREATED.labels(task_name=task_name, status='failed').inc()
                logger.exception(f"Exception in task {evaluate_alert_task.name}: {str(e)}")
            finally:
                logger.info(f"Task {task_name} completed")
        else:
            TASKS_CREATED.labels(task_name=task_name, status='skipped').inc()
            logger.info(f"Task {task_name} is already running. Skipping {evaluate_alert_task.request.id}")


def get_measurements_and_alerts_new(configs: list[Alerts]) -> Dict[
    Tuple[Aggregate, AggregatePeriod], Dict[int, Alerts]]:
    measurements_and_alerts = defaultdict(lambda: defaultdict(Alerts))

    for alertConfig in configs:
        if alertConfig.aggregate_period_enum is None:
            poll_group = (alertConfig.aggregate_enum.value,'none')
        else:
            poll_group = (alertConfig.aggregate_enum.value, alertConfig.aggregate_period_enum.label)
            
        measurements_and_alerts[poll_group][alertConfig.measurement_id] = alertConfig

    return measurements_and_alerts


async def evaluate_alert_task_async(task: EvaluateAlertTask):
    try:
        customer_id = task.customer_id
        alert_configs = fetch_alerts_by_ids(task.alert_ids)
        logger.info(f"Evaluating alerts for customer_id: {task.customer_id}, alert_ids: {task.alert_ids}")
        measurements = []
        alert_id_to_config = defaultdict()
        for alert_config in alert_configs:
            measurements.append(alert_config.measurement_id)
            alert_id_to_config[alert_config.id] = alert_config

        if not measurements:
            EVALUATE_ALERT_OUTCOMES.labels(outcome='failure').inc()
            logger.error(f"Measurements cannot be empty {measurements} for task alert_ids: {task.alert_ids}")
            return False
        
        with EVALUATE_ALERT_DURATION.time():
            data = await read_data_new(
                customer=customer_id,
                measurements=measurements,
                aggregate=Aggregate(task.aggregate),
                period=AggregatePeriod(task.aggregate_period),
                timeout=300,
            )
            # Refactored: Evaluate all data points in the window for each alert
            for alert_id in task.alert_ids:
                alert_config = alert_id_to_config[alert_id]
                if alert_config is None:
                    EVALUATE_ALERT_OUTCOMES.labels(outcome='failure').inc()
                    logger.error(f"Alert configuration not found for alert ID {alert_id}")
                    continue
                alert_id = alert_config.id
                meas_id = alert_config.measurement_id
                limit = alert_config.threshold_value
                deadband = alert_config.reset_deadband
                # Skip ANOMALY threshold type (not implemented)
                if alert_config.threshold_type_enum.threshold == ThresholdType.ANOMALY:
                    logger.info(f"Skipping alert ID {alert_id} with threshold type ANOMALY (not implemented)")
                    continue
                if alert_config.comparison_enum is None:
                    logger.error(f"Comparison enum is None for alert ID {alert_id}")
                    EVALUATE_ALERT_OUTCOMES.labels(outcome='failure').inc()
                    continue
                comparator = alert_config.comparison_enum.condition
                asset_id = alert_config.asset_id
                if meas_id not in data:
                    logger.warning(f"Failed to fetch data for measurement ID {meas_id}")
                    EVALUATE_ALERT_OUTCOMES.labels(outcome='failure').inc()
                    continue

                try:
                    meas_data = data[meas_id]
                    # --- Get all data points for this measurement ---
                    values = meas_data.get('values')
                    if not values or not isinstance(values, list):
                        # Fallback to single value for backward compatibility
                        values = []
                        if meas_data.get('status') == 'success' and 'timestamp' in meas_data:
                            values.append({
                                'timestamp': meas_data['timestamp'],
                                'value': meas_data.get('value')
                            })
                    # --- Fetch last processed timestamp for this alert ---
                    last_processed_ts = 0
                    try:
                        from .db.db_service import fetch_last_processed_timestamp, update_last_processed_timestamp
                        last_processed_ts = fetch_last_processed_timestamp(alert_id) or 0
                    except Exception as e:
                        logger.warning(f"Could not fetch last processed timestamp for alert {alert_id}: {e}")
                        last_processed_ts = 0
                    # --- Sort values by timestamp ---
                    values = sorted(values, key=lambda x: x.get('timestamp', 0))
                    for point in values if values else [None]:
                        if point:
                            ts = point.get('timestamp')
                            val = point.get('value')
                        else:
                            ts = None
                            val = None
                        if ts is not None and ts <= last_processed_ts:
                            continue  # Skip old/delayed/invalid data
                        # --- DEAD Alert ---
                        if alert_config.threshold_type_enum.threshold == ThresholdType.DEAD:
                            now = int(datetime.utcnow().timestamp() * 1000)
                            dead_duration_seconds = int(alert_config.threshold_value * 60 * 1000)
                            last_seen = ts
                            current_value = val
                            prev_state = fetch_alert_state(alert_id)
                            # Get last known value for better DEAD alert handling
                            # TODO: Could fetch from Redis or database if needed
                            last_known_value = val  # Use current value as last known for now

                            result = check_dead_measurement(
                                alert_id=alert_id,
                                measurement_id=meas_id,
                                now=now,
                                last_seen=last_seen,
                                dead_duration_seconds=dead_duration_seconds,
                                aggregate=alert_config.agg,
                                period=alert_config.period,
                                asset_id=asset_id,
                                comparator_id=4 if comparator == 'GE' else 3,
                                current_value=current_value,
                                last_known_value=last_known_value
                            )
                            current_state = LimitState[result.state]
                            last_seen_time = datetime.fromtimestamp(ts/1000).strftime('%Y-%m-%d %H:%M:%S') if ts is not None else 'None'
                            logger.info(
                                f"[DEAD-EVAL] alert_id: {alert_id} meas_id: {meas_id} "
                                f"last_seen: {last_seen_time} duration: {alert_config.threshold_value}min "
                                f"comparison: {comparator} current_value: {val} new_state: {current_state.name}"
                            )
                            if current_state.name != prev_state.name:
                                logger.info(f"State change for alert {alert_id}: {prev_state.name} -> {current_state.name}")
                                update_alert_state(alert_id, current_state.name)

                                # Use standardized timestamp and input value handling
                                event_timestamp = _get_event_timestamp(ts, "DEAD")
                                event_input_value = _get_event_input_value(
                                    alert_type="DEAD",
                                    measurement_value=val,
                                    current_state=current_state.name,
                                    last_known_value=last_known_value
                                )

                                event_id = insert_event(
                                    timestamp=event_timestamp,
                                    input_value=event_input_value,
                                    deadband=0,
                                    state=current_state.name,
                                    limit=result.limit,
                                    comparator=result.comparator,
                                    alert_id=alert_id,
                                    measurement_id=meas_id,
                                    aggregate=alert_config.agg,
                                    period=alert_config.period,
                                    asset_id=asset_id
                                )

                                # Use standardized logging
                                _log_event_creation("DEAD", event_id, alert_id, current_state.name, event_input_value, event_timestamp)
                                if current_state.name == "NORMAL":
                                    insert_state_execution(alert_id, event_timestamp)
                                    _log_excursion_creation("DEAD", alert_id, event_timestamp)

                                if event_id is not None:
                                    send_rabbitmq_notification(result, event_id)
                                    _log_notification_sent("DEAD", event_id, alert_id, current_state.name, event_timestamp)
                                else:
                                    logger.info(f"Event ID is None for Alert ID: {result.alert_id}, notification will not be sent.")
                                EVALUATE_ALERT_STATE_CHANGED.inc()
                            EVALUATE_ALERT_OUTCOMES.labels(outcome='success').inc()
                            update_last_processed_timestamp(alert_id, ts if ts else now)
                            continue
                            # --- STALE Alert ---
                        elif alert_config.threshold_type_enum.threshold == ThresholdType.STALE:
                            if ts is not None:
                                now = datetime.utcfromtimestamp(ts/1000)
                            else:
                                now = datetime.utcnow()
                            stale_duration_minutes = int(alert_config.threshold_value)

                            # The StaleCheckerService now handles last_changed tracking internally
                            # We pass None to let it manage the state properly
                            last_changed = None

                            logger.info(f"[STALE-CHECK] alert_id: {alert_id} meas_id: {meas_id} "
                                      f"threshold: {stale_duration_minutes}min value: {val}")

                            comparison_op = CompareOperation.GE if comparator == 'GE' else CompareOperation.GT

                            # Get stale_band from alert config, default to None (not 0)
                            stale_band = getattr(alert_config, 'stale_band', None)
                            if stale_band is not None and stale_band <= 0:
                                stale_band = None  # Treat 0 or negative as disabled

                            result = check_stale_measurement(
                                alert_id=alert_id,
                                measurement_id=meas_id,
                                now=now,
                                last_changed=last_changed,  # Let StaleCheckerService manage this
                                stale_duration_minutes=stale_duration_minutes,
                                aggregate=alert_config.agg,
                                period=alert_config.period,
                                asset_id=asset_id,
                                comparator_id=4 if comparator == 'GE' else 3,
                                comparison_op=comparison_op,
                                input_val=val,
                                stale_band=stale_band
                            )
                            current_value = result.input_value
                            prev_state = fetch_alert_state(alert_id)
                            current_state = LimitState[result.state]
                            logger.info(
                                f"[STALE-EVAL] alert_id: {alert_id} meas_id: {meas_id} last_changed: {last_changed} "
                                f"duration: {stale_duration_minutes}min comparison: {comparator} "
                                f"current_value: {current_value} new_state: {current_state.name}"
                            )
                            if current_state.name != prev_state.name:
                                logger.info(
                                    f"[STALE-STATE-CHANGE] alert_id: {alert_id} meas_id: {meas_id} "
                                    f"state_change: {prev_state.name} -> {current_state.name}"
                                )
                                update_alert_state(alert_id, current_state.name)

                                # Use standardized timestamp and input value handling
                                event_timestamp = _get_event_timestamp(ts, "STALE")
                                event_input_value = _get_event_input_value(
                                    alert_type="STALE",
                                    measurement_value=val,  # Use actual measurement value, not current_value
                                    current_state=current_state.name
                                )

                                event_id = insert_event(
                                    timestamp=event_timestamp,
                                    input_value=event_input_value,
                                    deadband=0,
                                    state=current_state.name,
                                    limit=result.limit,
                                    comparator=result.comparator,
                                    alert_id=alert_id,
                                    measurement_id=meas_id,
                                    aggregate=alert_config.agg,
                                    period=alert_config.period,
                                    asset_id=asset_id
                                )

                                # Use standardized logging
                                _log_event_creation("STALE", event_id, alert_id, current_state.name, event_input_value, event_timestamp)
                                if current_state.name == "NORMAL":
                                    insert_state_execution(alert_id, event_timestamp)
                                    _log_excursion_creation("STALE", alert_id, event_timestamp)

                                if event_id is not None:
                                    send_rabbitmq_notification(result, event_id)
                                    _log_notification_sent("STALE", event_id, alert_id, current_state.name, event_timestamp)
                                else:
                                    logger.info(f"Event ID is None for Alert ID: {result.alert_id}, notification will not be sent.")
                                EVALUATE_ALERT_STATE_CHANGED.inc()
                            EVALUATE_ALERT_OUTCOMES.labels(outcome='success').inc()
                            update_last_processed_timestamp(alert_id, ts)
                            continue
                        # --- Threshold/Limit Alert ---
                        if meas_data.get('status') == 'success':
                            logger.info(f"[ALERT-EVAL] alert_id={alert_id}, meas_id={meas_id}, status=success, value={val}, timestamp={ts}")
                            result = check_limit(
                                alert_id=alert_id,
                                measurement_id=meas_id,
                                limit=limit,
                                deadband=deadband,
                                comparator=CompareOperation[comparator],
                                timestamp=ts,
                                input_value=val,
                                aggregate=Aggregate(task.aggregate),
                                period=AggregatePeriod(task.aggregate_period),
                                asset_id=asset_id
                            )
                            if result is not None and result.state is not None:
                                prev_state = fetch_alert_state(alert_id)
                                current_state = LimitState[result.state]
                                logger.info(f"alert_id: {alert_id} meas_id {meas_id} limit:{limit} current_val:{val} prev_state: {prev_state} current_state: {current_state}")
                                if current_state and prev_state.name != current_state.name:
                                    logger.info(f"[STATE CHANGE] alert_id={alert_id}, prev_state={prev_state.name}, current_state={current_state.name}")
                                    update_alert_state(alert_id, current_state.name)

                                    # Use standardized timestamp and input value handling
                                    event_timestamp = _get_event_timestamp(ts, "NOMINAL")
                                    event_input_value = _get_event_input_value(
                                        alert_type="NOMINAL",
                                        measurement_value=val,
                                        current_state=current_state.name
                                    )

                                    event_id = insert_event(
                                        timestamp=event_timestamp,
                                        input_value=event_input_value,
                                        deadband=deadband,
                                        state=current_state.name,
                                        limit=result.limit,
                                        comparator=alert_config.condition,
                                        alert_id=alert_id,
                                        measurement_id=meas_id,
                                        aggregate=alert_config.agg,
                                        period=alert_config.period,
                                        asset_id=asset_id
                                    )

                                    # Use standardized logging
                                    _log_event_creation("NOMINAL", event_id, alert_id, current_state.name, event_input_value, event_timestamp)
                                    if current_state.name == "NORMAL":
                                        insert_state_execution(alert_id, event_timestamp)
                                        _log_excursion_creation("NOMINAL", alert_id, event_timestamp)

                                    if event_id is not None:
                                        send_rabbitmq_notification(result, event_id)
                                        _log_notification_sent("NOMINAL", event_id, alert_id, current_state.name, event_timestamp)
                                    else:
                                        logger.info(f"Event ID is None for Alert ID: {result.alert_id}, notification will not be sent.")
                                    EVALUATE_ALERT_STATE_CHANGED.inc()
                                EVALUATE_ALERT_OUTCOMES.labels(outcome='success').inc()
                                update_last_processed_timestamp(alert_id, ts)
                        elif meas_data.get('status') == 'nodata':
                            EVALUATE_ALERT_OUTCOMES.labels(outcome='no_data').inc()
                            logger.warning(f"No data found for measurement ID {meas_id}")
                        else:
                            EVALUATE_ALERT_OUTCOMES.labels(outcome='failure').inc()
                            logger.exception(f"Failed to fetch data for measurement ID {meas_id}")
                except ClientError as e:
                    EVALUATE_ALERT_OUTCOMES.labels(outcome='failure').inc()
                    logger.exception(f"Network error while fetching data for meas_id {meas_id}: {str(e)}")
                except TimeoutError as e:
                    EVALUATE_ALERT_OUTCOMES.labels(outcome='failure').inc()
                    logger.exception(f"Timeout occurred while fetching data for meas_id {meas_id}: {str(e)}")
                except Exception as e:
                    EVALUATE_ALERT_OUTCOMES.labels(outcome='failure').inc()
                    logger.exception(f"Unexpected error while processing alert ID {alert_id}: {str(e)}")
                    continue
    except JSONDecodeError as e:
        EVALUATE_ALERT_OUTCOMES.labels(outcome='failure').inc()
        logger.exception(f"Failed to parse alert configuration: {str(e)}")
    except Exception as e:
        logger.exception(f"Unexpected error in evaluate_alert evaluate_alert_task_async: {str(e)}")
        logger.exception(f"Exception : {e}")
        EVALUATE_ALERT_OUTCOMES.labels(outcome='failure').inc()
    return True