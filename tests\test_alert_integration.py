import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

from app.tasks import update_alert_state, insert_event
from app.StaleCheckerService import check_stale_measurement
from app.DeadCheckerService import check_dead_measurement
from app.LimitCheckerService import check_limit
from app.enums import CompareOperation, LimitState

@pytest.fixture
def mock_redis(monkeypatch):
    store = {}
    monkeypatch.setattr("app.StaleCheckerService.master", MagicMock())
    monkeypatch.setattr("app.StaleCheckerService.master.get", lambda key: store.get(key))
    monkeypatch.setattr("app.StaleCheckerService.master.set", lambda key, val, ex=None: store.update({key: val}))
    return store

@pytest.fixture
def mock_db(monkeypatch):
    monkeypatch.setattr("app.db.db_service.update_alert_state", MagicMock())
    monkeypatch.setattr("app.db.db_service.insert_event", MagicMock())
    monkeypatch.setattr("app.db.db_service.fetch_alert_state", MagicMock(return_value=LimitState.NORMAL))

@pytest.fixture
def mock_notify(monkeypatch):
    monkeypatch.setattr("app.rabbitmq.rabbitmq_service.send_rabbitmq_notification", MagicMock())

def test_nominal_alert_lifecycle(mock_db, mock_notify):
    # Simulate nominal alert state transitions
    now = datetime.now()
    alert_id = 100
    measurement_id = 200
    threshold = 10.0

    # Value below threshold
    result = check_limit(
        alert_id=alert_id,
        measurement_id=measurement_id,
        limit=threshold,
        deadband=0.0,
        comparator=CompareOperation.GT,
        timestamp=now,
        input_value=5.0,
        aggregate="avg",
        period="1m",
        asset_id=1
    )
    assert result.state == "NORMAL"

    # Value above threshold
    result = check_limit(
        alert_id=alert_id,
        measurement_id=measurement_id,
        limit=threshold,
        deadband=0.0,
        comparator=CompareOperation.GT,
        timestamp=now,
        input_value=15.0,
        aggregate="avg",
        period="1m",
        asset_id=1
    )
    assert result.state == "EXCEEDED"

def test_dead_alert_lifecycle(mock_db, mock_notify):
    # Simulate dead alert state transitions
    now = int(datetime.now().timestamp() * 1000)
    alert_id = 101
    measurement_id = 201
    threshold_ms = 60 * 1000

    # Recent data (should be NORMAL)
    result = check_dead_measurement(
        alert_id=alert_id,
        measurement_id=measurement_id,
        now=now,
        last_seen=now,
        dead_duration_seconds=threshold_ms,
        aggregate="avg",
        period="1m",
        asset_id=1,
        comparator_id=1,
        current_value=1.0
    )
    assert result.state == "NORMAL"

    # Old data (should be DEAD)
    old = now - 2 * threshold_ms
    result = check_dead_measurement(
        alert_id=alert_id,
        measurement_id=measurement_id,
        now=now,
        last_seen=old,
        dead_duration_seconds=threshold_ms,
        aggregate="avg",
        period="1m",
        asset_id=1,
        comparator_id=1,
        current_value=1.0
    )
    assert result.state == "DEAD"

@patch("app.StaleCheckerService.master")
def test_stale_alert_lifecycle(mock_master, mock_db, mock_notify):
    # Simulate stale alert state transitions
    now = datetime.now()
    alert_id = 102
    measurement_id = 202
    stale_duration = 7  # minutes

    # Simulate previous value stored 8 minutes ago
    def get_side_effect(key):
        if "last_value" in key:
            return "20.0"
        return None
    mock_master.get.side_effect = get_side_effect

    result = check_stale_measurement(
        alert_id=alert_id,
        measurement_id=measurement_id,
        now=now,
        last_changed=now - timedelta(minutes=8),
        stale_duration_minutes=stale_duration,
        aggregate="avg",
        period="1m",
        asset_id=1,
        comparator_id=1,
        comparison_op=CompareOperation.GT,
        input_val=20.0
    )
    assert result.state == "STALE"

    # Value changes, should reset to NORMAL
    mock_master.get.return_value = "25.0"
    result = check_stale_measurement(
        alert_id=alert_id,
        measurement_id=measurement_id,
        now=now,
        last_changed=now,
        stale_duration_minutes=stale_duration,
        aggregate="avg",
        period="1m",
        asset_id=1,
        comparator_id=1,
        comparison_op=CompareOperation.GT,
        input_val=25.0
    )
    assert result.state == "NORMAL"


class TestLateDataHandlingIntegration:
    """Integration tests for late data handling functionality."""

    @patch('app.db.db_service.fetch_last_processed_timestamp')
    @patch('app.db.db_service.update_last_processed_timestamp')
    def test_late_data_filtering_integration(self, mock_update_ts, mock_fetch_ts):
        """Test that late data handling works in integration with alert processing."""
        # Setup timestamps to simulate late data scenario
        base_time = int(datetime.utcnow().timestamp() * 1000)
        old_ts = base_time - 600000      # 10 minutes ago (late data)
        last_processed = base_time - 300000  # 5 minutes ago (already processed up to here)
        new_ts = base_time - 120000      # 2 minutes ago (new data)

        # Mock last processed timestamp
        mock_fetch_ts.return_value = last_processed

        # Test data filtering logic (simulates what tasks.py does)
        test_data = [
            {'timestamp': old_ts, 'value': 15.0},    # Should be filtered (too old)
            {'timestamp': new_ts, 'value': 12.0},    # Should be processed (new)
        ]

        # Apply filtering logic
        processed_data = []
        for point in test_data:
            ts = point.get('timestamp')
            if ts is not None and ts > last_processed:
                processed_data.append(point)

        # Verify late data handling
        assert len(processed_data) == 1
        assert processed_data[0]['timestamp'] == new_ts
        assert processed_data[0]['value'] == 12.0

        # Verify old data was filtered out
        old_timestamps = [point['timestamp'] for point in processed_data if point['timestamp'] == old_ts]
        assert len(old_timestamps) == 0

    def test_out_of_order_data_sorting_integration(self):
        """Test that out-of-order data is properly sorted before processing."""
        # Setup out-of-order timestamps
        base_time = int(datetime.utcnow().timestamp() * 1000)
        ts1 = base_time - 300000  # 5 minutes ago (first chronologically)
        ts2 = base_time - 180000  # 3 minutes ago (second chronologically)
        ts3 = base_time - 240000  # 4 minutes ago (third chronologically, but arrives out of order)

        # Simulate data arriving out of order
        received_data = [
            {'timestamp': ts1, 'value': 10.0},  # Arrives first
            {'timestamp': ts3, 'value': 12.0},  # Arrives second (out of order)
            {'timestamp': ts2, 'value': 8.0},   # Arrives third (out of order)
        ]

        # Apply sorting logic (simulates what tasks.py does)
        sorted_data = sorted(received_data, key=lambda x: x.get('timestamp', 0))

        # Verify chronological order after sorting
        expected_timestamps = [ts1, ts3, ts2]  # Chronological order
        actual_timestamps = [point['timestamp'] for point in sorted_data]
        assert actual_timestamps == expected_timestamps

        # Verify values are in corresponding chronological order
        expected_values = [10.0, 12.0, 8.0]
        actual_values = [point['value'] for point in sorted_data]
        assert actual_values == expected_values