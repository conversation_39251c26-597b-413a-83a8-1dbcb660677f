import os
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
from BromptonPythonUtilities.Redis_Connection_Utility.tsdb_connection import get_redis_client
from app.config import (
    redis_host,
    redis_password,
    redis_db,
    redis_port,
    use_sentinel,
    redis_sentinel_master,
    async_mode,
    redis_hosts
)


logger = setup_logging()

try:
    # Clear any existing Redis-related environment variables
    for key in list(os.environ.keys()):
        if key.startswith('redis_') or key == 'sentinel':
            del os.environ[key]

    # Set environment variables for Redis connection
    if use_sentinel:
        # Set sentinel-specific environment variables
        for i, host_port in enumerate(redis_hosts):
            host, port = host_port.split(':')
            os.environ[f'redis_sentinel_host_{i}'] = host
        os.environ['redis_sentinel_master'] = redis_sentinel_master
        os.environ['sentinel'] = 'true'
        logger.info(f"Configuring Redis Sentinel with hosts: {redis_hosts}")
    else:
        # Standalone Redis mode using config values
        os.environ['sentinel'] = 'false'
        os.environ['redis_host'] = redis_host
        os.environ['redis_port'] = str(redis_port)
        logger.info(f"Using Redis host: {redis_host}:{redis_port}")

    # Common environment variables
    os.environ['redis_password'] = redis_password
    os.environ['redis_db'] = str(redis_db)
    os.environ['async_mode'] = str(async_mode).lower()

    # Initialize Redis client
    redis_client = get_redis_client()
    logger.info(f"Redis client initialized successfully in {'Sentinel' if use_sentinel else 'Standalone'} mode")

    # Extract connection details for broker URL
    pool = redis_client.connection_pool
    host = pool.connection_kwargs.get("host")
    port = pool.connection_kwargs.get("port")
    db = pool.connection_kwargs.get("db", 0)

    # Verify connection
    redis_client.ping()
    logger.info(f"Successfully connected to Redis at {host}:{port}")

    if use_sentinel:
        broker_url = f"redis://:{redis_password}@{host}:{port}/{db}?sentinel={redis_sentinel_master}"
        logger.info(f"Using Redis Sentinel with master: {redis_sentinel_master}")
    else:
        broker_url = f"redis://:{redis_password}@{host}:{port}/{db}"
    
    logger.info(f"Redis broker URL configured (host: {host}, port: {port}, db: {db})")

except Exception as e:
    logger.error(f"Failed to initialize Redis client: {str(e)}")
    raise

# Export the redis client as master for backward compatibility
master = redis_client



# from redis import StrictRedis
# from redis.exceptions import ResponseError
# from redis.sentinel import Sentinel
# from app.config import broker_port, broker_db, broker_host, redis_password, redis_sentinel_master
# import logging
# from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
# logger= setup_logging()
# # logger = logging.getLogger(__name__)

# def get_redis_connection():
#     # Attempt to connect via Sentinel if a sentinel master is specified
#     if redis_sentinel_master:
#         try:
#             sentinel = Sentinel(
#                 sentinels=[('redis-node-0.redis-headless.redis.svc.cluster.local', 26379),
#                      ('redis-node-1.redis-headless.redis.svc.cluster.local', 26379),
#                      ('redis-node-2.redis-headless.redis.svc.cluster.local', 26379)
#                      ],
#                 sentinel_kwargs={'password': redis_password},
#                 socket_timeout=10,
#                 password=redis_password
#             )

#             master = sentinel.master_for(redis_sentinel_master, db=broker_db, password=redis_password)
#             master_address = master.connection_pool.get_master_address()
#             master_host, master_port = master_address

#             logger.info(f"Connected to Redis master via Sentinel at {master_host}:{master_port}")
#             return master, master_host, master_port
#         except ResponseError as e:
#             logger.error(f"Sentinel command error: {e}. Falling back to standard Redis connection.")
#         except Exception as e:
#             logger.error(f"Failed to connect to Redis via Sentinel: {e}")
#             raise

#     # If Sentinel is not configured or fails, fallback to a standard Redis connection
#     try:
#         redis_client = StrictRedis(
#             host=broker_host,
#             port=broker_port,
#             db=broker_db,
#             password=redis_password
#         )
#         logger.info(f"Connected to Redis at {broker_host}:{broker_port}")
#         return redis_client, broker_host, broker_port
#     except Exception as e:
#         logger.error(f"Failed to connect to Redis: {e}")
#         raise

# # Get the Redis connection (this will be either Sentinel-based or standard Redis)
# master, master_host, master_port = get_redis_connection()

# # Set broker_url based on the connection type
# if redis_sentinel_master:
#     broker_url = f"redis://:{redis_password}@{master_host}:{master_port}/{broker_db}"
# else:
#     broker_url = f"redis://:{redis_password}@{broker_host}:{broker_port}/{broker_db}"  # Assuming this is set to a standard Redis URL in your config