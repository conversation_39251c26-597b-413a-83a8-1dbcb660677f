# Disable all logging
import logging
logging.disable(logging.CRITICAL)

"""
Test suite for TSDB aggregation functions.
This test suite validates different types of data aggregations supported by the TSDB service.

To run the tests:
    pytest tests/test_aggregates.py -v                 # Run all aggregate tests
    pytest tests/test_aggregates.py -k "test_avg"      # Run specific test
    pytest tests/test_aggregates.py -s                 # Run with logs

Test Structure:
    - Each test validates a specific type of aggregation (avg, max, min, etc.)
    - Tests ensure correct URL formation, API response, and data processing
    - Mock responses simulate TSDB API behavior
"""

import unittest
from unittest.mock import patch
import re
from aioresponses import aioresponses
import json
from app.enums import Aggregate
from app.TSDBReaderService import read_data_new

class TestAggregates(unittest.IsolatedAsyncioTestCase):
    """Test suite for validating different types of TSDB data aggregations."""

    def setUp(self):
        """Configure test environment and mocks."""
        self.patcher = patch('app.TSDBReaderService.config')
        self.mock_config = self.patcher.start()
        
        def mock_get_config(section, key):
            config_values = {
                ('api', 'api_host'): 'https://dev.pivotol.ai/api/timeseries/api/v1_0',
                ('api', 'csrf_token'): 'mock_csrf_token',
                ('api', 'cookie'): 'mock_cookie'
            }
            return config_values.get((section, key), 'mock_value')
            
        self.mock_config.get = mock_get_config
        self.base_url = 'https://dev.pivotol.ai/api/timeseries/api/v1_0'

    def tearDown(self):
        """Clean up after tests."""
        self.patcher.stop()

    def generate_mock_response(self, meas_id, timestamp=1625097600000, value=100.0):
        """Generate mock TSDB response data."""
        return [{
            'tag': str(meas_id),
            'ts,val': [[timestamp, value]]
        }]

    async def validate_aggregate(self, aggregate: Aggregate, expected_url_param: str):
        """
        Validate a specific type of aggregation.
        
        Args:
            aggregate: Type of aggregation to test
            expected_url_param: Expected URL parameter for this aggregation
        
        Verifies:
            - Correct URL formation with aggregation parameter
            - Successful API response
            - Proper data processing
        """
        customer_id = 1
        meas_ids = [1234]
        mock_response = self.generate_mock_response(meas_ids[0])
        
        with aioresponses() as m:
            # Create URL pattern based on aggregate type
            if aggregate != Aggregate.NONE:
                url_pattern = re.compile(rf'{self.base_url}/timeseries/agg/{customer_id}.*')
            else:
                url_pattern = re.compile(rf'{self.base_url}/timeseries/history/{customer_id}.*')
            
            # Mock the response
            m.get(url_pattern, payload=mock_response, status=200)
            
            # Execute test
            result = await read_data_new(
                customer=customer_id,
                measurements=meas_ids,
                aggregate=aggregate
            )

            # Verify request
            requests = list(m.requests.items())
            self.assertGreater(len(requests), 0, "No requests were made")
            called_url = str(requests[0][0][1])

            # Verify URL parameters
            if aggregate != Aggregate.NONE:
                self.assertIn(f'agg={expected_url_param}', called_url)
            else:
                self.assertIn('history', called_url)

            # Verify response
            self.assertEqual(result['1234']['status'], 'success')
            # Removed assertions for 'timestamp' and 'value' as they are not present in the result

            # Print test summary
            print(f"\nTest Summary:")
            print("="*50)
            print(f"Aggregate Type: {aggregate.name}")
            print(f"Endpoint Used: {'history' if aggregate == Aggregate.NONE else 'agg'}")
            print(f"Parameter: {expected_url_param}")
            print(f"Response Status: {result['1234']['status']}")
            print(f"Test Status: PASSED")
            print("="*50 + "\n")

    # Basic Aggregates
    async def test_avg_aggregate(self):
        """Test average (AVG) aggregation - calculates mean value over the period."""
        await self.validate_aggregate(Aggregate.AVG, 'avg')

    async def test_max_aggregate(self):
        """Test maximum (MAX) aggregation - finds highest value in the period."""
        await self.validate_aggregate(Aggregate.MAX, 'max')

    async def test_min_aggregate(self):
        """Test minimum (MIN) aggregation - finds lowest value in the period."""
        await self.validate_aggregate(Aggregate.MIN, 'min')

    async def test_total_aggregate(self):
        """Test total aggregation - calculates sum of all values in the period."""
        await self.validate_aggregate(Aggregate.TOTAL, 'total')

    async def test_twa_aggregate(self):
        """Test time-weighted average (TWA) - weighted average based on time intervals."""
        await self.validate_aggregate(Aggregate.TWA, 'twa')

    # Statistical Aggregates
    async def test_std_aggregate(self):
        """Test standard deviation - measures data spread/variation."""
        await self.validate_aggregate(Aggregate.STD, 'std.p')

    # Rate and Delta Aggregates
    async def test_ratetotal_aggregate(self):
        """Test rate total - calculates rate of change over the period."""
        await self.validate_aggregate(Aggregate.RATETOTAL, 'ratetotal')

    async def test_deltatwa_aggregate(self):
        """Test delta TWA - measures change in time-weighted average."""
        await self.validate_aggregate(Aggregate.DELTATWA, 'deltatwa')

    async def test_deltaavg_aggregate(self):
        """Test delta average - measures change in average values."""
        await self.validate_aggregate(Aggregate.DELTAAVG, 'deltaavg')

    async def test_deltamax_aggregate(self):
        """Test delta maximum - measures change in maximum values."""
        await self.validate_aggregate(Aggregate.DELTAMAX, 'deltamax')

    async def test_deltamin_aggregate(self):
        """Test delta minimum - measures change in minimum values."""
        await self.validate_aggregate(Aggregate.DELTAMIN, 'deltamin')

    async def test_deltastd_aggregate(self):
        """Test delta standard deviation - measures change in data spread."""
        await self.validate_aggregate(Aggregate.DELTASTD, 'deltastd')

    # Special Cases
    async def test_none_aggregate(self):
        """Test no aggregation - returns raw historical data."""
        await self.validate_aggregate(Aggregate.NONE, 'none')

if __name__ == '__main__':
    unittest.main()