# STALE Alert Timestamp Fix

## Issue Description

There was an inconsistency in timestamp handling for STALE alerts in the event insertion process. STALE alert events were using inconsistent timestamps compared to NOMINAL and DEAD alerts, causing confusion in event records.

### Problem Details

**Observed Behavior:**
- STALE events had timestamps that didn't match the expected pattern
- Example from production data:
  - STALE event: 11-Jul-2025 06:53:07 am
  - NORMAL event: 11-Jul-2025 06:53:03 am
  - 4-second difference indicated timing inconsistency

**Root Cause:**
1. **STALE alerts used current time (`now`) for internal logic** in `StaleCheckerService`
2. **Event insertion used measurement timestamp (`ts`)** via `_get_event_timestamp()`
3. **This created a mismatch** where STALE events didn't reflect when the stale condition was actually detected

## Technical Analysis

### Timestamp Flow Comparison

| Alert Type | Timestamp Source | Event Timestamp | Consistency |
|------------|------------------|-----------------|-------------|
| **NOMINAL** | Measurement timestamp (`ts`) | `_get_event_timestamp(ts)` | ✅ Consistent |
| **DEAD** | Measurement timestamp (`ts`) | `_get_event_timestamp(ts)` | ✅ Consistent |
| **STALE** (Before) | Current time (`now`) internally, but `ts` for events | `_get_event_timestamp(ts)` | ❌ Inconsistent |
| **STALE** (After) | Current time (`now`) for both | Detection timestamp | ✅ Consistent |

### Code Locations

**Issue Location:** `app/tasks.py` lines 450-456 (before fix)
```python
# BEFORE (Inconsistent)
event_timestamp = _get_event_timestamp(ts, "STALE")  # Uses measurement time
# But StaleCheckerService uses current time internally
```

**Fix Location:** `app/tasks.py` lines 450-460 (after fix)
```python
# AFTER (Consistent)
current_time_ms = int(datetime.utcnow().timestamp() * 1000)
event_timestamp = current_time_ms  # Uses detection time
```

## Solution Implemented

### Fix Strategy

**Decision:** STALE alerts should use **detection timestamp** (when stale condition was detected) rather than measurement timestamp.

**Rationale:**
1. **Semantic Accuracy**: STALE events represent when the stale condition was detected, not when the measurement occurred
2. **Consistency**: Aligns with how `StaleCheckerService` internally calculates stale conditions
3. **Clarity**: Makes it clear when the system detected the stale condition

### Implementation

**File:** `app/tasks.py`
**Lines:** 450-460

```python
# For STALE alerts, use current time (when stale was detected) not measurement time
# This ensures the event timestamp reflects when the stale condition was detected
current_time_ms = int(datetime.utcnow().timestamp() * 1000)
event_timestamp = current_time_ms
event_input_value = _get_event_input_value(
    alert_type="STALE",
    measurement_value=val,  # Use actual measurement value, not current_value
    current_state=current_state.name
)

logger.debug(f"[STALE-TIMESTAMP] Using detection time {event_timestamp} instead of measurement time {ts}")
```

## Testing

### Test Coverage

**New Test File:** `tests/test_stale_timestamp_issue.py`

**Test Categories:**
1. **Consistency Tests**: Verify `_get_event_timestamp()` behavior
2. **Issue Demonstration**: Show the original problem
3. **Fix Verification**: Confirm the solution works
4. **Integration Tests**: End-to-end timestamp handling

**Key Test Results:**
- ✅ All existing STALE alert tests pass
- ✅ NOMINAL and DEAD alert tests unaffected
- ✅ New timestamp behavior verified
- ✅ No regressions introduced

### Test Execution

```bash
# Run STALE timestamp tests
pytest tests/test_stale_timestamp_issue.py -v

# Verify no regressions
pytest tests/test_stale_alert.py tests/test_nominal_alert.py tests/test_dead_alert.py -v
```

## Impact Analysis

### Before Fix

```
Event ID: 122115
Timestamp: 11-Jul-2025 06:53:07 am  # Detection time (inconsistent)
State: STALE
Input Value: 0

Event ID: 122126  
Timestamp: 11-Jul-2025 06:53:03 am  # Measurement time
State: NORMAL
Input Value: 33.00
```

**Issues:**
- Timestamps didn't follow logical sequence
- STALE event appeared to occur after NORMAL event
- Confusion about when stale condition was detected

### After Fix

```
Event ID: 122115
Timestamp: 11-Jul-2025 06:53:07 am  # Detection time (consistent)
State: STALE
Input Value: 33.00  # Actual measurement value

Event ID: 122126
Timestamp: 11-Jul-2025 06:53:03 am  # Measurement time  
State: NORMAL
Input Value: 33.00
```

**Improvements:**
- ✅ Timestamps follow logical sequence
- ✅ STALE event clearly shows when condition was detected
- ✅ Input values are consistent and meaningful
- ✅ Event timeline makes sense

## Behavioral Changes

### What Changed

1. **STALE Event Timestamps**: Now use detection time instead of measurement time
2. **Timestamp Consistency**: STALE events are internally consistent
3. **Event Sequencing**: Events appear in logical chronological order

### What Stayed the Same

1. **STALE Detection Logic**: No changes to when STALE conditions are detected
2. **NOMINAL/DEAD Alerts**: No changes to other alert types
3. **Event Data**: All other event fields remain unchanged
4. **API Compatibility**: No breaking changes to external interfaces

## Monitoring

### Log Messages

**New Debug Log:**
```
[STALE-TIMESTAMP] Using detection time 1720689183000 instead of measurement time 1720689000000
```

**Existing Logs Unchanged:**
```
[EVENT-INSERT] type=STALE event_id=12345 alert_id=456 state=STALE value=33.0 timestamp=1720689183000
[STALE-RESULT] alert_id=456, meas_id=123, value=33.0, time_since_change=20.0min, state=STALE
```

### Verification

To verify the fix is working:

1. **Check Event Timestamps**: STALE events should have timestamps close to current time
2. **Monitor Log Sequence**: Events should appear in logical chronological order
3. **Validate State Changes**: STALE → NORMAL transitions should make temporal sense

## Future Considerations

### Consistency Across Alert Types

| Alert Type | Event Timestamp Represents | Rationale |
|------------|---------------------------|-----------|
| **NOMINAL** | When measurement occurred | Value-based alerts tied to measurement time |
| **DEAD** | When measurement occurred | Absence detection tied to last known measurement |
| **STALE** | When stale condition detected | Condition detection tied to analysis time |

### Potential Enhancements

1. **Configurable Behavior**: Allow configuration of timestamp strategy per alert type
2. **Dual Timestamps**: Store both measurement time and detection time
3. **Event Metadata**: Add additional context about timing decisions

## Conclusion

This fix resolves the timestamp inconsistency in STALE alerts by ensuring that:

1. **STALE events use detection timestamps** (when stale condition was identified)
2. **Timestamps are internally consistent** within STALE alert processing
3. **Event sequences are logical** and chronologically correct
4. **No regressions** are introduced to existing functionality

The fix improves data quality and reduces confusion in event analysis while maintaining full backward compatibility with existing systems.
