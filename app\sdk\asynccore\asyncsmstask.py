import datetime
import zoneinfo
from typing import List
from .asynceventtasks import LimitState,CompareOperation
from .asyncevent import AsyncTask, PersistentObject, EventState
from twilio.rest import Client
import re
SMS_REGEX=re.compile(r"^\+[1-9]{1}[0-9]{0,2}[2-9]{1}[0-9]{2}[2-9]{1}[0-9]{2}[0-9]{4}")

class AsyncSmsTask(AsyncTask):
    def __init__(self,sender:str,recipients:List[str],completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        # if not SMS_REGEX.match(sender):
            # raise ValueError(f"{sender} is not a valid phone number")
        self.recipients=recipients
        self.__frm__=sender
        super().__init__(completion_callback=completion_callback,timeout=timeout,persist_obj=persist_obj)

    @property
    def recipients(self):
        return self.__recipients__
    @recipients.setter
    def recipients(self,recipients):
        # for subscriber in recipients:
        #     if not SMS_REGEX.match(subscriber):
        #         raise ValueError(f"{subscriber} is not a valid phone number")
        self.__recipients__=recipients

    # TO BE OVERRIDEN IN Derived classes
    def __get_body__(self,state:EventState):
        raise NotImplementedError("This method is Abstract.  Must implement in derived class")

class AsyncPrintTask(AsyncTask):
    def __init__(self,completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        super().__init__(completion_callback=completion_callback,timeout=timeout,persist_obj=persist_obj)

    def __get_body__(self,state:EventState):
        print("AsyncPrintTask: {}".format(state))
        utcdatetime = datetime.datetime.fromtimestamp(state.timestamp,tz=datetime.timezone.utc).strftime("%Y-%m-%dT%H:%M:%S %Z")
        if(isinstance(state.source,EventState)):
            state_desc = "Limit Alert"
            if (state.state['state'] == LimitState.NORMAL):
                state_desc = "Returned to Normal"
            asset=state.source.source['asset']['tag']
            measurement=state.source.source['measurement']['tag']
            limit = state.state['limit']
            comparator = state.state['comparator']
            deadband = state.state['deadband']
            current_value = state.state['input']
            return_to_normal = ''
            if (comparator in [CompareOperation.GE, CompareOperation.GT]):
                return_to_normal = f"Return to Normal: LE {limit - deadband}"
            elif (comparator in [CompareOperation.LE, CompareOperation.LT]):
                return_to_normal = f"Return to Normal: GE {limit + deadband}"
            return f"{state_desc}\nAt: {utcdatetime}\nAsset: {asset}\nMeasurement: {measurement}\nCurrentValue: {current_value}\nSettings:\n\tThreshold: {comparator.name} {limit}\n\t{return_to_normal}\n"
        elif(isinstance(state.source,PersistentObject)):
                return f"At: {utcdatetime}\n{state.source.description}" # TODO: redo later as more ideas of persistent object take shape

    async def __execute__(self, state:EventState, *args, **kwargs):
        exceptions=[]
        body=self.__get_body__(state)
        print(f"Sending SMS message: {body}")
        # for recipient in self.recipients:
        #     try:
        #         client = Client(self.__account_sid__,self.__auth_token__)
        #         client.messages.create(
        #                      body=body,
        #                      from_=self.__frm__,
        #                      to=recipient
        #                  )
        #     except Exception as e:
        #         exceptions.append(e)
        # if(exceptions):
        #     msg="\n".join([e.args[0] for e in exceptions])
        #     raise(Exception(f"Exceptions sending SMS message for state {e}:\n{msg}"))
        return True


class AsyncTwilioAlertSmsTask(AsyncSmsTask):
    def __init__(self,account_sid:str,auth_token:str,sender:str,recipients:List[str],completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        self.__account_sid__=account_sid
        self.__auth_token__=auth_token
        super().__init__(sender=sender,recipients=recipients,completion_callback=completion_callback,timeout=timeout,persist_obj=persist_obj)

    def __get_body__(self,state:EventState):
        utcdatetime = datetime.datetime.fromtimestamp(state.timestamp,tz=datetime.timezone.utc).strftime("%Y-%m-%dT%H:%M:%S %Z")
        if(isinstance(state.source,EventState)):
            state_desc = "Limit Alert"
            if (state.state['state'] == LimitState.NORMAL):
                state_desc = "Returned to Normal"
            asset=state.source.source['asset']['tag']
            measurement=state.source.source['measurement']['tag']
            limit = state.state['limit']
            comparator = state.state['comparator']
            deadband = state.state['deadband']
            current_value = state.state['input']
            return_to_normal = ''
            if (comparator in [CompareOperation.GE, CompareOperation.GT]):
                return_to_normal = f"Return to Normal: LE {limit - deadband}"
            elif (comparator in [CompareOperation.LE, CompareOperation.LT]):
                return_to_normal = f"Return to Normal: GE {limit + deadband}"
            return f"{state_desc}\nAt: {utcdatetime}\nAsset: {asset}\nMeasurement: {measurement}\nCurrentValue: {current_value}\nSettings:\n\tThreshold: {comparator.name} {limit}\n\t{return_to_normal}\n"
        elif(isinstance(state.source,PersistentObject)):
                return f"At: {utcdatetime}\n{state.source.description}" # TODO: redo later as more ideas of persistent object take shape

    async def __execute__(self, state:EventState, *args, **kwargs):
        exceptions=[]
        body=self.__get_body__(state)
        print(f"Sending SMS message: {body}")
        # for recipient in self.recipients:
        #     try:
        #         client = Client(self.__account_sid__,self.__auth_token__)
        #         client.messages.create(
        #                      body=body,
        #                      from_=self.__frm__,
        #                      to=recipient
        #                  )
        #     except Exception as e:
        #         exceptions.append(e)
        # if(exceptions):
        #     msg="\n".join([e.args[0] for e in exceptions])
        #     raise(Exception(f"Exceptions sending SMS message for state {e}:\n{msg}"))
        return True