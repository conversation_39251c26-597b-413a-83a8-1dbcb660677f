from .asyncevent import *

# Event is the event that will be raised by this task
# Period is the value in seconds under which a count>target_count will trigger the event
# Count is maximum number of event to count over the Period if equal or greater triggers
# Examples: Count=3, Period=3600 => 3 time per hour, Count=2
# Once triggered the count is reset to zero and task waits again for the condition
MAXCOUNTLIMIT=50
MINPERIOD=60
class AsyncEventFrequencyCounterTask(AsyncTaskWithEvent):
    def __init__(self, event_context:str, count_limit:int=2, target_period:float=60, completion_callback=None,
                 timeout: float = None, persist_obj: PersistentObject = None):
        self.count_limit = count_limit
        self.target_period=target_period
        self.__queue__=[]  # queue stores event timestamps in seconds
        super().__init__(event_context=event_context,completion_callback=completion_callback, timeout=timeout, persist_obj=persist_obj)

    @property
    def count_limit(self):
        return self.__count_limit__

    @count_limit.setter
    def count_limit(self, count_limit: int):
        if(count_limit and (not isinstance(count_limit, int) or count_limit<2 or count_limit>MAXCOUNTLIMIT)):
             raise ValueError(f"count_limit must be an positive int between 2 and {MAXCOUNTLIMIT}")
        self.__count_limit__=count_limit

    @property
    def target_period(self):
        return self.__target_period__

    @target_period.setter
    def target_period(self, target_period: float):
        if(target_period and not isinstance(target_period, int) or target_period<0 or target_period<MINPERIOD):
             raise ValueError(f"target_period must be an positive float in sec > ({MINPERIOD}")
        self.__target_period__=target_period

    # overriden execute method
    async def __execute__(self, state:EventState, *args, **kwargs):
        if(state is None or state.timestamp is None):
            raise ValueError("An even state and its timestamp are required")
        count_exceeded=False
        if(not self.__queue__):
            self.__queue__.append(state.timestamp)
        else:
            self.__queue__.append(state.timestamp)
            count=len(self.__queue__)
            if(count>=self.count_limit):
                oldest_timestamp = self.__queue__[0]
                # if less than period specified, count was exceeded
                if ((state.timestamp - oldest_timestamp) <= self.target_period):
                    count_exceeded=True
                    await self.__trigger_event__(f"event count >= {self.count_limit}")
                # remove oldest and wait for next triggered
                self.__queue__.pop(0)
        return count_exceeded
