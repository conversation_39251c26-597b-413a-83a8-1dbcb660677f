import asyncio
import pytest
import pytest_asyncio
import docker
import os
import pendulum
import redis.asyncio as redis
from testcontainers.core.container import DockerContainer
from testcontainers.core.waiting_utils import wait_for_logs

try:
    from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
except ImportError:
    from Logging_Utility.logging_config import setup_logging

try:
    from BromptonPythonUtilities.TSDB_Utility.tsdb_utils import (
        get_tsdb_data_async, store_in_tsdb_async, Aggregate, BucketSize
    )
except ImportError:
    from TSDB_Utility.tsdb_utils import (
        get_tsdb_data_async, store_in_tsdb_async, Aggregate, BucketSize
    )

logger = setup_logging()

# Ensure pytest-asyncio uses a dedicated loop for async tests
@pytest_asyncio.fixture(scope="function")
def event_loop():
    """Create and maintain event loop for each test"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    try:
        # Cancel all tasks
        pending = asyncio.all_tasks(loop)
        if pending:
            loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
        # Shutdown asyncgens
        loop.run_until_complete(loop.shutdown_asyncgens())
    finally:
        asyncio.set_event_loop(None)
        loop.close()

# Calculate start and end times for tests - last 10 days
now = pendulum.now()
TEST_END = int(now.timestamp() * 1000)
TEST_START = int(now.subtract(days=10).timestamp() * 1000)

class TestTsdbAsyncIntegration:
    REDIS_PORT = 6379
    REDIS_IMAGE = "redis/redis-stack-server:7.4.0-v0"

    @pytest.fixture(scope="class")
    def setup_redis_container(self):
        """Start Redis container with provided dump.rdb"""
        dump_path = os.path.abspath("TSDB_Utility/tests/data/dump.rdb")
        dump_dir = os.path.dirname(dump_path)

        container = (
            DockerContainer(self.REDIS_IMAGE)
            .with_name('redis_test_async')
            .with_exposed_ports(self.REDIS_PORT)
            .with_volume_mapping(dump_dir, '/data')
            .with_command("redis-stack-server --dir /data --save '' --rdb-save-incremental-fsync yes")
        )
        container.start()
        wait_for_logs(container, "Ready to accept connections")
        try:
            yield container
        finally:
            container.stop()
            try:
                docker_client = docker.from_env()
                for c in docker_client.containers.list(all=True):
                    if c.name == 'redis_test_async':
                        c.remove(force=True)
            except Exception:
                pass

    @pytest_asyncio.fixture(scope="function")
    async def setup_redis_connection_async(self, setup_redis_container, event_loop):
        """Create async Redis connection to container"""
        mapped_port = setup_redis_container.get_exposed_port(self.REDIS_PORT)

        # Create an async Redis client with the current event loop
        conn = await redis.Redis.from_url(
            f"redis://localhost:{mapped_port}",
            encoding="utf-8",
            decode_responses=False,
            socket_timeout=30,
            socket_connect_timeout=30
        ).initialize()
        if not await conn.ping():
            raise RuntimeError("Could not connect to Redis")
            
        try:
            yield conn
        finally:
            if not event_loop.is_closed():
                await conn.close()
            await asyncio.sleep(0)  # Allow connection cleanup to complete

    @pytest.mark.asyncio
    async def test_read_dump_data_async(self, setup_redis_connection_async, event_loop):
        """Test reading preloaded data from dump.rdb"""
        meas_info = {10102: {"data_type": "REAL"}}
        data = await get_tsdb_data_async(
            conn=setup_redis_connection_async,
            meas_info=meas_info,
            agg=Aggregate.AVG,
            agg_period=BucketSize._5M,
            start=TEST_START,
            end=TEST_END
        )
        assert len(data) > 0, "No data returned for async range query"

    @pytest.mark.asyncio
    async def test_write_and_read_async(self, setup_redis_connection_async, event_loop):
        """Test writing new data and reading it back asynchronously"""
        current_time = int(pendulum.now().timestamp() * 1000)
        test_data = [
            ("10102", current_time, 123.45, "REAL"),
            ("10102", current_time + 1000, 67.89, "REAL")
        ]
        success, failures = await store_in_tsdb_async(setup_redis_connection_async, test_data)
        assert success, f"Write async failed: {failures}"

        results = await get_tsdb_data_async(
            conn=setup_redis_connection_async,
            meas_info={10102: {"data_type": "REAL"}},
            start=current_time - 1000,
            end=current_time + 2000
        )
        assert 10102 in results, "Expected key 10102 in results"
        values = [
            float(val) if not isinstance(val, bytes) else float(val.decode())
            for ts, val in results[10102]
        ]
        assert any(abs(v - 123.45) < 1e-3 for v in values), "First value not found"
        assert any(abs(v - 67.89) < 1e-3 for v in values), "Second value not found"
