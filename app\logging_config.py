
import logging
from pythonjsonlogger import jsonlogger
from opentelemetry.trace import get_current_span
from app.middleware import customer_id_context, alert_id_context


class TraceLoggingFilter(logging.Filter):
    def filter(self, record):
        # Get the current span from OpenTelemetry
        span = get_current_span()
        if span and span.get_span_context():
            record.trace_id = hex(span.get_span_context().trace_id)[2:].zfill(32)
            record.span_id = hex(span.get_span_context().span_id)[2:].zfill(16)
        else:
            # Default values if no span context is available
            record.trace_id = "00000000000000000000000000000000"
            record.span_id = "0000000000000000"
        
        record.alert_id = alert_id_context.get()
        record.customer_id = customer_id_context.get()
        return True

def setup_logging():
    """Set up global logging with JSON formatter and OpenTelemetry tracing."""
    # Get the root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # Create a stream handler
    handler = logging.StreamHandler()

    # Define a JSON formatter
    json_formatter = jsonlogger.JsonFormatter(
        fmt="%(asctime)s %(name)s %(levelname)s %(trace_id)s %(span_id)s %(customer_id)s %(alert_id)s %(message)s"
    )
    handler.setFormatter(json_formatter)

    # Add the custom filter to the handler
    handler.addFilter(TraceLoggingFilter())

    # Clear existing handlers and set the new handler
    root_logger.handlers.clear()
    root_logger.addHandler(handler)
