from datetime import datetime
from typing import Union, Optional

from app.enums import CompareOperation, LimitState


class LimitCheckResult:
    def __init__(self, alert_id: int, measurement_id:int, timestamp: datetime, state: str, limit: Union[float, int, bool], comparator: str,
                 input_value: Union[float, int, bool],deadband: Union[float, int, bool], 
                 aggregate:str, period:str, asset_id: int):
        self.alert_id = alert_id
        self.measurement_id = measurement_id
        self.timestamp = timestamp
        self.state = state
        self.limit = limit
        self.comparator = comparator
        self.input_value = input_value
        self.deadband = deadband
        self.aggregate = aggregate
        self.period = period
        self.asset_id = asset_id


def check_limit(
        alert_id: int,
        measurement_id: int,
        limit: Union[float, int, bool],
        deadband: Union[float, int],
        comparator: CompareOperation,
        timestamp: datetime,
        input_value: Union[float, int, bool],
        aggregate:str,
        period:str,
        asset_id: int
) -> Optional[
    LimitCheckResult]:
    if input_value is None or limit is None or deadband is None:
        return None

    if not isinstance(input_value, (float, int, bool)):
        raise ValueError("Input passed is not a number or bool")

    new_state = None
    if isinstance(input_value, bool):
        if input_value == limit:
            new_state = LimitState.EXCEEDED
    elif comparator == CompareOperation.EQ:
        if input_value == limit:
            new_state = LimitState.EXCEEDED
    elif comparator == CompareOperation.GT:
        if input_value > limit:
            new_state = LimitState.EXCEEDED
        elif input_value <= (limit - deadband):
            new_state = LimitState.NORMAL
    elif comparator == CompareOperation.GE:
        if input_value >= limit:
            new_state = LimitState.EXCEEDED
        elif input_value <= (limit - deadband):
            new_state = LimitState.NORMAL
    elif comparator == CompareOperation.LT:
        if input_value < limit:
            new_state = LimitState.EXCEEDED
        elif input_value >= (limit + deadband):
            new_state = LimitState.NORMAL
    elif comparator == CompareOperation.LE:
        if input_value <= limit:
            new_state = LimitState.EXCEEDED
        elif input_value >= (limit + deadband):
            new_state = LimitState.NORMAL

    return LimitCheckResult(
        alert_id=alert_id,
        measurement_id=measurement_id,
        timestamp=timestamp,
        state=new_state.name if new_state else None,
        limit=limit,
        comparator=comparator.name,
        input_value=input_value,
        deadband=deadband,
        aggregate= aggregate,
        period= period,
        asset_id= asset_id
    )
