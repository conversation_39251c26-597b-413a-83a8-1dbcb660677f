from datetime import datetime
from typing import Union, Optional

from app.enums import CompareOperation, LimitState


class LimitCheckResult:
    """
    Result object for limit checking operations.

    Contains all necessary information for database insertion and state tracking
    after a limit check operation has been performed.

    Attributes:
        alert_id: Unique identifier for the alert
        measurement_id: Unique identifier for the measurement
        timestamp: When the check was performed
        state: Current state (NORMAL, EXCEEDED, DEAD, STALE)
        limit: The threshold value that was checked against
        comparator: Comparison operation used (GT, GE, LT, LE, EQ)
        input_value: The actual measurement value
        deadband: Hysteresis value to prevent oscillation
        aggregate: Aggregation type used (avg, max, min, etc.)
        period: Time period for aggregation (1min, 5min, etc.)
        asset_id: Unique identifier for the asset
    """

    def __init__(self, alert_id: int, measurement_id: int, timestamp: datetime, state: str,
                 limit: Union[float, int, bool], comparator: str,
                 input_value: Union[float, int, bool], deadband: Union[float, int, bool],
                 aggregate: str, period: str, asset_id: int):
        """
        Initialize LimitCheckResult with all required parameters.

        Args:
            alert_id: Unique identifier for the alert
            measurement_id: Unique identifier for the measurement
            timestamp: When the check was performed
            state: Current state (NORMAL, EXCEEDED, DEAD, STALE)
            limit: The threshold value that was checked against
            comparator: Comparison operation used (GT, GE, LT, LE, EQ)
            input_value: The actual measurement value
            deadband: Hysteresis value to prevent oscillation
            aggregate: Aggregation type used (avg, max, min, etc.)
            period: Time period for aggregation (1min, 5min, etc.)
            asset_id: Unique identifier for the asset
        """
        self.alert_id = alert_id
        self.measurement_id = measurement_id
        self.timestamp = timestamp
        self.state = state
        self.limit = limit
        self.comparator = comparator
        self.input_value = input_value
        self.deadband = deadband
        self.aggregate = aggregate
        self.period = period
        self.asset_id = asset_id


def check_limit(
        alert_id: int,
        measurement_id: int,
        limit: Union[float, int, bool],
        deadband: Union[float, int],
        comparator: CompareOperation,
        timestamp: datetime,
        input_value: Union[float, int, bool],
        aggregate: str,
        period: str,
        asset_id: int
) -> Optional[LimitCheckResult]:
    """
    Check if a measurement value exceeds defined limits (NOMINAL alert processing).

    Performs threshold comparison using the specified comparison operation and applies
    deadband logic to prevent oscillation between states. This is the core logic
    for NOMINAL alerts that trigger based on measurement values crossing thresholds.

    Args:
        alert_id: Unique identifier for the alert being checked
        measurement_id: Unique identifier for the measurement
        limit: Threshold value to compare against
        deadband: Hysteresis value to prevent state oscillation
        comparator: Comparison operation (GT, GE, LT, LE, EQ)
        timestamp: When the measurement was taken
        input_value: The measurement value to check
        aggregate: Aggregation type used (avg, max, min, etc.)
        period: Time period for aggregation (1min, 5min, etc.)
        asset_id: Unique identifier for the asset

    Returns:
        LimitCheckResult if check was successful, None if invalid input

    Raises:
        ValueError: If input_value is not a number or boolean
    """
    if input_value is None or limit is None or deadband is None:
        return None

    if not isinstance(input_value, (float, int, bool)):
        raise ValueError("Input passed is not a number or bool")

    new_state = None
    if isinstance(input_value, bool):
        if input_value == limit:
            new_state = LimitState.EXCEEDED
    elif comparator == CompareOperation.EQ:
        if input_value == limit:
            new_state = LimitState.EXCEEDED
    elif comparator == CompareOperation.GT:
        if input_value > limit:
            new_state = LimitState.EXCEEDED
        elif input_value <= (limit - deadband):
            new_state = LimitState.NORMAL
    elif comparator == CompareOperation.GE:
        if input_value >= limit:
            new_state = LimitState.EXCEEDED
        elif input_value <= (limit - deadband):
            new_state = LimitState.NORMAL
    elif comparator == CompareOperation.LT:
        if input_value < limit:
            new_state = LimitState.EXCEEDED
        elif input_value >= (limit + deadband):
            new_state = LimitState.NORMAL
    elif comparator == CompareOperation.LE:
        if input_value <= limit:
            new_state = LimitState.EXCEEDED
        elif input_value >= (limit + deadband):
            new_state = LimitState.NORMAL

    return LimitCheckResult(
        alert_id=alert_id,
        measurement_id=measurement_id,
        timestamp=timestamp,
        state=new_state.name if new_state else None,
        limit=limit,
        comparator=comparator.name,
        input_value=input_value,
        deadband=deadband,
        aggregate= aggregate,
        period= period,
        asset_id= asset_id
    )
