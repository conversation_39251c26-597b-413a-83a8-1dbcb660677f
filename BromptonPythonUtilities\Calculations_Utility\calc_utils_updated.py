from typing import List, Dict, Tuple, Union, Optional, Any
from collections import namedtuple
import re
import js2py
import numpy as np
import pandas as pd
# Fix SQLAlchemy import issue with minimal change
try:
    from sqlalchemy import Engine, Table, select, and_
except ImportError:
    from sqlalchemy import Table, select, and_
    # Use Any as a fallback type for Engine
    Engine = Any
    
try:
    from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
except ImportError:
    from Logging_Utility.logging_config import setup_logging
    
logger = setup_logging()

columns = ['meas_id', 'calc_tag', 'input_label', 'comment', 'input_id', 'constant_number', 'constant_string',
           'input_datasource',
           'expression', 'template_id', 'input_tag', 'datasource','poll_period','data_type']


RowTuple=namedtuple('RowTuple',columns)

class ResponseError(Exception):
    def __init__(self, message):
        super().__init__(message)

def get_poll_in_minutes(period:str):
    if(period.endswith("min")):
        return int(period[:-3])
    elif(period.endswith("hr")):
        return 60*int(period[:-2])
    elif(period.endswith("day")):
        return 24*60*int(period[:-3])
    elif(period=='DAILY'):
        return 24*60
    else:
        return 7*24*60  # Weekly

def get_offline_calcs(hook)->Dict[str,Dict[int,List[Tuple[int,str]]]]:
    offline_cals_qry = f"""
        select 
            coalesce(p.value,'5min') poll_period,
            cust_id,
            output_measurement,
            data_type
        from calculation_instance i 
        join v_measurements m 
            on i.ispersisted=true 
            and i.output_measurement=m.id
        left join calculation_period p 
            on p.id=i.poll_period
    """
    calcs = {}  # {poll_minutes->{cust_id:[output measurements]}

    # connect to db
    cur = None
    try:
        conn = hook if hasattr(hook, 'cursor') else hook.get_conn()
        logger.info("Fetching offline calculations from database")
        cur = conn.cursor()
        cur.execute(offline_cals_qry)
        rows = cur.fetchall()
        for row in rows:
            poll_period=row[0]
            if(not poll_period in calcs):
                calcs[poll_period]={}
            if(not row[1] in calcs[poll_period]):
                calcs[poll_period][row[1]]=[]
            calcs[poll_period][row[1]].append((row[2],row[3]))
    except Exception as e:
        logger.exception("Error occurred while fetching offline calculations.", exc_info=True)
        raise ResponseError(f"Database error: {e}") from e
    finally:
        if (cur):
            cur.close()
    return calcs

# SQL related constants
V_CALCULATIONS_TABLE = "v_calculations"

def get_calcs_graph(
    connection: Union[Engine, object],
    ids: List[int],
    table: Optional[Table] = None
) -> Tuple[Dict[int, Dict], Dict[int, Dict], List[List[int]]]:
    """
    Get calculation graph metadata.
    Args:
        connection: Either a database hook (with cursor() method) or SQLAlchemy engine
        ids: List of calculation IDs to fetch
        table: SQLAlchemy Table object (required when using SQLAlchemy engine)
    Returns:
        Tuple containing:
        - calc_nodes: Dict of calculation nodes
        - meas_nodes: Dict of measurement nodes
        - calc_layers: List of calculation layers
    """
    calc_nodes = {}
    meas_nodes = {}
    calc_nodes_remaining = [int(id) for id in ids]
    calc_layers = []
    requested_ids = set(calc_nodes_remaining)  # Keep track of originally requested IDs
    
    # Determine if we're using SQLAlchemy or direct cursor
    using_sqlalchemy = not hasattr(connection, 'cursor')
    if using_sqlalchemy and table is None:
        raise ValueError("Table parameter is required when using SQLAlchemy engine")

    while calc_nodes_remaining:
        if calc_nodes_remaining:  # protect from empty list when only one layer exists
            calc_layers.append(calc_nodes_remaining)
        calc_nodes_to_read = [node for node in calc_nodes_remaining if not(node in calc_nodes)]
        
        if calc_nodes_to_read:
            try:
                # Get rows using appropriate database access method
                if using_sqlalchemy:
                    with connection.connect() as conn:
                        clauses = [table.c.meas_id.in_([id for id in calc_nodes_to_read])]
                        cursor = conn.execute(select(table).where(and_(*clauses)))
                        rows = cursor.fetchall()
                else:
                    # Using cursor-based access
                    id_filter = ','.join([str(id) for id in calc_nodes_to_read])
                    offline_cals_qry = f"select {','.join(columns)} from v_calculations where meas_id in ({id_filter})"
                    cur = connection.cursor()
                    try:
                        cur.execute(offline_cals_qry)
                        logger.info("Fetching calculation metadata for IDs", extra={"ids": id_filter})
                        rows = cur.fetchall()
                        rows = [RowTuple(*row) for row in rows]
                    finally:
                        if not using_sqlalchemy:
                            cur.close()
                
                if not rows:
                    missing_ids = [id for id in calc_nodes_to_read]
                    raise ValueError(f"Calculations not found in database: {missing_ids}")

                found_ids = set()
                calc_nodes_remaining = []

                for row in rows:
                    if not using_sqlalchemy:
                        row = RowTuple(*row)
                    found_ids.add(row.meas_id)
                    if not row.meas_id in calc_nodes:
                        calc_nodes[row.meas_id] = {'tag': row.calc_tag, 'inputs': {}}
                    
                    # Handle interpolation with enhanced logic
                    interpolate_value = ('fill' if row.input_datasource == 4
                                       else 'linear' if not (row.input_datasource in (None, 0, 3))
                                       else None)
                    
                    calc_nodes[row.meas_id]['inputs'][row.input_label.strip()] = {
                        'comment': row.comment,
                        'id': row.input_id,
                        'constant': row.constant_number if row.constant_number is not None else row.constant_string,
                        'interpolate': interpolate_value
                    }
                    
                    # Handle uses_factors flag
                    if not 'uses_factors' in calc_nodes[row.meas_id] and row.input_datasource == 4:
                        calc_nodes[row.meas_id]['uses_factors'] = True
                        
                    # Add expression and metadata if not present
                    if not 'expression' in calc_nodes[row.meas_id]:
                        calc_nodes[row.meas_id].update({
                            'expression': row.expression,
                            'template_id': row.template_id,
                            'poll_period': getattr(row, 'poll_period', None),
                            'data_type': getattr(row, 'data_type', None)
                        })
                    # Handle dependencies
                    if row.input_id and row.datasource and row.input_datasource == row.datasource:  # if input is calc
                        if not row.input_id in calc_nodes_remaining:
                            calc_nodes_remaining.append(row.input_id)
                    elif row.input_id:
                        if not row.input_id in meas_nodes:
                            meas_nodes[row.input_id] = {'tag': row.input_tag, "calcs": {}}  # list of calcs that use it
                        meas_nodes[row.input_id]['calcs'].update({row.meas_id: row.input_label.strip()})

                # Check for missing IDs
                missing_ids = set(calc_nodes_to_read) - found_ids
                if missing_ids:
                    raise ValueError(f"Calculations not found in database: {missing_ids}")

            except Exception as e:
                logger.exception("Error occurred while building calculation graph.", exc_info=True)
                raise e

        else:
            break

    # Final check for any originally requested IDs that weren't found
    missing_ids = requested_ids - set(calc_nodes.keys())
    if missing_ids:
        raise ValueError(f"Calculations not found in database: {missing_ids}")

    return calc_nodes, meas_nodes, calc_layers

# Cache for calculated functions to avoid repeated parsing
calc_cache = {}
pattern = r"\$[A-Z]"

def parse_args(expression: str) -> List[str]:
    """
    Extract arguments from a calculation expression.
    
    Args:
        expression: String containing the calculation expression
    
    Returns:
        List of extracted arguments
    """
    return re.findall(pattern, expression)

def get_func(expression: str) -> tuple:
    """
    Convert a JS expression into a vectorized NumPy function.
    
    Args:
        expression: JavaScript expression to evaluate
    
    Returns:
        Tuple containing:
        - np_func: NumPy universal function
        - inputs: List of input variables
    """
    inputs = list(set(parse_args(expression)))
    # define function
    f = f"function f({','.join(inputs)}) {{return {expression}}}"
    fun = js2py.eval_js(f)
    np_func = np.frompyfunc(fun.__call__, len(inputs), 1)
    return np_func, inputs

def get_input_arrays(inputs: List[str], df: pd.DataFrame) -> List[np.ndarray]:
    """
    Convert input columns from DataFrame to NumPy arrays.
    
    Args:
        inputs: List of input variable names
        df: DataFrame containing input data
    
    Returns:
        List of NumPy arrays for each input
    """
    # evaluate for cases
    all_inputs = []  # each input in its own np.array
    # put arguments in order
    for j, l in enumerate(inputs):
        if (len(all_inputs) < (j + 1)):
            all_inputs.append([])
        all_inputs[j] = df[l].to_numpy()
    return all_inputs

def perform_vectorized_calc(
    tsdb_data: Dict[int, List[Union[float, Any]]],
    calc_nodes: Dict[int, Dict[str, Any]],
    layers_calc: List[List[int]]
) -> Dict[int, Union[List, Exception]]:
    """
    Perform vectorized calculations on time series data based on calculation layers.
    
    Args:
        tsdb_data: Dictionary mapping measurement IDs to time series data points
        calc_nodes: Dictionary of calculation nodes with their metadata
        layers_calc: List of calculation layers to process
    
    Returns:
        Dictionary mapping calculation IDs to their results or exceptions
    """
    # Loop through each layer in reverse order
    ret = {}
    for layer in reversed(layers_calc):
        for calc in layer:
            # Create df of inputs and then expand constants
            df_list = []
            constants = {}
            input_missing = False
            
            for label, input in calc_nodes[calc]['inputs'].items():
                if input['id']:
                    if input['id'] in tsdb_data:
                        vts = tsdb_data[input['id']]
                        # Ignore bad results
                        if vts is None or isinstance(vts, Exception) or (isinstance(vts, list) and len(vts) == 0):
                            input_missing = True
                        elif not(isinstance(vts, list)):  # Case of current value
                            df = pd.DataFrame([vts], columns=['ts', label])
                        else:
                            df = pd.DataFrame(vts, columns=['ts', label])
                    elif input['id'] in ret:  # Get from calculation from previous layers
                        if isinstance(ret[input['id']], Exception):
                            input_missing = True
                        else:
                            df = pd.DataFrame(ret[input['id']], columns=['ts', label])
                    else:
                        input_missing = True
                    
                    if input_missing:
                        ret[calc] = ValueError(f"Missing calculation input {input['id']}")
                        break
                    
                    df.set_index('ts', inplace=True)
                    df_list.append(df)
                else:
                    constants[label] = input['constant']
            
            if input_missing:
                continue

            # Concatenate DataFrames and avoid unnecessary copies
            df_all = pd.concat(df_list, axis=1, copy=False)

            # Add constants to df without creating extra copies
            for label, constant in constants.items():
                df_all[label] = constant

            # Interpolate only if there are NaN values
            if df_all.isnull().values.any():
                df_all.sort_index(inplace=True)
                for col in df_all.columns:  # Interpolate if so required
                    interp_method = calc_nodes[calc]['inputs'][col].get('interpolate', None)
                    if interp_method == 'linear':
                        df_all[col] = df_all[col].interpolate(method='index')
                    elif interp_method == 'fill':
                        df_all[col] = df_all[col].ffill()
                
                # Special handling when inputs are calculations with different time indexes
                if not df_all.empty and df_all.dropna().empty:
                    index_col = None
                    min_dt = 0
                    rows = 0
                    for col in df_all.columns:
                        df_view = df_all[df_all[col].notnull()].copy()
                        df_view.sort_index(inplace=True)
                        df_view.reset_index(inplace=True)
                        df_view['dt'] = df_view['ts'] - df_view['ts'].shift()
                        min_dt_col = df_view['dt'].min() if not df_view.empty and df_view.shape[0] > 1 else float('inf')
                        rows_col = df_view.shape[0]
                        if min_dt == 0 or (min_dt_col <= min_dt and rows_col > rows):
                            min_dt = min_dt_col
                            index_col = col
                            rows = rows_col
                    
                    # Interpolate other columns based on best time series
                    for col in df_all.columns:
                        if col == index_col:
                            continue
                        df_all[col] = df_all[col].interpolate(method='index')

                # Drop remaining NaN values
                df_all.dropna(inplace=True)
            
            try:
                # Use cached calculation if possible
                if calc_nodes[calc]['template_id'] not in calc_cache:
                    np_fun, inputs_list = get_func(calc_nodes[calc]['expression'])
                    calc_cache[calc_nodes[calc]['template_id']] = (np_fun, inputs_list)
                else:
                    np_fun, inputs_list = calc_cache[calc_nodes[calc]['template_id']]
                
                inputs_arrays = get_input_arrays(inputs_list, df_all)
                
                # Perform the calculation vectorized
                df_all[calc] = np_fun(*inputs_arrays)
                
                # Efficiently create result list
                ret[calc] = list(zip(df_all.index.tolist(), df_all[calc].tolist()))

            except Exception as e:
                ret[calc] = Exception(f"Exception of type {type(e)} in calc: {e}")

    return ret
