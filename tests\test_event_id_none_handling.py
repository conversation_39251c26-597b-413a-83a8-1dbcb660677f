import pytest
from unittest.mock import patch, MagicMock
from app.tasks import evaluate_alert_task_async
from app.rabbitmq.rabbitmq_service import send_rabbitmq_notification
import logging
from fastapi.testclient import TestClient
from app.main import app
from app.dependencies import JWTAuth

# Mock JWTAuth dependency for all tests
@pytest.fixture(autouse=True)
def patch_jwt_auth():
    with patch('app.dependencies.JWTAuth', return_value=lambda: None):
        yield

@pytest.fixture(autouse=True)
def patch_jwt_auth_call():
    with patch.object(JWTAuth, "__call__", return_value=None):
        yield

class MockResult:
    def __init__(self, alert_id=123, state='EXCEEDED', timestamp=1234567890, measurement_id=1, input_value='10', limit=5.0, comparator=1, deadband=0.5, aggregate=None, period=None, asset_id=1):
        self.alert_id = alert_id
        self.state = state
        self.timestamp = timestamp
        self.measurement_id = measurement_id
        self.input_value = input_value
        self.limit = limit
        self.comparator = comparator
        self.deadband = deadband
        self.aggregate = aggregate
        self.period = period
        self.asset_id = asset_id

@pytest.fixture
def client():
    return TestClient(app)

@pytest.fixture(autouse=True)
def reset_rabbitmq_singleton():
    from app.rabbitmq import rabbitmq_service
    rabbitmq_service.RabbitMQConnectionManager._instance = None
    yield
    rabbitmq_service.RabbitMQConnectionManager._instance = None

@pytest.mark.asyncio
def test_evaluate_alert_task_async_event_id_none(monkeypatch, caplog):
    # Mock dependencies
    mock_task = MagicMock()
    mock_task.customer_id = 1
    mock_task.alert_ids = [1]
    mock_task.aggregate = 'max'
    mock_task.aggregate_period = '2hr'

    # Patch fetch_alerts_by_ids to return a mock alert config
    mock_alert_config = MagicMock()
    mock_alert_config.id = 1
    mock_alert_config.measurement_id = 1
    mock_alert_config.threshold_value = 10
    mock_alert_config.reset_deadband = 0.5
    mock_alert_config.comparison_enum = MagicMock(condition='GE')
    mock_alert_config.asset_id = 1
    mock_alert_config.agg = None
    mock_alert_config.period = None
    mock_alert_config.threshold_type_enum = MagicMock(threshold='NOMINAL')  # Use NOMINAL to match the code path
    monkeypatch.setattr('app.tasks.fetch_alerts_by_ids', lambda ids: [mock_alert_config])

    # Patch DB session to avoid any real DB access
    mock_db = MagicMock()
    mock_alert = MagicMock()
    mock_alert.id = 1
    mock_alert.state = 'NORMAL'
    mock_db.query.return_value.filter.return_value.first.return_value = mock_alert
    mock_db.query.return_value.options.return_value.filter.return_value.all.return_value = [mock_alert]
    monkeypatch.setattr('app.db.database.get_db', lambda: iter([mock_db]))

    # Patch fetch_alert_state in db_service to avoid DB error log
    prev_state = MagicMock()
    prev_state.name = 'NORMAL'
    monkeypatch.setattr('app.db.db_service.fetch_alert_state', lambda alert_id: prev_state)

    # Patch fetch_last_processed_timestamp to return an int (not a MagicMock)
    monkeypatch.setattr('app.db.db_service.fetch_last_processed_timestamp', lambda alert_id: 0)

    # Patch read_data_new to return mock data (async)
    async def mock_read_data_new(**kwargs):
        return {1: {'status': 'success', 'timestamp': 1234567890, 'value': 42}}
    monkeypatch.setattr('app.tasks.read_data_new', mock_read_data_new)

    # Patch check_limit to return a result with a state change
    class Result:
        def __init__(self):
            self.alert_id = 1
            self.state = 'EXCEEDED'
            self.timestamp = 1234567890
            self.measurement_id = 1
            self.input_value = 42
            self.limit = 10
            self.comparator = 'GE'
            self.deadband = 0.5
            self.aggregate = None
            self.period = None
            self.asset_id = 1
    monkeypatch.setattr('app.tasks.check_limit', lambda **kwargs: Result())

    # Patch fetch_alert_state to return a previous state different from result.state
    prev_state = MagicMock()
    prev_state.name = 'NORMAL'
    monkeypatch.setattr('app.tasks.fetch_alert_state', lambda alert_id: prev_state)

    # Patch insert_event to return None (simulate DB failure)
    monkeypatch.setattr('app.tasks.insert_event', lambda **kwargs: None)

    # Patch send_rabbitmq_notification only
    with patch('app.tasks.send_rabbitmq_notification') as mock_notify:
        import asyncio
        with caplog.at_level(logging.INFO):
            asyncio.run(evaluate_alert_task_async(mock_task))
        mock_notify.assert_not_called()
    assert 'Event ID is None for Alert ID: 1, notification will not be sent.' in caplog.text


def test_send_rabbitmq_notification_event_id_present(reset_rabbitmq_singleton):
    result = MockResult()
    mock_channel = MagicMock()
    with patch('app.rabbitmq.rabbitmq_service.logger') as mock_logger, \
         patch('app.rabbitmq.rabbitmq_service.RabbitMQConnectionManager.__new__') as mock_new:
        mock_manager = MagicMock()
        mock_manager.get_channel.return_value = mock_channel
        mock_manager.close = MagicMock()
        mock_new.return_value = mock_manager
        send_rabbitmq_notification(result, 42)
        mock_logger.info.assert_any_call(f"RabbitMQ alert sent: alert_id={result.alert_id}, event_id=42")
        mock_manager.get_channel.assert_called()
        mock_channel.basic_publish.assert_called_once()
        mock_manager.close.assert_not_called()


def test_send_rabbitmq_notification_event_id_none(reset_rabbitmq_singleton):
    result = MockResult()
    with patch('app.rabbitmq.rabbitmq_service.logger') as mock_logger, \
         patch('app.rabbitmq.rabbitmq_service.RabbitMQConnectionManager.__new__') as mock_new:
        mock_manager = MagicMock()
        mock_manager.get_channel = MagicMock()
        mock_manager.close = MagicMock()
        mock_new.return_value = mock_manager
        send_rabbitmq_notification(result, None)
        mock_logger.info.assert_any_call(f'Event ID is None for Alert ID: {result.alert_id}, notification will not be sent.')
        mock_manager.get_channel.assert_not_called()


def test_send_rabbitmq_notification_returns_true_on_success(reset_rabbitmq_singleton):
    result = MockResult()
    mock_channel = MagicMock()
    with patch('app.rabbitmq.rabbitmq_service.logger') as mock_logger, \
         patch('app.rabbitmq.rabbitmq_service.RabbitMQConnectionManager.__new__') as mock_new:
        mock_manager = MagicMock()
        mock_manager.get_channel.return_value = mock_channel
        mock_manager.close = MagicMock()
        mock_new.return_value = mock_manager
        ret = send_rabbitmq_notification(result, 42)
        assert ret is True
        mock_logger.info.assert_any_call(f"RabbitMQ alert sent: alert_id={result.alert_id}, event_id=42")
        mock_manager.get_channel.assert_called()
        mock_channel.basic_publish.assert_called_once()
        mock_manager.close.assert_not_called()


def test_send_rabbitmq_notification_returns_false_on_event_id_none(reset_rabbitmq_singleton):
    result = MockResult()
    with patch('app.rabbitmq.rabbitmq_service.logger') as mock_logger, \
         patch('app.rabbitmq.rabbitmq_service.RabbitMQConnectionManager.__new__') as mock_new:
        mock_manager = MagicMock()
        mock_manager.get_channel = MagicMock()
        mock_manager.close = MagicMock()
        mock_new.return_value = mock_manager
        ret = send_rabbitmq_notification(result, None)
        assert ret is False
        mock_logger.info.assert_any_call(f'Event ID is None for Alert ID: {result.alert_id}, notification will not be sent.')
        mock_manager.get_channel.assert_not_called()


def test_send_test_alert_endpoint_returns_notification_not_sent(client):
    # This test assumes you have a FastAPI test client fixture named 'client'
    payload = {
        "alert_id": 1,
        "input_value": 99,
        "state": "EXCEEDED",
        "timestamp": 1720000000,
        "comparator": ">",
        "measurement_id": 100,
        "limit": 200,
        "deadband": 10,
        "aggregate": "AVG",
        "period": "5m",
        "asset_id": 50,
        "event_id": None
    }
    response = client.post("/send-test-alert", json=payload)
    assert response.status_code == 200
    assert response.json()["status"] == "Notification not sent: event_id is None"
    assert response.json()["alert_id"] == 1

def test_send_test_alert_endpoint_returns_alert_sent(client):
    # This test assumes you have a FastAPI test client fixture named 'client'
    payload = {
        "alert_id": 1,
        "input_value": 99,
        "state": "EXCEEDED",
        "timestamp": 1720000000,
        "comparator": ">",
        "measurement_id": 100,
        "limit": 200,
        "deadband": 10,
        "aggregate": "AVG",
        "period": "5m",
        "asset_id": 50,
        "event_id": 1000
    }
    with patch('app.rabbitmq.rabbitmq_service.send_rabbitmq_notification', return_value=True):
        response = client.post("/send-test-alert", json=payload)
        assert response.status_code == 200
        assert response.json()["status"] == "Alert sent to RabbitMQ"
        assert response.json()["event_id"] == 1000
        assert response.json()["alert_id"] == 1
