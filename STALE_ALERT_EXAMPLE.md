# 📡 Stale Alert Detection – Developer Guide

This document explains how **Stale alerts** are evaluated, common issues obsrved in the current system, and how to correctly implement and debug Stale alert behavior.

---

## 📌 Stale Alert Configuration

A Stale alert is triggered based on the following configuration:

| Parameter          | Value         |
| ------------------ | ------------- |
| Aggregation        | `avg`       |
| Aggregation Period | `5 minutes` |
| Stale Duration     | `6 minutes` |

---

## 🧠 Detection Logic (How It Works)

- Every **minute**, the system calculates the **rolling average of the last 5 minutes** of data.
- If this **5-minute average remains unchanged for 6 consecutive 1-minute intervals**, the measurement is considered **STALE**.
- A **STALE event is triggered** at the 6th minute.
- Once the average **changes**, a **NORMAL event is triggered** to indicate the measurement is no longer stale.

> 🔁 This logic includes **0** as a valid value. If `avg = 0` for 6 minutes, a **STALE alert must be raised**.

---

## ✅ Example: Value = 0 Case

```text
Input (1-min interval):
[0, 0, 0, 0, 0, 0, 0, 0, 0, 1, ...]

Rolling Averages:
min 4 → avg(0-4) = 0  
min 5 → avg(1-5) = 0  
min 6 → avg(2-6) = 0  
min 7 → avg(3-7) = 0  
min 8 → avg(4-8) = 0  
min 9 → avg(5-9) = 0 ✅ 6 unchanged → STALE triggered

→ min 10: value = 1 → avg changes → NORMAL triggered
```

---

## ✅ Real Dataset Result

Given the following values (1-min intervals):

```text
[0,0,0,0,0,0,0,0,0,1,2,3,5,6,7,8,
10,10,10,10,10,10,10,10,10,10,
12,12,12,12,12,12,12,12,12,12,12,
20,
0,0,0,0,0,0,0,0,0,
21,22,10,15,17,19,21,
33,33,33,33,33,33,33,33,33,33,
40,41,42,43,21]
```

The expected alert **events** are:

| Minute | State  | Input Value |
| ------ | ------ | ----------- |
| 9      | STALE  | 0           |
| 10     | NORMAL | 1           |
| 25     | STALE  | 10          |
| 26     | NORMAL | 12          |
| 44     | STALE  | 0           |
| 45     | NORMAL | 21          |
| 63     | STALE  | 33          |
| 64     | NORMAL | 40          |

---

## 📈 Excursion Table

An **excursion** is created when a STALE alert is followed by a NORMAL alert.

### Expected Excursions:

| Start Minute | End Minute | State | Input Value |
| ------------ | ---------- | ----- | ----------- |
| 9            | 10         | STALE | 0           |
| 25           | 26         | STALE | 10          |
| 44           | 45         | STALE | 0           |
| 63           | 64         | STALE | 33          |

---

## ❗ Common Issues Observed

- ❌ STALE not triggering when average is 0 (mistaken as "no data")
- ❌ No event inserted even when stale condition is met
- ❌ Event inserted, but excursion is not recorded due to missing NORMAL
- ❌ Excursion has negative duration or incorrect start/end time

---

## 🛠️ Developer Checklist

✅ Do NOT skip `0` as a value — it's valid and can be stale✅ Ensure 5-min avg is computed for every minute✅ Track last 6 average values — if all equal, trigger STALE✅ Reset stale state on avg change and emit NORMAL✅ Log and verify:

- Timestamp
- Aggregated value
- Detected stale state

---

## ✅ Testing Recommendation

Add test cases for:

- Flat 0 values → expect STALE
- Constant non-zero values → expect STALE
- Changing values → expect NORMAL after STALE
- Missing NORMAL → no excursion → bug
- Negative duration → excursion logic bug

---

Data Input used as example in Json format with time interval of 1 min

{
  "values": [0,0,0,0,0,0,0,0,0,1,2,3,5,6,7,8,10,10,10,10,10,10,10,10,10,10,12,12,12,12,12,12,12,12,12,12,12,20,0,0,0,0,0,0,0,0,0,21,22,10,15,17,19,21,33,33,33,33,33,33,33,33,33,33,40,41,42,43,21]
}

value 0 can be either actual input value as 0 or there is no data in measurement
