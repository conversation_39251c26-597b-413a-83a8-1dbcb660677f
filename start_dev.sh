#!/bin/bash

# Export environment variables
export ENV=dev
export USE_REDIS_SENTINEL=true
export KUBECONFIG=/Users/<USER>/.kube/k8s-mario-config

# Initialize and update git submodule
echo "Initializing BromptonPythonUtilities submodule..."
if [ ! -d "BromptonPythonUtilities" ]; then
    git submodule init
    git submodule update
    if [ ! -d "BromptonPythonUtilities" ]; then
        git clone https://github.com/bromptonenergy/BromptonPythonUtilities.git
    fi
fi

# Update PYTHONPATH to include BromptonPythonUtilities
export PYTHONPATH=$PYTHONPATH:.:$(pwd)/BromptonPythonUtilities

# Install required Python packages
echo "Installing required Python packages..."
pip install -r requirements.txt
pip install python-json-logger opentelemetry-sdk

# Function to check if a port is in use
check_port() {
    local port=$1
    nc -z localhost $port > /dev/null 2>&1
    return $?
}

# Function to wait for Redis to be available
wait_for_redis() {
    local port=$1
    echo "Waiting for Redis to be available on port $port..."
    while ! redis-cli -p $port ping > /dev/null 2>&1; do
        sleep 1
    done
    echo "Redis is available on port $port!"
}

# Start port forwarding for Redis nodes and sentinels
PORT_FORWARD_PIDS=()

# Node 0
if ! check_port 30379; then
    echo "Setting up Redis node-0 port forwarding on port 30379..."
    kubectl -n redis port-forward pod/redis-node-0 30379:6379 &
    PORT_FORWARD_PIDS+=($!)
fi

if ! check_port 32379; then
    echo "Setting up Redis sentinel node-0 port forwarding on port 32379..."
    kubectl -n redis port-forward pod/redis-node-0 32379:26379 &
    PORT_FORWARD_PIDS+=($!)
fi

# Node 1
if ! check_port 30380; then
    echo "Setting up Redis node-1 port forwarding on port 30380..."
    kubectl -n redis port-forward pod/redis-node-0 30380:6379 &
    PORT_FORWARD_PIDS+=($!)
fi

if ! check_port 32380; then
    echo "Setting up Redis sentinel node-1 port forwarding on port 32380..."
    kubectl -n redis port-forward pod/redis-node-0 32380:26379 &
    PORT_FORWARD_PIDS+=($!)
fi

# Node 2
if ! check_port 30381; then
    echo "Setting up Redis node-2 port forwarding on port 30381..."
    kubectl -n redis port-forward pod/redis-node-0 30381:6379 &
    PORT_FORWARD_PIDS+=($!)
fi

if ! check_port 32381; then
    echo "Setting up Redis sentinel node-2 port forwarding on port 32381..."
    kubectl -n redis port-forward pod/redis-node-0 32381:26379 &
    PORT_FORWARD_PIDS+=($!)
fi

# Wait for Redis to be available on the main port
wait_for_redis 30379

# Start FastAPI application
echo "Starting FastAPI application..."
uvicorn app.main:app --reload --port 8000 &
FASTAPI_PID=$!

# Start Celery worker
echo "Starting Celery worker..."
celery -A app.celery_app.celery_app worker --loglevel=info &
CELERY_PID=$!

# Function to cleanup processes on script exit
cleanup() {
    echo "Cleaning up processes..."
    [ -n "$FASTAPI_PID" ] && kill $FASTAPI_PID
    [ -n "$CELERY_PID" ] && kill $CELERY_PID
    for pid in "${PORT_FORWARD_PIDS[@]}"; do
        [ -n "$pid" ] && kill $pid
    done
}

# Set up trap to cleanup on script exit
trap cleanup EXIT

# Keep script running
echo "Development environment is ready!"
echo "Press Ctrl+C to stop all services"
wait