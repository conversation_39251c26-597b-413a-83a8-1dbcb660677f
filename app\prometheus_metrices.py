
from prometheus_client import Counter,  Gauge, Histogram

TASKS_CREATED = Counter('tasks_created_total', 'Total tasks created', ['task_name','status'])

TASKS_DELETED = Counter('tasks_deleted_total', 'Total tasks deleted', ['task_name','status'])

TASK_FAILURES = Counter('tasks_failures_total', 'Total task failures', ['task_name'])

ACTIVE_ALERTS = Gauge('active_alerts_total', 'Total active alerts', ['customer_id', 'aggregate', 'period'])

REDIS_CONNECTION_STATUS = Gauge('redis_connection_status', 'Redis connection health')

TASK_EXECUTION_TIME = Histogram('task_execution_time_seconds', 'Time taken to execute tasks', ['task_name'])

API_LATENCY = Histogram('api_request_latency_seconds', 'Latency of API requests', ['endpoint', 'method'])

EVALUATE_ALERT_OUTCOMES = Counter(
    'evaluate_alert_outcomes_total',
    'Total number of alert evaluations with outcomes',
    ['outcome']
)

EVALUATE_ALERT_STATE_CHANGED = Counter(
    'evaluate_alert_state_changed_total',
    'Total number of alerts where the state changed'
)
EVALUATE_ALERT_DURATION = Histogram(
    'evaluate_alert_duration_seconds',
    'Time taken to evaluate alerts'
)