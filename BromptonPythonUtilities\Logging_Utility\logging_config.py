import logging
from pythonjsonlogger import jsonlogger
from opentelemetry.trace import get_current_span, Span
# from api.app.middleware import CustomMiddleware, customer_id_context, asset_id_context, measurement_id_context


# Default trace values
DEFAULT_TRACE_ID = '00000000000000000000000000000000'
DEFAULT_SPAN_ID = '0000000000000000'

class CustomJsonFormatter(jsonlogger.JsonFormatter):
    def process_log_record(self, log_record):
        # Add timestamp in ISO format if not present
        if 'timestamp' not in log_record:
            log_record['timestamp'] = self.formatTime(record=None)
            
        # Process the record with default json formatter
        log_record = super().process_log_record(log_record)
        
        # Ensure trace fields are present
        log_record.setdefault('trace_id', DEFAULT_TRACE_ID)
        log_record.setdefault('span_id', DEFAULT_SPAN_ID)
        
        # Structure extra fields under a context object if they exist
        extra_fields = {k: v for k, v in log_record.items()
                       if k not in ('timestamp', 'name', 'levelname', 'pathname',
                                  'funcName', 'lineno', 'trace_id', 'span_id', 'message')}
        if extra_fields:
            log_record['context'] = extra_fields
            # Remove the fields from root level
            for k in extra_fields.keys():
                log_record.pop(k, None)
                
        return log_record

class TraceLoggingFilter(logging.Filter):
    def filter(self, record):
        # Get the current span from OpenTelemetry
        span: Span = get_current_span()
        span_context = span.get_span_context() if span else None

        if span_context and span_context.is_valid:
            # Set trace_id and span_id using valid span context
            record.trace_id = f"{span_context.trace_id:032x}"
            record.span_id = f"{span_context.span_id:016x}"
        else:
            # Default values if no span context is available
            record.trace_id = DEFAULT_TRACE_ID
            record.span_id = DEFAULT_SPAN_ID

        return True

def setup_logging(log_level='debug'):
    """Set up global logging with JSON formatter and OpenTelemetry tracing.
    
    Args:
        log_level: The logging level to use as string. Defaults to 'debug'
                  Valid values: 'debug', 'info', 'warning', 'error', 'critical'
                  If not specified, debug level will be used
    """
    # Convert string level to logging level
    level_map = {
        'debug': logging.DEBUG,
        'info': logging.INFO,
        'warning': logging.WARNING,
        'error': logging.ERROR,
        'critical': logging.CRITICAL
    }
    # Convert level string to lowercase and get numeric level (default to DEBUG)
    numeric_level = level_map.get(log_level.lower(), logging.DEBUG)
    
    # Get the root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Ensure propagation
    root_logger.propagate = True

    # Create a stream handler
    handler = logging.StreamHandler()
    # Set handler level to match logger level
    handler.setLevel(numeric_level)

    # Create formatter with base fields
    json_formatter = CustomJsonFormatter(
        fmt="%(timestamp)s %(name)s %(levelname)s %(pathname)s %(funcName)s %(lineno)d %(trace_id)s %(span_id)s %(message)s",
        timestamp=True,
        json_default=str
    )
    handler.setFormatter(json_formatter)

    # Add the custom filter to the handler
    handler.addFilter(TraceLoggingFilter())

    # Clear existing handlers and set the new handler
    root_logger.handlers.clear()
    root_logger.addHandler(handler)
    return root_logger