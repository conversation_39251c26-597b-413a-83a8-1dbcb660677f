from typing import Any, List
from paho.mqtt import client as mqtt_client
import threading
from google.protobuf.json_format import MessageToDict


try:
    import eclipse.tahu.sparkplug_b_pb2 as sparkplug_b_pb2
except ImportError:
    try:
        import be.BromptonPythonUtilities.eclipse.tahu.sparkplug_b_pb2 as sparkplug_b_pb2
    except ImportError:
        from ..eclipse.tahu import sparkplug_b_pb2 as sparkplug_b_pb2


try:
    from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
except ImportError:
    from Logging_Utility.logging_config import setup_logging

logger = setup_logging()


class MQTTSessionPublisher:

    @staticmethod
    def on_connect(client, userdata, flags, rc):
        if rc == 0:
            logger.info("Connected to MQTT Broker", extra={"host": client._host, "port": client._port})
        else:
            logger.error("Failed to connect", extra={"return_code": rc})

    def __init__(self, client_id: str, broker: str, port: int, mqtt_username: str, mqtt_password: str):
        self.client_id = client_id
        self.mqtt_username = mqtt_username
        self.mqtt_password = mqtt_password
        self.client = self.connect_mqtt(client_id, broker, port)


    def connect_mqtt(self, client_id: str, broker: str, port: int) -> mqtt_client.Client:
        client = mqtt_client.Client(client_id, clean_session=False)
        client.on_connect = MQTTSessionPublisher.on_connect
        client.username_pw_set(self.mqtt_username, self.mqtt_password)
        logger.info("Connecting MQTT client", extra={"client_id": client_id, "broker": broker, "port": port})
        client.connect(broker, port)
        return client

    def publish(self, topic: str, payload: Any = None, qos: int = 1, retain: bool = False):
        result = self.client.publish(topic, payload, qos=qos, retain=retain)
        if (result[0]):
            logger.error("Error publishing payload", extra={"error_code": result[0], "topic": topic})

    def publish_ncmd_for_rebirth(self,topics:List,now:int):
        # Send NCMD to get the tag definitions (Rebirth)
        payload = sparkplug_b_pb2.Payload()
        payload.timestamp = int(round(now * 1000))
        metric = payload.metrics.add()
        metric.name = "Node Control/Rebirth"
        metric.datatype = 11  # boolean
        metric.boolean_value = True
        byteArray = bytearray(payload.SerializeToString())
        logger.info("Sending NCMD command for rebirth", extra={"topics": topics})
        for topic in topics:
            self.publish(topic.replace('NDATA', 'NCMD'), byteArray, qos=0)

    def stop(self):
        self.client.disconnect()

class MQTTSessionListener:
    lock = threading.Lock()
    messages = {}

    @staticmethod
    def on_message(client, userdata, msg):
        # inboundPayload = sparkplug_b_pb2.Payload()
        # inboundPayload.ParseFromString(msg.payload)
        # client_id=client._client_id.decode()
        # metrics = MessageToDict(inboundPayload)['metrics']
        topic=msg.topic 
        try:
            inboundPayload = sparkplug_b_pb2.Payload()
            inboundPayload.ParseFromString(msg.payload)
            client_id=client._client_id.decode()
            metrics = MessageToDict(inboundPayload)['metrics']
        except Exception as e:
            logger.exception("Error parsing message",exc_info=e,extra={"topic":topic,"payload": msg.payload})
            return
        logger.debug("Received message", extra={"metrics": metrics})

        MQTTSessionListener.lock.acquire()
        try:
            if(not client_id in MQTTSessionListener.messages):
                MQTTSessionListener.messages[client_id]={}
            if(not topic in MQTTSessionListener.messages[client_id]):
                MQTTSessionListener.messages[client_id][topic]=[]
            MQTTSessionListener.messages[client_id][topic].extend(metrics)
        finally:
            MQTTSessionListener.lock.release()

    @staticmethod
    def on_connect(client, userdata, flags, rc):
        if rc == 0:
            logger.info("Connected to MQTT Broker", extra={"host": client._host, "port": client._port})
        else:
            logger.error("Failed to connect", extra={"return_code": rc})

    def __init__(self, client_id: str, broker: str, port: int, topics: List[str], mqtt_username: str, mqtt_password: str):
        self.client_id = client_id
        self.topics = topics
        self.mqtt_username = mqtt_username
        self.mqtt_password = mqtt_password
        self.client = self.connect_mqtt(client_id, broker, port)
        self.run()

    def run(self): # start, on separate thread, a mqtt_client subscribed to the topics of interest
        t = threading.Thread(target=self.client.loop_forever)
        t.start()

    def connect_mqtt(self, client_id: str, broker: str, port: int) -> mqtt_client.Client:
        global client
        client = mqtt_client.Client(client_id, clean_session=False)
        client.on_connect = MQTTSessionListener.on_connect
        client.on_message = MQTTSessionListener.on_message
        client.username_pw_set(self.mqtt_username, self.mqtt_password)
        logger.info("Connecting MQTT client", extra={"client_id": client_id, "broker": broker, "port": port})
        client.connect(broker, port)
        logger.info("Subscribing to topics", extra={"topics": self.topics})
        for topic in self.topics:
            client.subscribe(topic, qos=1)
        return client

    def stop(self):
        self.client.disconnect()

    def get_messages(self,client_id):
        MQTTSessionListener.lock.acquire()
        try:
            return MQTTSessionListener.messages.get(client_id,None)
        finally:
            MQTTSessionListener.lock.release()