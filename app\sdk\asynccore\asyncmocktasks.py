from .asynceventtasks import CompareOperation
from .asynctsdbreadertask import *
from typing import Dict,Union

# Changes values to simulate triggering limits
class AsyncMockDataReader(AsyncTSDBReaderTask):
    # is initialized with limit settings to simulate limit events
    def __init__(self,measurements:Dict[Tuple[str,int],List[Tuple[str,int]]],limits:Dict[int,List[Dict[str,Union[CompareOperation,float]]]],
                pattern=(2,3)): # 2 normals + 3 limit exceeded
        super().__init__(api="http://google.com",measurements=measurements,customer=0,aggregate=Aggregate.NONE,period=AggregatePeriod._1M)
        self.__pattern__=pattern
        self.__pattern_state__= []
        self.__limits__=[] # [(id,limit,comparator,deadband)]
        self.__normal_val__=[]
        self.__exceeded_val__=[]
        ids=[]
        for id,limit_list in limits.items():
            for limit in limit_list:
                if (not id in ids):
                    ids.append(id)
                else:
                    continue  # prevents conflicting settings for each meas_id (only simulates first limit found)
                self.__limits__.append((id,limit['limit'],limit['comparator'],limit['deadband']))
                # start at normal
                if(limit['comparator'] in [CompareOperation.GE,CompareOperation.GT]):
                    self.__normal_val__.append(limit['limit'] - limit['deadband'])
                    self.__exceeded_val__.append(limit['limit']+1)
                elif(limit['comparator'] in [CompareOperation.LE,CompareOperation.LT]):
                    self.__normal_val__.append(limit['limit'] + limit['deadband'])
                    self.__exceeded_val__.append(limit['limit']-1)
                else:
                    self.__normal_val__.append(limit['limit'] + 1)  # anything but the limit
                    self.__exceeded_val__.append(limit['limit'])
                self.__pattern_state__.append((1,0))  # normal,limit exceeded

    # Overriden method
    async def __execute__(self, state:EventState, *args, **kwargs):
        parallel_asyncs=[]
        ret=[]
        for i,limit in enumerate(self.__limits__): # (id,limit,comparator,deadband)
            context=(int(limit[0]))
            output_state=EventState()
            output_state.context=context
            output_state.source=self.__event_descriptive_contexts__[context]
            output_state.timestamp=datetime.now().timestamp()
            normal_cnt=self.__pattern_state__[i][0]
            limit_exceeded_cnt=self.__pattern_state__[i][1]
            if(normal_cnt>0):
                normal_cnt+=1
                if (normal_cnt > self.__pattern__[0]):
                    output_state.state = self.__exceeded_val__[i]
                    self.__pattern_state__[i]=(0,1)
                else:
                    self.__pattern_state__[i]=(normal_cnt,0)
                    output_state.state = self.__normal_val__[i]
            elif(limit_exceeded_cnt>0):
                limit_exceeded_cnt+=1
                if (limit_exceeded_cnt > self.__pattern__[1]):
                    output_state.state = self.__normal_val__[i]
                    self.__pattern_state__[i] = (1,0)
                else:
                    self.__pattern_state__[i]=(0,limit_exceeded_cnt)
                    output_state.state = self.__exceeded_val__[i]
            parallel_asyncs.append(self.__trigger_event__(context, output_state))
            ret.append(output_state)
        await asyncio.gather(*parallel_asyncs, return_exceptions=False)
        return ret
