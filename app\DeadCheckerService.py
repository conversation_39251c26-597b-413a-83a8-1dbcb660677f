from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Union
from app.enums import LimitState, CompareOperation
from app.LimitCheckerService import LimitCheckResult
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging

logger = setup_logging()

def check_dead_measurement(
    alert_id: int,
    measurement_id: int,
    now: datetime,
    last_seen: Optional[datetime],
    dead_duration_seconds: int,
    aggregate: str,
    period: str,
    asset_id: int,
    comparator_id: int,
    current_value: Optional[Union[str, int, float]],
    last_known_value: Optional[float] = None
) -> Optional[LimitCheckResult]:
    """
    Determines if a measurement should be marked DEAD based on absence of data.
    Returns a LimitCheckResult with compatible types for DB insertion.
    """
    # Keep everything in milliseconds for consistent comparison
    now_ms = now if isinstance(now, (int, float)) else int(now.timestamp() * 1000)  # Ensure milliseconds
    last_seen_ms = last_seen if last_seen else None  # Already in milliseconds from TSDBReaderService
    duration_ms = dead_duration_seconds  # Already in milliseconds from tasks.py
    
    # Calculate time difference if we have last seen data
    time_diff_ms = None
    if last_seen_ms is not None:
        time_diff_ms = now_ms - last_seen_ms
    
    # Determine state based on time difference
    if last_seen_ms is None or time_diff_ms > duration_ms:
        new_state = LimitState.DEAD
        if last_seen_ms is None:
            logger.info(f"[DEAD] Alert {alert_id}: No data available")
        else:
            logger.info(f"[DEAD] Alert {alert_id}: Data age exceeds {duration_ms/1000:.0f}s threshold")
    else:
        new_state = LimitState.NORMAL
        logger.info(f"[DEAD] Alert {alert_id}: Data within threshold")

    # Handle input value based on state and available data
    if current_value is not None:
        input_value = current_value
    elif new_state == LimitState.DEAD and last_known_value is not None:
        input_value = last_known_value
        logger.info(f"[DEAD] Alert {alert_id}: Using last known value {last_known_value}")
    else:
        input_value = 0.0
        logger.info(f"[DEAD] Alert {alert_id}: No data available, using 0")

    return LimitCheckResult(
        alert_id=alert_id,
        measurement_id=measurement_id,
        timestamp=now,
        state=new_state.name,
        limit=0,  # Not required for dead alerts
        comparator=comparator_id,
        input_value=input_value,
        deadband=0,  # Required to be 0 for dead alerts
        aggregate=aggregate,
        period=period,
        asset_id=asset_id
    )
