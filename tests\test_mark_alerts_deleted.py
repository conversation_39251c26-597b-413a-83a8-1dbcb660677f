import pytest
from unittest.mock import MagicMock
from datetime import datetime, timezone

from app.db.db_service import mark_alerts_deleted

@pytest.fixture
def mock_db(monkeypatch):
    mock_session = MagicMock()
    monkeypatch.setattr('app.db.db_service.database.get_db', lambda: iter([mock_session]))
    return mock_session

def test_mark_alerts_deleted_success(mock_db):
    # Setup
    alert_ids = [1, 2, 3]
    user_id = 42
    # Call
    mark_alerts_deleted(alert_ids, user_id)
    # Assert update called with correct fields
    mock_db.query().filter().update.assert_called_once()
    update_args, update_kwargs = mock_db.query().filter().update.call_args
    update_dict = update_args[0]
    assert update_dict['deleted_by'] == user_id
    # deleted_at should be a datetime with tzinfo=timezone.utc
    assert isinstance(update_dict['deleted_at'], datetime)
    assert update_dict['deleted_at'].tzinfo == timezone.utc
    assert mock_db.commit.called

def test_mark_alerts_deleted_exception(mock_db):
    alert_ids = [1]
    user_id = 99
    # Simulate exception on update
    mock_db.query().filter().update.side_effect = Exception("DB error")
    with pytest.raises(Exception):
        mark_alerts_deleted(alert_ids, user_id)
    assert mock_db.rollback.called
