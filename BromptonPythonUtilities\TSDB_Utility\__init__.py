# Original exports
from .tsdb_utils import get_tsdb_data, store_in_tsdb, Aggregate, BucketSize

# Optional async support
from .tsdb_utils import get_tsdb_data_async, store_in_tsdb_async  # noqa: F401

__all__ = ['get_tsdb_data', 'store_in_tsdb', 'Aggregate', 'BucketSize']

# Optional async exports - these won't break existing code even if redis.asyncio is not available
__all__ += ['get_tsdb_data_async', 'store_in_tsdb_async']