from sqlalchemy import Column, Integer, String, ForeignKey, Float, DateTime, Boolean
from sqlalchemy.orm import relationship, declarative_base

Base = declarative_base()

class Aggregates(Base):
    __tablename__ = 'aggregates'

    id = Column(Integer, primary_key=True, autoincrement=True)
    label = Column(String, nullable=True)
    value = Column(String, nullable=True)

class Periods(Base):
    __tablename__ = 'periods'

    id = Column(Integer, primary_key=True, autoincrement=True)
    label = Column(String, nullable=True)
    value = Column(String, nullable=True)
    sort_order = Column(Integer, nullable=True)


class AlertCondition(Base):
    __tablename__ = 'alert_condition'

    id = Column(Integer, primary_key=True, autoincrement=True)
    condition = Column(String, nullable=True)

class AlertFilterType(Base):
    __tablename__ = 'alert_filter_type'

    id = Column(Integer, primary_key=True, autoincrement=True)
    filter_type = Column(String, nullable=True)

class AlertFilterUnit(Base):
    __tablename__ = 'alert_filter_unit'

    id = Column(Integer, primary_key=True, autoincrement=True)
    filter_unit = Column(String, nullable=True)

class AlertThresholdType(Base):
    __tablename__ = 'alert_threshold_type'

    id = Column(Integer, primary_key=True, autoincrement=True)
    threshold = Column(String, nullable=True)

class Measurement(Base):
    __tablename__ = 'measurement'

    id = Column(Integer, primary_key=True, autoincrement=True)

class Asset(Base):
    __tablename__ = 'asset'

    id = Column(Integer, primary_key=True, autoincrement=True)
#
# class AlertUsers(Base):
#     __tablename__ = 'alert_users'
#
#     id = Column(Integer, primary_key=True, autoincrement=True)
#     user = Column(Integer, ForeignKey('user.id'), nullable=False)
#     alert = Column(Integer, ForeignKey('alerts.id'), nullable=False)
#     notificationtype = Column(Integer, nullable=False)
#
#     # Relationships
#     user_rel = relationship("User", foreign_keys=[user])
#     alert_rel = relationship("Alerts", foreign_keys=[alert])



class Alerts(Base):
    __tablename__ = 'alerts'

    id = Column(Integer, primary_key=True, autoincrement=True)
    asset_id = Column(Integer, nullable=False)
    measurement_id = Column(Integer, nullable=False)
    agg = Column(Integer, ForeignKey('aggregates.id'), nullable=False)
    period = Column(Integer, ForeignKey('periods.id'), nullable=False)
    threshold_type = Column(Integer, ForeignKey('alert_threshold_type.id'), nullable=False)
    condition = Column(Integer, ForeignKey('alert_condition.id'), nullable=False)
    threshold_value = Column(Float, nullable=False)
    reset_deadband = Column(Float, nullable=False)
    description = Column(String(150), nullable=True)
    notification_type = Column(Integer, nullable=True)
    customer_id = Column(Integer, nullable=True)
    state = Column(String, nullable=True)
    enabled = Column(Boolean, nullable=False)
    deleted_by = Column(Integer, nullable=True)
    deleted_at = Column(DateTime, nullable=True)
    stale_band = Column(Float, nullable=True)  # Optional stale band range for stale alerts
    last_processed_ts = Column(Integer, nullable=True)  # Last processed timestamp (ms since epoch)

    # Relationships
    # asset = relationship("Asset", foreign_keys=[asset_id])
    # measurement = relationship("Measurement", foreign_keys=[measurement_id])
    aggregate_enum = relationship("Aggregates", foreign_keys=[agg])
    aggregate_period_enum = relationship("Periods", foreign_keys=[period])
    threshold_type_enum = relationship("AlertThresholdType", foreign_keys=[threshold_type])
    comparison_enum = relationship("AlertCondition", foreign_keys=[condition])


class Events(Base):
    __tablename__ = 'events'
    id = Column(Integer, primary_key=True, index=True)
    input_value = Column(String)
    deadband = Column(Float)
    timestamp =  Column(DateTime)
    state = Column(String, index=True)
    limit = Column(String, index=True)
    alert_id = Column(Integer, ForeignKey('alerts.id'), nullable=False)
    comparator = Column(Integer, ForeignKey('alert_condition.id'), nullable=False)
    aggregate = Column(Integer, ForeignKey('aggregates.id'), nullable=False)
    period = Column(Integer, ForeignKey('periods.id'), nullable=False)
    measurement_id = Column(Integer, ForeignKey('measurement.id'), nullable=False)
    asset_id = Column(Integer, ForeignKey('asset.id'), nullable=False)  

class ExcursionStates(Base):
    __tablename__ = 'excursion_stats'

    id = Column(Integer, primary_key=True, autoincrement=True)
    alert_id = Column(Integer, ForeignKey('alerts.id', ondelete='CASCADE'), nullable=False)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=False)
    min_value = Column(Float, nullable=False)
    max_value = Column(Float, nullable=False)
    avg_value = Column(Float, nullable=False)
    time_duration = Column(String, nullable=False)

    # Relationships
    alert = relationship("Alerts", foreign_keys=[alert_id])
