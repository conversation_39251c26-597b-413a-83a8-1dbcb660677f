"""
TSDB (Time Series Database) related enumerations.
These enums are used for managing time series data operations.
"""

from enum import Enum


class Aggregate(str, Enum):
    """Aggregation types for time series data."""
    def __str__(self):
        return self.value
        
    def __repr__(self):
        return f"{self.__class__.__name__}.{self.name}"
    NONE = 'none'         # No aggregation
    TOTAL = 'total'       # Sum total
    TWA = 'twa'           # Time-weighted average
    MAX = 'max'           # Maximum value
    MIN = 'min'          # Minimum value
    AVG = 'avg'          # Simple average
    STD = 'std.p'        # Population standard deviation
    RATETOTAL = 'ratetotal'  # Will check if the measurements are rates and compute the twa*factor 
                            # where factor=AggregatePeriod/ratedivisor
    DELTATWA = 'deltatwa'    # Delta of time-weighted average
    DELTAAVG = 'deltaavg'    # Delta of average
    DELTAMAX = 'deltamax'    # Delta of maximum
    DELTAMIN = 'deltamin'    # Delta of minimum
    DELTASTD = 'deltastd'    # Delta of standard deviation


class BucketSize(Enum):
    """Time bucket sizes for aggregation in milliseconds."""
    def __str__(self):
        return f"{self.__class__.__name__}.{self.name}"
        
    def __repr__(self):
        return f"{self.__class__.__name__}.{self.name}"
    _1M = 60 * 1000         # 1 minute
    _2M = 2 * 60 * 1000     # 2 minutes
    _5M = 5 * 60 * 1000     # 5 minutes
    _10M = 10 * 60 * 1000   # 10 minutes
    _15M = 15 * 60 * 1000   # 15 minutes
    _20M = 20 * 60 * 1000   # 20 minutes
    _30M = 30 * 60 * 1000   # 30 minutes
    _1H = 60 * 60 * 1000    # 1 hour
    _2H = 2 * 60 * 60 * 1000  # 2 hours
    _4H = 4 * 60 * 60 * 1000  # 4 hours
    _6H = 6 * 60 * 60 * 1000  # 6 hours
    _8H = 8 * 60 * 60 * 1000  # 8 hours
    _12H = 12 * 60 * 60 * 1000  # 12 hours
    DAILY = 24 * 60 * 60 * 1000  # 1 day
    WEEKLY = 7 * 24 * 60 * 60 * 1000  # 1 week
    _30d = 30 * 24 * 60 * 60 * 1000   # 30 days
    MONTHLY = 31 * 24 * 60 * 60 * 1000  # 1 month (31 days)