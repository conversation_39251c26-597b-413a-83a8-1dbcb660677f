"""
Performance tests for alert processing to ensure system scalability.
"""

import pytest
import time
from unittest.mock import MagicMock, patch
from datetime import datetime, timedelta

from app.DeadCheckerService import check_dead_measurement
from app.StaleCheckerService import check_stale_measurement
from app.LimitCheckerService import check_limit
from app.enums import CompareOperation, LimitState


class TestAlertPerformance:
    """Test performance characteristics of alert processing."""

    def test_dead_alert_processing_performance(self):
        """Test DEAD alert processing performance with multiple measurements."""
        start_time = time.time()
        
        # Process 100 dead alert checks
        for i in range(100):
            result = check_dead_measurement(
                alert_id=i,
                measurement_id=i,
                now=int(datetime.utcnow().timestamp() * 1000),
                last_seen=int((datetime.utcnow() - timedelta(minutes=10)).timestamp() * 1000),
                dead_duration_seconds=300000,  # 5 minutes in ms
                aggregate="avg",
                period="1min",
                asset_id=1,
                comparator_id=4,
                current_value=None,
                last_known_value=42.0
            )
            assert result is not None
            
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should process 100 alerts in less than 1 second
        assert processing_time < 1.0, f"Processing took {processing_time:.3f}s, expected < 1.0s"
        
        # Calculate alerts per second
        alerts_per_second = 100 / processing_time
        assert alerts_per_second > 100, f"Only {alerts_per_second:.1f} alerts/sec, expected > 100"

    @patch('app.StaleCheckerService.master')
    def test_stale_alert_processing_performance(self, mock_redis):
        """Test STALE alert processing performance with multiple measurements."""
        # Mock Redis responses
        mock_redis.get.side_effect = lambda key: {
            'measurement:1:last_value': '10.0',
            'measurement:1:last_changed': str((datetime.utcnow() - timedelta(minutes=20)).timestamp()),
            'measurement:1:initial_value': '10.0'
        }.get(key.split(':')[-1] if ':' in key else key)

        start_time = time.time()

        # Process 100 stale alert checks
        for i in range(100):
            result = check_stale_measurement(
                alert_id=i,
                measurement_id=1,
                now=datetime.utcnow(),
                last_changed=datetime.utcnow() - timedelta(minutes=20),
                stale_duration_minutes=15,
                aggregate="avg",
                period="1min",
                asset_id=1,
                comparator_id=4,
                comparison_op=CompareOperation.GT,
                input_val=10.0,
                stale_band=None
            )
            assert result is not None

        end_time = time.time()
        processing_time = end_time - start_time

        # Should process 100 alerts in less than 2 seconds (includes Redis mocking overhead)
        assert processing_time < 2.0, f"Processing took {processing_time:.3f}s, expected < 2.0s"

    def test_nominal_alert_processing_performance(self):
        """Test NOMINAL alert processing performance with multiple measurements."""
        start_time = time.time()

        # Process 100 nominal alert checks
        for i in range(100):
            result = check_limit(
                alert_id=i,
                measurement_id=i,
                limit=10.0,
                deadband=1.0,
                comparator=CompareOperation.GT,
                timestamp=datetime.utcnow(),
                input_value=15.0,
                aggregate="avg",
                period="1min",
                asset_id=1
            )
            assert result is not None

        end_time = time.time()
        processing_time = end_time - start_time

        # Should process 100 alerts in less than 0.5 seconds
        assert processing_time < 0.5, f"Processing took {processing_time:.3f}s, expected < 0.5s"

        # Calculate alerts per second (avoid division by zero)
        if processing_time > 0:
            alerts_per_second = 100 / processing_time
            assert alerts_per_second > 200, f"Only {alerts_per_second:.1f} alerts/sec, expected > 200"

    def test_mixed_alert_types_performance(self):
        """Test performance with mixed alert types processing."""
        start_time = time.time()
        
        with patch('app.StaleCheckerService.master') as mock_redis:
            # Mock Redis for stale alerts
            mock_redis.get.return_value = '10.0'
            
            # Process 300 mixed alerts (100 each type)
            for i in range(100):
                # DEAD alert
                dead_result = check_dead_measurement(
                    alert_id=i,
                    measurement_id=i,
                    now=int(datetime.utcnow().timestamp() * 1000),
                    last_seen=int((datetime.utcnow() - timedelta(minutes=10)).timestamp() * 1000),
                    dead_duration_seconds=300000,
                    aggregate="avg",
                    period="1min",
                    asset_id=1,
                    comparator_id=4,
                    current_value=None
                )
                
                # STALE alert
                stale_result = check_stale_measurement(
                    alert_id=i + 100,
                    measurement_id=i,
                    now=datetime.utcnow(),
                    last_changed=datetime.utcnow() - timedelta(minutes=20),
                    stale_duration_minutes=15,
                    aggregate="avg",
                    period="1min",
                    asset_id=1,
                    comparator_id=4,
                    comparison_op=CompareOperation.GT,
                    input_val=10.0,
                    stale_band=None
                )
                
                # NOMINAL alert
                nominal_result = check_limit(
                    alert_id=i + 200,
                    measurement_id=i,
                    limit=10.0,
                    deadband=1.0,
                    comparator=CompareOperation.GT,
                    timestamp=datetime.utcnow(),
                    input_value=15.0,
                    aggregate="avg",
                    period="1min",
                    asset_id=1
                )
                
                assert dead_result is not None
                assert stale_result is not None
                assert nominal_result is not None
                
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should process 300 mixed alerts in less than 3 seconds
        assert processing_time < 3.0, f"Processing took {processing_time:.3f}s, expected < 3.0s"
        
        # Calculate alerts per second
        alerts_per_second = 300 / processing_time
        assert alerts_per_second > 100, f"Only {alerts_per_second:.1f} alerts/sec, expected > 100"

    def test_memory_usage_stability(self):
        """Test that memory usage remains stable during alert processing."""
        import gc
        import sys
        
        # Force garbage collection before test
        gc.collect()
        
        # Get initial memory usage (approximate)
        initial_objects = len(gc.get_objects())
        
        # Process many alerts to test memory stability
        for batch in range(10):
            for i in range(50):
                result = check_limit(
                    alert_id=i + (batch * 50),
                    measurement_id=i,
                    limit=10.0,
                    deadband=1.0,
                    comparator=CompareOperation.GT,
                    timestamp=datetime.utcnow(),
                    input_value=15.0,
                    aggregate="avg",
                    period="1min",
                    asset_id=1
                )
                assert result is not None
            
            # Force garbage collection after each batch
            gc.collect()
        
        # Check final memory usage
        final_objects = len(gc.get_objects())
        
        # Memory growth should be minimal (less than 10% increase)
        memory_growth = (final_objects - initial_objects) / initial_objects
        assert memory_growth < 0.1, f"Memory grew by {memory_growth:.2%}, expected < 10%"

    def test_concurrent_processing_simulation(self):
        """Simulate concurrent alert processing to test thread safety."""
        import threading
        import queue
        
        results_queue = queue.Queue()
        errors_queue = queue.Queue()
        
        def process_alerts(start_id, count):
            """Process alerts in a separate thread."""
            try:
                for i in range(count):
                    result = check_limit(
                        alert_id=start_id + i,
                        measurement_id=i,
                        limit=10.0,
                        deadband=1.0,
                        comparator=CompareOperation.GT,
                        timestamp=datetime.utcnow(),
                        input_value=15.0,
                        aggregate="avg",
                        period="1min",
                        asset_id=1
                    )
                    results_queue.put(result)
            except Exception as e:
                errors_queue.put(e)
        
        # Create multiple threads to simulate concurrent processing
        threads = []
        for i in range(5):
            thread = threading.Thread(
                target=process_alerts,
                args=(i * 20, 20)  # Each thread processes 20 alerts
            )
            threads.append(thread)
        
        start_time = time.time()
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Check that no errors occurred
        assert errors_queue.empty(), f"Errors occurred during concurrent processing: {list(errors_queue.queue)}"
        
        # Check that all results were produced
        assert results_queue.qsize() == 100, f"Expected 100 results, got {results_queue.qsize()}"
        
        # Concurrent processing should complete in reasonable time
        assert processing_time < 2.0, f"Concurrent processing took {processing_time:.3f}s, expected < 2.0s"
