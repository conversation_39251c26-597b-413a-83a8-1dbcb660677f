import pytest
from app.enums import (
    Aggregate, BucketSize, BucketSize_Dict,
    ThresholdType, Comparison, AggregatePeriod,
    LimitState, CompareOperation, LimitSetting,
    CompareOperation_Dict
)

def test_aggregate_imports():
    """Test that Aggregate enum is properly imported from BromptonPythonUtilities"""
    assert Aggregate.NONE.value == 'none'
    assert Aggregate.TOTAL.value == 'total'
    assert Aggregate.TWA.value == 'twa'
    assert Aggregate.MAX.value == 'max'
    assert Aggregate.MIN.value == 'min'
    assert Aggregate.AVG.value == 'avg'
    assert Aggregate.STD.value == 'std.p'
    assert Aggregate.RATETOTAL.value == 'ratetotal'
    assert Aggregate.DELTATWA.value == 'deltatwa'
    assert Aggregate.DELTAAVG.value == 'deltaavg'
    assert Aggregate.DELTAMAX.value == 'deltamax'
    assert Aggregate.DELTAMIN.value == 'deltamin'
    assert Aggregate.DELTASTD.value == 'deltastd'

def test_bucket_size_imports():
    """Test that BucketSize enum is properly imported from BromptonPythonUtilities"""
    assert BucketSize._1M.value == 60 * 1000
    assert BucketSize._1H.value == 60 * 60 * 1000
    assert BucketSize.DAILY.value == 24 * 60 * 60 * 1000
    assert BucketSize.WEEKLY.value == 7 * 24 * 60 * 60 * 1000
    assert BucketSize.MONTHLY.value == 31 * 24 * 60 * 60 * 1000

def test_bucket_size_dict_compatibility():
    """Test backward compatibility of BucketSize_Dict"""
    # Test regular entries
    assert BucketSize_Dict['_1M'].value == 60 * 1000
    assert BucketSize_Dict['_1H'].value == 60 * 60 * 1000
    
    # Test hr suffix compatibility
    hour_mappings = [
        ('_1H', '_1hr'),
        ('_2H', '_2hr'),
        ('_4H', '_4hr'),
        ('_6H', '_6hr'),
        ('_8H', '_8hr'),
        ('_12H', '_12hr')
    ]
    
    for h_name, hr_name in hour_mappings:
        assert hr_name in BucketSize_Dict
        assert BucketSize_Dict[h_name].value == BucketSize_Dict[hr_name].value

def test_threshold_type_enum():
    """Test ThresholdType enum values"""
    assert ThresholdType.NOMINAL.value == 'NOMINAL'
    assert ThresholdType.HIGH.value == 'HIGH'
    assert ThresholdType.LOW.value == 'LOW'
    assert ThresholdType.STALE.value == 'STALE'
    assert ThresholdType.DEAD.value == 'DEAD'
    assert ThresholdType.ANOMALY.value == 'ANOMALY'  # Test for ANOMALY

def test_comparison_enum():
    """Test Comparison enum values"""
    assert Comparison.LT.value == 'LT'
    assert Comparison.LE.value == 'LE'
    assert Comparison.GT.value == 'GT'
    assert Comparison.GE.value == 'GE'
    assert Comparison.EQ.value == 'EQ'

def test_aggregate_period_enum():
    """Test AggregatePeriod enum values"""
    assert AggregatePeriod._1M.value == '1min'
    assert AggregatePeriod._1H.value == '1hr'
    assert AggregatePeriod.DAILY.value == 'DAILY'
    assert AggregatePeriod.NONE.value == 'none'
    assert AggregatePeriod.raw.value == 'raw'

def test_limit_state_enum():
    """Test LimitState enum values"""
    assert LimitState.NORMAL.value == 0
    assert LimitState.EXCEEDED.value == 1
    assert LimitState.STALE.value == 2
    assert LimitState.DEAD.value == 3

def test_compare_operation_enum():
    """Test CompareOperation enum and dictionary"""
    assert CompareOperation.EQ.value == 0
    assert CompareOperation.LT.value == 1
    assert CompareOperation.LE.value == 2
    assert CompareOperation.GT.value == 3
    assert CompareOperation.GE.value == 4
    
    # Test CompareOperation_Dict
    for op in CompareOperation:
        assert op.name in CompareOperation_Dict
        assert CompareOperation_Dict[op.name] == op

def test_limit_setting_enum():
    """Test LimitSetting enum values"""
    assert LimitSetting.LIMIT.value == 0
    assert LimitSetting.DEADBAND.value == 1

def test_aggregate_period_alignment():
    """Test alignment between AggregatePeriod and BucketSize names"""
    period_to_bucket = {
        '_1M': '_1M',
        '_2M': '_2M',
        '_5M': '_5M',
        '_10M': '_10M',
        '_15M': '_15M',
        '_20M': '_20M',
        '_30M': '_30M',
        '_1H': '_1H',
        '_2H': '_2H',
        '_4H': '_4H',
        '_6H': '_6H',
        '_8H': '_8H',
        '_12H': '_12H',
        'DAILY': 'DAILY',
        'WEEKLY': 'WEEKLY',
        'MONTHLY': 'MONTHLY'
    }
    
    for period_name, bucket_name in period_to_bucket.items():
        assert hasattr(AggregatePeriod, period_name)
        assert hasattr(BucketSize, bucket_name)