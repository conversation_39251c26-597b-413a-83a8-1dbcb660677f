# Alert Services Quick Reference

## 🚀 Quick Start

### Adding a New Alert Type

1. **Create Service Module**: `app/NewAlertCheckerService.py`
2. **Add to tasks.py**: Include in alert processing section
3. **Add Tests**: Create `tests/test_new_alert.py`
4. **Update Enums**: Add to `ThresholdType` enum if needed

### Processing Flow

```python
# In tasks.py - Alert Processing Section
if alert_config.threshold_type_enum.threshold == ThresholdType.YOUR_TYPE:
    result = check_your_alert(...)
    if result and current_state.name != prev_state.name:
        # Handle state change
        event_timestamp = _get_event_timestamp(ts, "YOUR_TYPE")
        event_input_value = _get_event_input_value("YOUR_TYPE", val, current_state.name)
        # Create event, send notifications, etc.
```

## 📊 Alert Types Cheat Sheet

| Type | Trigger | States | Key Config |
|------|---------|--------|------------|
| **NOMINAL** | Value crosses threshold | NORMAL ↔ EXCEEDED | `threshold_value`, `comparison_op`, `deadband` |
| **DEAD** | Data missing/old | NORMAL ↔ DEAD | `threshold_value` (minutes) |
| **STALE** | Value unchanged | NORMAL ↔ STALE | `threshold_value` (minutes), `stale_band` |
| **ANOMALY** | Statistical outlier | Not implemented | TBD |

## 🔧 Common Code Patterns

### Service Function Template

```python
def check_your_alert(
    alert_id: int,
    measurement_id: int,
    # ... other parameters
) -> Optional[LimitCheckResult]:
    """
    Brief description of what this alert checks.
    
    Args:
        alert_id: Unique identifier for the alert
        measurement_id: Unique identifier for the measurement
        # ... document all parameters
        
    Returns:
        LimitCheckResult if check successful, None otherwise
    """
    # Your logic here
    
    return LimitCheckResult(
        alert_id=alert_id,
        measurement_id=measurement_id,
        timestamp=timestamp,
        state=new_state.name,
        limit=limit_value,
        comparator=comparator_id,
        input_value=input_value,
        deadband=deadband_value,
        aggregate=aggregate,
        period=period,
        asset_id=asset_id
    )
```

### Event Creation Pattern

```python
# Standardized event creation in tasks.py
if current_state.name != prev_state.name:
    logger.info(f"State change for alert {alert_id}: {prev_state.name} -> {current_state.name}")
    update_alert_state(alert_id, current_state.name)
    
    # Use helper functions for consistency
    event_timestamp = _get_event_timestamp(ts, "ALERT_TYPE")
    event_input_value = _get_event_input_value("ALERT_TYPE", val, current_state.name)
    
    event_id = insert_event(
        timestamp=event_timestamp,
        input_value=event_input_value,
        # ... other parameters
    )
    
    # Standard logging format
    logger.info(
        f"[EVENT-INSERT] type=ALERT_TYPE event_id={event_id} alert_id={alert_id} "
        f"state={current_state.name} value={event_input_value} timestamp={event_timestamp}"
    )
    
    # Handle excursions and notifications
    if current_state.name == "NORMAL":
        insert_state_execution(alert_id, event_timestamp)
        logger.info(f"[EXCURSION-INSERT] alert_id={alert_id} end_time={event_timestamp}")
        
    if event_id is not None:
        send_rabbitmq_notification(result, event_id)
        logger.info(
            f"[NOTIFICATION-SENT] type=ALERT_TYPE event_id={event_id} alert_id={alert_id} "
            f"state={current_state.name} timestamp={event_timestamp}"
        )
```

## 🧪 Testing Patterns

### Basic Test Structure

```python
import pytest
from unittest.mock import MagicMock, patch
from app.YourCheckerService import check_your_alert

class TestYourAlert:
    """Test your alert type functionality."""
    
    @pytest.mark.parametrize("input_value,expected_state,description", [
        (5.0, "NORMAL", "Value within range"),
        (15.0, "EXCEEDED", "Value exceeds threshold"),
        # ... more test cases
    ])
    def test_your_alert_detection(self, input_value, expected_state, description):
        """Test alert detection with various inputs."""
        result = check_your_alert(
            alert_id=1,
            measurement_id=1,
            # ... test parameters
        )
        
        assert result is not None
        assert result.state == expected_state, description
```

### Integration Test Pattern

```python
@patch('app.db.db_service.insert_event')
@patch('app.rabbitmq.rabbitmq_service.send_rabbitmq_notification')
def test_your_alert_lifecycle(self, mock_notification, mock_insert_event):
    """Test complete alert lifecycle."""
    mock_insert_event.return_value = 123
    
    # Test state change from NORMAL to ALERT
    # Test state change from ALERT to NORMAL
    # Verify event creation, notifications, excursions
```

## 🔍 Debugging Guide

### Common Issues

**1. State Not Changing**
```python
# Check if comparison logic is correct
logger.debug(f"Comparing {input_value} {comparator} {limit} = {result}")
```

**2. Events Not Created**
```python
# Verify state change detection
logger.debug(f"Previous: {prev_state.name}, Current: {current_state.name}")
```

**3. Timestamp Issues**
```python
# Use helper function for consistency
event_timestamp = _get_event_timestamp(ts, "YOUR_TYPE")
logger.debug(f"Using timestamp: {event_timestamp}")
```

### Logging Best Practices

```python
# Use consistent log levels
logger.info("Normal operations")
logger.warning("Recoverable issues")
logger.error("Serious problems")
logger.debug("Detailed debugging info")

# Follow established log message formats
logger.info(f"[EVENT-INSERT] type=YOUR_TYPE event_id={event_id} ...")
logger.info(f"[EXCURSION-INSERT] alert_id={alert_id} ...")
logger.info(f"[NOTIFICATION-SENT] type=YOUR_TYPE event_id={event_id} ...")
```

## 📈 Performance Tips

### Optimization Guidelines

1. **Minimize Database Calls**: Batch operations when possible
2. **Use Helper Functions**: Leverage `_get_event_timestamp()` and `_get_event_input_value()`
3. **Efficient Comparisons**: Avoid unnecessary calculations
4. **Memory Management**: Don't store large objects unnecessarily

### Redis Usage (for STALE alerts)

```python
# Efficient Redis operations
def get_cached_value(measurement_id: int) -> Optional[float]:
    """Get value with proper error handling."""
    try:
        value = master.get(f"measurement:{measurement_id}:last_value")
        return float(value) if value is not None else None
    except (ValueError, TypeError, ConnectionError) as e:
        logger.warning(f"Redis error for measurement {measurement_id}: {e}")
        return None
```

## 🚨 Error Handling

### Standard Error Patterns

```python
def your_function(...) -> Optional[LimitCheckResult]:
    """Your function with proper error handling."""
    try:
        # Validate inputs
        if input_value is None:
            logger.warning(f"No input value for alert {alert_id}")
            return None
            
        # Your logic here
        
        return LimitCheckResult(...)
        
    except ValueError as e:
        logger.error(f"Invalid input for alert {alert_id}: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error in alert {alert_id}: {e}")
        return None
```

## 📚 Key Files Reference

| File | Purpose | Key Functions |
|------|---------|---------------|
| `tasks.py` | Main orchestrator | `evaluate_alert_task_async()` |
| `LimitCheckerService.py` | NOMINAL alerts | `check_limit()` |
| `DeadCheckerService.py` | DEAD alerts | `check_dead_measurement()` |
| `StaleCheckerService.py` | STALE alerts | `check_stale_measurement()` |
| `db_service.py` | Database operations | `insert_event()`, `insert_state_execution()` |
| `rabbitmq_service.py` | Notifications | `send_rabbitmq_notification()` |

## 🔄 Helper Functions

### Available in tasks.py

```python
# Timestamp standardization
_get_event_timestamp(ts: Optional[int], alert_type: str) -> int

# Input value standardization  
_get_event_input_value(alert_type: str, measurement_value: Optional[float], 
                      current_state: str, last_known_value: Optional[float] = None) -> float
```

## 📋 Checklist for New Features

- [ ] Service module created with proper docstrings
- [ ] Added to tasks.py alert processing section
- [ ] Helper functions used for timestamp/value handling
- [ ] Comprehensive unit tests written
- [ ] Integration tests added
- [ ] Performance tests included
- [ ] Error handling implemented
- [ ] Logging follows established patterns
- [ ] Documentation updated
- [ ] Code review completed

## 🎯 Quick Commands

```bash
# Run specific alert tests
pytest tests/test_nominal_alert.py -v
pytest tests/test_dead_alert.py -v  
pytest tests/test_stale_alert.py -v

# Run performance tests
pytest tests/test_alert_performance.py -v

# Run all alert-related tests
pytest tests/test_*alert*.py -v

# Check test coverage
pytest tests/ --cov=app --cov-report=term-missing

# Run with debugging output
pytest tests/test_your_test.py -v -s --log-cli-level=DEBUG
```
