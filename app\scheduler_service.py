import logging
import time
from redbeat import RedBeatSchedulerEntry
from app.redis import master
from app.celery_app import app as celery_app
from app.models import EvaluateAlertTask
from app.prometheus_metrices import TASKS_CREATED, TASKS_DELETED, TASK_EXECUTION_TIME, TASK_FAILURES
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
logger= setup_logging()
# logger = logging.getLogger(__name__)

async def delete_task(task_name):
    start_time = time.time()
    try:
        res = master.exists('redbeat:' + task_name)
        # logger.info(f"Task {task_name} found: {res}")
        if res:
            # entry = RedBeatSchedulerEntry.from_key('redbeat:' + task_name, app=celery_app)
            # Remove the entry from Redis
            master.delete('redbeat:' + task_name)
            TASKS_DELETED.labels(task_name=task_name, status="success").inc()
            logger.info(f"Task {task_name} deleted successfully")
        else:
            TASKS_DELETED.labels(task_name=task_name, status="not_found").inc()
            logger.info(f"Task {task_name} not found")
    except Exception as e:
        logger.error(f"Error deleting task {task_name}: {e}")
        TASKS_DELETED.labels(task_name=task_name, status="error").inc()
    finally:
        TASK_EXECUTION_TIME.labels(task_name=task_name).observe(time.time() - start_time)

async def create_task(task_name: str, task: EvaluateAlertTask, interval_seconds:int = 60):
    start_time = time.time()
    try:
        task_entry = RedBeatSchedulerEntry(
            name=task_name,
            task='app.tasks.evaluate_alert_task',
            schedule=interval_seconds,
            args=[task_name, task.json()],  # Serialize the AlertConfig object to JSON string and wrap it in a list
            app=celery_app,
            enabled=True,
        )
        logger.info(f"Task {task} created successfully")
        task_entry.save()
        TASKS_CREATED.labels(task_name=task_name, status="success").inc()
    except Exception as e:
        logger.exception(f"Error creating task {task_name}: {e}")
        TASKS_CREATED.labels(task_name=task_name, status="error").inc()
    finally:
        TASK_EXECUTION_TIME.labels(task_name=task_name).observe(time.time() - start_time)