# TSDB Utility

This utility provides common operations for working with Redis Time Series Database (TSDB). It includes functions for reading and writing time series data, supporting both numeric and non-numeric (string/boolean) data types.

## Installation

1. Add BromptonPythonUtilities as a submodule to your project:
```bash
git submodule add https://github.com/your-username/BromptonPythonUtilities.git
```

2. Install the required dependencies:
```bash
pip install -r BromptonPythonUtilities/requirements.txt
```

## Features

### Read Operations
Both synchronous (`get_tsdb_data`) and asynchronous (`get_tsdb_data_async`) operations are supported:
- Support for both numeric (stored in TimeSeries) and non-numeric data types (stored in Streams)
- Batch processing with default size of 50 entries
- Aggregation support with configurable bucket sizes
- Time-weighted average (TWA) option for lower density data via `lower_density` parameter
- Adjacent points inclusion via `include_adjacents` parameter
- Special handling for STRING and BOOLEAN types using Redis Streams

### Write Operations
Both synchronous (`store_in_tsdb`) and asynchronous (`store_in_tsdb_async`) operations are supported:
- Support for both time series (numeric) and stream (non-numeric) data types
- Efficient batch processing with configurable batch size (default: 50)
- Detailed error tracking and reporting with failed operations list
- Automatic key formatting with curly braces
- Special handling for STRING and BOOLEAN types using Redis Streams

## Usage Examples

### Reading Time Series Data

```python
from BromptonPythonUtilities.TSDB_Utility import (
    get_tsdb_data, get_tsdb_data_async,
    store_in_tsdb, store_in_tsdb_async,
    Aggregate, BucketSize
)

# Example measurement info
meas_info = {
    1: {"data_type": "NUMERIC"},
    2: {"data_type": "STRING"}
}

# Synchronous usage
with redis.Redis() as conn:
    # Simple read of numeric data
    data = get_tsdb_data(
        conn=conn,
        meas_info=meas_info,
        start=start_time,
        end=end_time
    )

# Asynchronous usage
async with redis.asyncio.Redis() as conn:
    # Simple read of numeric data
    data = await get_tsdb_data_async(
        conn=conn,
        meas_info=meas_info,
        start=start_time,
        end=end_time
    )

    # More examples of advanced usage:

    # Read with time-weighted average for lower density data
    twa_data = await get_tsdb_data_async(
        conn=conn,
        meas_info=meas_info,
        start=start_time,
        end=end_time,
        lower_density=True  # Uses TWA series if available
    )

    # Read with aggregation and bucket size
    agg_data = await get_tsdb_data_async(
        conn=conn,
        meas_info=meas_info,
        start=start_time,
        end=end_time,
        agg=Aggregate.TWA,  # Time-weighted average aggregation
        agg_period=BucketSize._1H  # 1-hour buckets
    )

    # Read with rate calculation
    rate_data = await get_tsdb_data_async(
        conn=conn,
        meas_info=meas_info,
        start=start_time,
        end=end_time,
        agg=Aggregate.RATETOTAL,  # Computes rate * time period
        agg_period=BucketSize.WEEKLY  # Weekly buckets
    )
```

### Writing Time Series Data

```python
# Prepare mixed data types for writing
time_value_pairs = [
    # Format: (key, timestamp, value, data_type)
    ("sensor1", current_time, 123.45, "NUMERIC"),    # Stored in TimeSeries
    ("status1", current_time, "active", "STRING")    # Stored in Stream
]

# Synchronous write with error handling
with redis.Redis() as conn:
    success, failures = store_in_tsdb(
        conn=conn,
        tvs=time_value_pairs,
        batch_size=50
    )

# Asynchronous write with error handling
async with redis.asyncio.Redis() as conn:
    success, failures = await store_in_tsdb_async(
        conn=conn,
        tvs=time_value_pairs,
        batch_size=50
    )

    # Example error handling
    if failures:
        logger.error("Failed TSDB operations:")
        for key, ts, val, dtype, error in failures:
            logger.error(f"- {key} ({dtype}): {error}")
            # Example error handling strategies:
            if "WRONGTYPE" in error:
                logger.error(f"Data type mismatch for {key}")
            elif "ERR TSDB" in error:
                logger.error(f"TimeSeries error for {key}")
            elif "Connection" in error:
                logger.error(f"Redis connection error for {key}")
```

## Data Types

### Aggregate Types
```python
class Aggregate(str, Enum):
    TWA = 'twa'           # Time-weighted average
    MAX = 'max'           # Maximum value
    MIN = 'min'          # Minimum value
    AVG = 'avg'          # Simple average
    STD = 'std.p'        # Population standard deviation
    RATETOTAL = 'ratetotal'  # Rate calculation (twa * factor where factor = AggregatePeriod/ratedivisor)
    DELTATWA = 'deltatwa'    # Delta of time-weighted average
    DELTAAVG = 'deltaavg'    # Delta of average
    DELTAMAX = 'deltamax'    # Delta of maximum
    DELTAMIN = 'deltamin'    # Delta of minimum
    DELTASTD = 'deltastd'    # Delta of standard deviation
```

### Time Bucket Sizes
```python
class BucketSize(Enum):
    _1M = 60 * 1000         # 1 minute
    _2M = 2 * 60 * 1000     # 2 minutes
    _5M = 5 * 60 * 1000     # 5 minutes
    _10M = 10 * 60 * 1000   # 10 minutes
    _15M = 15 * 60 * 1000   # 15 minutes
    _20M = 20 * 60 * 1000   # 20 minutes
    _30M = 30 * 60 * 1000   # 30 minutes
    _1H = 60 * 60 * 1000    # 1 hour
    _2H = 2 * 60 * 60 * 1000  # 2 hours
    _4H = 4 * 60 * 60 * 1000  # 4 hours
    _6H = 6 * 60 * 60 * 1000  # 6 hours
    _8H = 8 * 60 * 60 * 1000  # 8 hours
    _12H = 12 * 60 * 60 * 1000  # 12 hours
    DAILY = 24 * 60 * 60 * 1000  # 1 day
    WEEKLY = 7 * 24 * 60 * 60 * 1000  # 1 week
    _30d = 30 * 24 * 60 * 60 * 1000   # 30 days
    MONTHLY = 31 * 24 * 60 * 60 * 1000  # 1 month (31 days)
```

## Running Tests

The utility includes a comprehensive test suite. To run the tests:

### Prerequisites

Before running tests, make sure you have the following dependencies installed:

```bash
# Install test dependencies
pip install pytest pytest-mock pytest-asyncio pendulum docker testcontainers redis pandas
```

### Running Integration Tests

Both synchronous and asynchronous integration tests use Docker to run Redis instances with test data:

```bash
# From the project root directory (d:\Utilities)

# Run sync integration tests
python -m pytest BromptonPythonUtilities\TSDB_Utility\tests\test_integration.py -v

# Run async tests
python -m pytest BromptonPythonUtilities\TSDB_Utility\tests\test_async.py -v

# Run all tests
python -m pytest BromptonPythonUtilities\TSDB_Utility\tests\test_*.py -v

# Run specific tests
python -m pytest BromptonPythonUtilities\TSDB_Utility\tests\test_integration.py::TestTsdbIntegration::test_read_dump_data -v
python -m pytest BromptonPythonUtilities\TSDB_Utility\tests\test_async.py::TestTsdbAsyncIntegration::test_read_dump_data_async -v
```

Note: Async tests require pytest-asyncio package to be installed. It's included in the requirements.txt.

### Running All Tests with Coverage

To run all tests (including both sync and async) and generate a coverage report:

```bash
# Install pytest-cov if you don't have it
pip install pytest-cov

# Run all tests with coverage
python -m pytest BromptonPythonUtilities\TSDB_Utility\tests\ --cov=BromptonPythonUtilities.TSDB_Utility --cov-report=html

# Run sync tests with coverage
python -m pytest BromptonPythonUtilities\TSDB_Utility\tests\test_integration.py --cov=BromptonPythonUtilities.TSDB_Utility --cov-report=html

# Run async tests with coverage
python -m pytest BromptonPythonUtilities\TSDB_Utility\tests\test_async.py --cov=BromptonPythonUtilities.TSDB_Utility --cov-report=html

# View coverage report
# Open htmlcov/index.html in your browser
```

### Troubleshooting Test Issues

If you encounter issues running the tests:

1. **Docker unavailable**: Ensure Docker is running for integration tests
    ```bash
    docker --version
    docker ps
    ```

2. **Redis connection errors**: Check if ports are already in use
    ```bash
    # On Windows
    # For sync tests
    netstat -ano | findstr :6379
    # For async tests
    netstat -ano | findstr :6380
    
    # On Linux/macOS
    # For sync tests
    lsof -i :6379
    # For async tests
    lsof -i :6380
    ```

3. **Import errors**: Ensure you're running from the correct directory
    ```bash
    # Change to project root
    cd d:\Utilities
    
    # Set PYTHONPATH if needed
    $env:PYTHONPATH = "d:\Utilities"  # PowerShell
    set PYTHONPATH=d:\Utilities        # CMD
    export PYTHONPATH=d:/Utilities     # Linux/macOS
    ```

4. **Missing test data**: Ensure the test data directory exists
    ```
    BromptonPythonUtilities\TSDB_Utility\tests\data\dump.rdb
    ```

5. **Async test failures**: Common async-specific issues
    ```bash
    # Install pytest-asyncio if missing
    pip install pytest-asyncio

    # Check if redis-py version supports async
    python -c "import redis.asyncio; print(redis.__version__)"
    # Should be >= 4.5.0

    # Run async tests with -v for detailed output
    python -m pytest BromptonPythonUtilities\TSDB_Utility\tests\test_async.py -v

    # Event loop errors
    If you see errors like "Event loop is closed" or "Task got Future attached to a different loop":
    - Use function-scoped fixtures for event_loop and Redis connections
    - Ensure proper cleanup of Redis connections before event loop closure
    - Add event_loop fixture dependency to test functions
    - Example fixture setup:
    ```python
    @pytest_asyncio.fixture(scope="function")
    def event_loop():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        yield loop
        try:
            pending = asyncio.all_tasks(loop)
            if pending:
                loop.run_until_complete(asyncio.gather(*pending))
            loop.run_until_complete(loop.shutdown_asyncgens())
        finally:
            asyncio.set_event_loop(None)
            loop.close()

    @pytest_asyncio.fixture(scope="function")
    async def redis_connection(event_loop):
        conn = await redis.Redis.from_url(...).initialize()
        try:
            yield conn
        finally:
            if not event_loop.is_closed():
                await conn.close()
            await asyncio.sleep(0)  # Allow cleanup
    ```
    ```

6. **Container conflicts**: If both sync and async tests fail, check for leftover containers
    ```bash
    # List all containers including stopped ones
    docker ps -a

    # Remove test containers if they exist
    docker rm -f redis_test redis_test_async
    ```

### Test Configuration

You can adjust test parameters by setting environment variables:

```bash
# On Windows PowerShell
# For sync tests
$env:REDIS_TEST_PORT = "16379"
$env:REDIS_TEST_IMAGE = "redis/redis-stack-server:7.4.0-v0"

# For async tests (optional, defaults to sync port + 1)
$env:REDIS_ASYNC_TEST_PORT = "16380"
$env:REDIS_ASYNC_TEST_IMAGE = "redis/redis-stack-server:7.4.0-v0"

# On Windows CMD
# For sync tests
set REDIS_TEST_PORT=16379
set REDIS_TEST_IMAGE=redis/redis-stack-server:7.4.0-v0

# For async tests (optional)
set REDIS_ASYNC_TEST_PORT=16380
set REDIS_ASYNC_TEST_IMAGE=redis/redis-stack-server:7.4.0-v0

# Run test suites
python -m pytest BromptonPythonUtilities\TSDB_Utility\tests\test_integration.py -v
python -m pytest BromptonPythonUtilities\TSDB_Utility\tests\test_async.py -v

# Run all tests
python -m pytest BromptonPythonUtilities\TSDB_Utility\tests\ -v
```

Note:
- Async tests will automatically use sync port + 1 if no specific port is configured
- Both test suites can run simultaneously since they use different ports
- The same Redis image can be used for both sync and async tests
- For async tests:
  - Use function-scoped fixtures for better isolation
  - Always explicitly depend on event_loop in test functions
  - Handle connection cleanup carefully before event loop closes
  - Follow the pattern in test_async.py for reliable async testing

## Implementation Details

### Storage Strategy
- **Numeric Data**: Stored using Redis TimeSeries with automatic key formatting: `{measurement_id}`
- **Non-numeric Data**: Stored using Redis Streams with automatic key formatting: `{measurement_id}`
  - STRING and BOOLEAN values are stored with a "value" field in the stream
  - Stream entries use timestamp-based IDs: `{timestamp}-0`

### Performance Considerations
- Batch processing is used for both read and write operations (default: 50 items per batch)
- Time series aggregations are computed server-side for better performance
- For non-numeric types, aggregations are computed client-side when needed
- Lower density data can be accessed using the TWA series (when available)

### Error Handling
- Failed operations are collected and reported with detailed error messages
- Batch processing continues even if individual operations fail
- Connection errors are handled gracefully with proper error reporting

### Configuration and Parameters

#### Read Operation Parameters (both sync and async)
- `conn`: Redis connection object (required)
- `meas_info`: Dictionary mapping measurement IDs to their metadata (required)
  ```python
  {
      1: {"data_type": "NUMERIC"},
      2: {"data_type": "STRING"},
      # Add more measurements as needed
  }
  ```
- `start`: Start timestamp in milliseconds (required)
- `end`: End timestamp in milliseconds (required)
- `agg`: Aggregation type from Aggregate enum (optional)
- `agg_period`: Time bucket size from BucketSize enum (optional, default: _1H)
- `lower_density`: Boolean to use lower density TWA series (optional, default: False)
- `include_adjacents`: Boolean to include adjacent points (optional, default: False)

#### Write Operation Parameters (both sync and async)
- `conn`: Redis connection object (required)
- `tvs`: List of tuples containing: (required)
  ```python
  [
      (key, timestamp, value, data_type),
      # Add more entries as needed
  ]
  ```
  - `key`: String identifier for the measurement
  - `timestamp`: Integer timestamp in milliseconds
  - `value`: Numeric value or string/boolean for non-numeric types
  - `data_type`: "NUMERIC", "STRING", or "BOOLEAN"
- `batch_size`: Number of entries per batch (optional, default: 50)

### Dependencies and Requirements
- Redis server with TimeSeries module enabled
- Python packages:
  - redis-py>=4.5.0 (for both sync and async support)
  - redis.asyncio (for async operations)
  - pandas (for non-numeric data aggregation)
  - logging (configured through BromptonPythonUtilities)
  - pytest-asyncio (for running async tests)
- Redis connection management is not handled by this utility

### Best Practices
- Use appropriate batch sizes based on your data volume (default: 50)
- Consider using TWA for high-frequency numeric data
- Monitor failed operations and adjust batch sizes if needed
- Use appropriate aggregation types based on your data characteristics:
  - TWA for continuous measurements
  - RATETOTAL for flow-like measurements
  - DELTA* aggregates for change-based analysis

### Common Error Scenarios and Solutions
1. **WRONGTYPE Error**
   - Cause: Attempting to write numeric data to a stream or vice versa
   - Solution: Ensure data types match between writes (NUMERIC vs STRING/BOOLEAN)

2. **Connection Errors**
   - Cause: Redis server unavailable or network issues
   - Solution: Implement retry logic with exponential backoff
   - Error will be reported for entire batch

3. **TimeSeries Module Errors**
   - Cause: Redis TimeSeries module not loaded or incompatible
   - Solution: Ensure Redis is configured with TimeSeries module
   - Check Redis server logs for module loading issues

4. **Data Type Conversion**
   - Cause: Non-numeric data in numeric fields or vice versa
   - Solution: Validate data types before writing
   - Use appropriate type conversion in your application
