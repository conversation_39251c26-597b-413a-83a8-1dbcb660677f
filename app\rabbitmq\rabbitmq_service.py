import json
import pika
import logging
from app.LimitCheckerService import LimitCheckResult
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
from app.rabbitmq.connection_manager import RabbitMQConnectionManager
import pika.exceptions

logger = setup_logging()

def send_rabbitmq_notification(result: LimitCheckResult, event_id: int) -> bool:
    if event_id is None:
        logger.info(f"Event ID is None for Alert ID: {result.alert_id}, notification will not be sent.")
        return False
    manager = RabbitMQConnectionManager()
    cfg = manager.config
    RABBITMQ_EXCHANGE = cfg['exchange']
    RABBITMQ_ROUTING_KEY = 'alert.exceeded'
    try:
        ch = manager.get_channel()
        alert_data = {
            "alert_id": result.alert_id,
            "state": result.state,
            "timestamp": result.timestamp,
            "measurement_id": result.measurement_id,
            "input_value": result.input_value,
            "limit": result.limit,
            "comparator": result.comparator,
            "deadband": result.deadband,
            "aggregate": result.aggregate,
            "period": result.period,
            "asset_id": result.asset_id,
            "event_id": event_id
        }
        payload = json.dumps(alert_data)
        logger.debug(f"RabbitMQ payload: {payload}")

        def publish(channel):
            channel.basic_publish(
                exchange=RABBITMQ_EXCHANGE,
                routing_key=RABBITMQ_ROUTING_KEY,
                body=payload,
                properties=pika.BasicProperties(content_type='application/json')
            )

        try:
            publish(ch)
            logger.info(f"RabbitMQ alert sent: alert_id={result.alert_id}, event_id={event_id}")
        except (pika.exceptions.StreamLostError, pika.exceptions.AMQPConnectionError, pika.exceptions.AMQPChannelError) as e:
            logger.warning(f"RabbitMQ publish failed (will retry): {e}")
            manager.close()
            try:
                ch = manager.get_channel()
                publish(ch)
                logger.info(f"RabbitMQ alert sent after reconnect: alert_id={result.alert_id}, event_id={event_id}")
            except Exception as retry_exc:
                logger.error(f"RabbitMQ resend failed: {retry_exc}, data :{alert_data}")
                raise
        return True
    except Exception as e:
        logger.error(f"RabbitMQ send failed: {e}, data :{alert_data}")
        manager.close()
        raise
