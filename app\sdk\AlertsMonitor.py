import argparse
import logging
import os
import sys
import signal
import functools
import time

import numpy as np
import pandas as pd
import asyncio
from app.sdk.asynccore.asynctsdbreadertask import AsyncTSDBReaderTask,Aggregate,AggregatePeriod
from app.sdk.asynccore.asynceventtasks import AsyncLimitCheckerWithResetTask,CompareOperation_Dict
from app.sdk.asynccore.asyncevent import EventState, AsyncEvent, LogHandler
from app.sdk.asynccore.asynceventschedulers import AsyncPollScheduler
from app.sdk.asynccore.asyncemailtask import AsyncTwilioAlertEmailTask,EMAIL_REGEX
from app.sdk.asynccore.asyncsmstask import AsyncTwilioAlertSmsTask,SMS_REGEX
from app.sdk.asynccore.asyncmocktasks import AsyncMockDataReader

stopped=False

poll_scheduler:AsyncPollScheduler
async def start_poll(event,poll_period,skip_start_event,one_shot=False):
    poll_scheduler=AsyncPollScheduler(event, poll_period, skip_start_event=skip_start_event, one_shot=one_shot)
    await poll_scheduler.start()

# This method is used to stop a poll
async def stop_poll():
    if(poll_scheduler):
        await poll_scheduler.stop()

def get_measurements_and_alerts(config:pd.DataFrame):
    measurements_and_alerts = {}  # (agg,period)->{(assettag,assetid)->{(meastag,measid)->[(limit,comparator,deadband,recipients)}
    # get unique combinations of aggregate and period and group them together in tsdb reader objects
    config['POLL GROUP'] = config.apply(lambda l: f"{l['AGGREGATE']}_{l['AGGREGATE PERIOD']}", axis=1)
    poll_groups=config['POLL GROUP'].unique()
    for poll_group in poll_groups:
        parts=poll_group.split("_")
        agg,period=parts[0],parts[1]
        poll_group_view=config[(config['AGGREGATE']==agg) & (config['AGGREGATE PERIOD']==period)]
        if(not (agg,period) in measurements_and_alerts):
            measurements_and_alerts[(agg,period)]={}
        asset_ids=poll_group_view['ASSET ID'].unique()
        for asset_id in asset_ids:
            asset_view=poll_group_view[poll_group_view['ASSET ID']==asset_id]
            asset_tag=asset_view['ASSET PATH'].iloc[0]
            meas_ids=asset_view['MEASUREMENT ID'].unique()
            if(not (asset_tag,asset_id) in measurements_and_alerts[(agg,period)]):
                measurements_and_alerts[(agg,period)][(asset_tag,asset_id)]={}
            for meas_id in meas_ids:
                meas_view=asset_view[asset_view['MEASUREMENT ID']==meas_id]
                meas_tag=meas_view['MEASUREMENT TAG'].iloc[0]
                if(not (meas_tag,meas_id) in measurements_and_alerts[(agg,period)][(asset_tag,asset_id)]):
                    measurements_and_alerts[(agg, period)][(asset_tag, asset_id)][(meas_tag,meas_id)]=[]
                for alert in meas_view.itertuples():
                    limit=alert.THRESHOLD_VALUE
                    comparison=alert.COMPARISON
                    deadband=alert.RESET_DEADBAND
                    recipients=alert.NOTIFICATION_RECIPIENTS
                    measurements_and_alerts[(agg, period)][(asset_tag, asset_id)][(meas_tag, meas_id)].append((limit,comparison,deadband,recipients))
    return measurements_and_alerts

def exit_handler(signum):
    global stopped
    stopped=True
    time.sleep(4) # wait for monitor to stop
    sys.exit(0)

loop=None
async def main():
    # Add exit handler to interrupt the process with ctrl-c or kill
    loop=asyncio.get_running_loop()
    for signame in {'SIGINT', 'SIGTERM'}:
        loop.add_signal_handler(
            getattr(signal, signame),
            functools.partial(exit_handler, signame))

    # Get measurements and alerts
    meas_and_alerts=get_measurements_and_alerts(config=config)

    # Setup poll event that will be triggered by poll scheduler
    poll_period=20
    poll_state = EventState()
    poll_state.context = api_host
    poll_state.state = f"triggering reading values of TSDB every {poll_period} sec"
    poll_event = AsyncEvent(poll_state)

    # Create and configure poll scheduler events, limit checkers and notifications list  (one per group of agg and period)
    for agg_period,asset_meas in meas_and_alerts.items():
        agg=Aggregate(agg_period[0])
        period=AggregatePeriod(agg_period[1])
        meas_ids=set()
        measurements={} # (assettag,assetid)->[(meastag,measid)]
        limits={} # measid->[(limit,comparator,deadband,recipients)]
        for tag_asset,tag_meas in asset_meas.items():
            measurements[tag_asset]=tag_meas.keys()
            for tag_id in tag_meas.keys():
                meas_ids.add(tag_id[1])
                if(not tag_id[1] in limits):
                    limits[tag_id[1]]=tag_meas[tag_id]

        # Create for tsdb reader for specific agg and period
        if(not test):
            tsdb_task = AsyncTSDBReaderTask(api_host,cust_id, measurements, aggregate=agg,
                                            period=period, timeout=30) # , completion_callback=log_task_completion)
        else:
            # for each alert configure task for limit alert that listens to the reader task
            test_limits={}
            for meas_id in meas_ids:
                for limit in limits[meas_id]:
                    comparator = CompareOperation_Dict[limit[1]]
                    threshold = float(limit[0])
                    deadband = float(limit[2])
                    if (not meas_id in test_limits):
                        test_limits[meas_id] = []
                    test_limits[meas_id].append({"limit": threshold, "comparator": comparator, "deadband": deadband})
            tsdb_task = AsyncMockDataReader(measurements,test_limits,pattern=(2,5))

        # Subscribe it to the poll event
        poll_event.add_callback(tsdb_task)
        # # Add errors callback to log exceptions for this task
        # poll_event.add_onerror(log_exception,tsdb_task)

        # for each alert configure task for limit alert that listens to the reader task
        for meas_id in meas_ids:
            # limit is tuple (limit,comparator,deadband,recipients)
            for limit in limits[meas_id]:
                comparator=CompareOperation_Dict[limit[1]]
                limit_checking_task=AsyncLimitCheckerWithResetTask(meas_id,limit=limit[0],deadband=limit[2],comparator=comparator) #,completion_callback=log_task_completion)
                # add each limit checker as callback to tsdb reader task
                tsdb_task.add_event_callback(limit_checking_task,meas_id)
                # limit_checking_task.add_event_callback(store_in_db)
                # # Add errors callback to log exceptions for this task
                # tsdb_task.add_event_onerror(log_exception,limit_checking_task)

                # Create notifier tasks
                sms_recipients=[]
                email_recipients=[]
                for recipient in limit[3].split(","):
                    if(SMS_REGEX.match(recipient)):
                        sms_recipients.append(recipient)
                    elif(EMAIL_REGEX.match(recipient)):
                        email_recipients.append(recipient)
                    else:
                        raise ValueError(f"{recipient} in limit {limit} for meas_id {meas_id} is not a valid email address nor a valid sms phone number")

                # create notifications tasks
                if(sms_recipients):
                    sms_task=AsyncTwilioAlertSmsTask(account_sid=twilio_account_sid, auth_token=twilio_auth_token, sender=sms_sender_phone_number,recipients=sms_recipients,timeout=30) #,completion_callback=log_task_completion)
                    limit_checking_task.add_event_callback(sms_task)
                if(email_recipients):
                    email_task=AsyncTwilioAlertEmailTask(api_key=twilio_email_api_key,sender=email_sender,recipients=email_recipients,timeout=30) #,completion_callback=log_task_completion)
                    limit_checking_task.add_event_callback(email_task)

                # # add logging for limit checker all callbacks
                # limit_checking_task.add_event_onerror(log_exception)

    # start poll
    logger.info("Starting poll for Alert Monitor")
    await start_poll(poll_event,poll_period,skip_start_event=False,one_shot=False)
    # Wait forever until stopped
    while(not stopped):
        await asyncio.sleep(3)
    logger.info("Stopping Alert Monitor")
    await stop_poll()
#    LogHandler.stop_logging()

def load_config(config_file:str):
    df=pd.read_excel(config_file).dropna()
    if (df.empty):
        raise ValueError(f"No valid configurations found in file {config_file}")
    df['ASSET ID']=df['ASSET ID'].astype(np.int64)
    df['MEASUREMENT ID']=df['MEASUREMENT ID'].astype(np.int64)
    df['AGGREGATE']=df['AGGREGATE'].apply(lambda a:a.replace("'",'').split('=')[-1].strip())
    df['AGGREGATE PERIOD']=df['AGGREGATE PERIOD'].apply(lambda a:a.replace("'",'').split('=')[-1].strip())
    df=df.rename(columns={'THRESHOLD VALUE':'THRESHOLD_VALUE','RESET DEADBAND':'RESET_DEADBAND','NOTIFICATION RECIPIENTS':'NOTIFICATION_RECIPIENTS'})
    return df

if __name__ == '__main__':

    # Parse arguments
    parser = argparse.ArgumentParser(description='Brompton Group Alerts Monitor.\nMonitors alerts conditions defined in input Excel file.')
    parser.add_argument('-c','--cust',type=int, help='Customer id',required=True)
    parser.add_argument('-l','--logdir',type=str, help='Path to root directory where logs will be writtenn',required=True)
    parser.add_argument('-a','--alerts',type=str,help='Path to Excel file containing alert configurations',required=True)
    parser.add_argument('-p','--poll',type=str,help='Poll period in seconds. Must be >=20',required=True)
    parser.add_argument('-d','--debug',type=bool,help='OPTIONAL - Enable debugging.  Default=False',default=False)
    parser.add_argument('-t','--test',type=bool,help='OPTIONAL - Enable test mode.  Will simulate alert conditions in a loop.  Default=False',default=False)

    # Parse
    args = parser.parse_args()
    cust_id=int(args.cust)
    log_dir=args.logdir
    config_file=args.alerts
    poll=int(args.poll)
    debug=bool(args.debug)
    test=bool(args.test)

    if(poll<20):
        raise ValueError("poll must be greater than or equal to 20")

    # Load environment variables
    email_sender = os.getenv('email_sender')
    sms_sender_phone_number = os.getenv('sms_sender_phone_number')
    twilio_email_api_key = os.getenv('twilio_email_api_key')
    twilio_account_sid=os.getenv('twilio_account_sid')
    twilio_auth_token= os.getenv('twilio_auth_token')
    api_host = os.getenv('api_host')

    # Read config
    config=load_config(config_file)

    # configure logging
    LogHandler.configure_logging(log_dir,level=logging.DEBUG if debug else logging.INFO)
    # if you wish to do some logging as well, get the logger already shared
    logger=logging.getLogger(__name__)

    # start
    try:
        asyncio.run(main())
    finally:
        if(not loop is None):
            loop.stop()
