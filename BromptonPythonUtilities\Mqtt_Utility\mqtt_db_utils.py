#from airflow.providers.postgres.hooks.postgres import <PERSON><PERSON><PERSON>Hook
from typing import List,Dict,Tuple,Any
from datetime import datetime
import json
from dataclasses import dataclass
from typing import List,Dict
try:
    from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
except ImportError:
    from Logging_Utility.logging_config import setup_logging
    
logger = setup_logging()

@dataclass
class AssetMeasurement:
    assetid:int
    shorttag:str
    tag:str=None
    tsdbkey:int=None
    dtype:str=None

# asset_tags: asset_id->[shortags]
def get_asset_meas_from_db(asset_tags: Dict[int, List[str]], hook) -> Dict[int,Dict[str,AssetMeasurement]]: # assetid->
        #List[AssetMeasurement]:
    asset_ids=set()
    short_tags=set()
#    asset_short=[]
    for asset,shorttags in asset_tags.items():
        asset_ids.add(str(asset))
        for shorttag in shorttags:
            short_tags.add(shorttag)
#            asset_short.append(f"{asset}:{shorttag}")
    asset_ids_str = ",".join(list(asset_ids))
    short_tags_str = "'"+"','".join(list(short_tags))+"'"
    asset_data_qry =f"""select a.id assetid,m.id tsdbkey,m.tag,d.name dtype,
                    case 
                        when position('\\' in m.tag)>0 then right(m.tag,position('\\' in reverse(m.tag))-1) 
                        when position(':' in m.tag)>0 then right(m.tag,position(':' in reverse(m.tag))-1) 
                        when position('/' in m.tag)>0 then right(m.tag,position('/' in reverse(m.tag))-1) 
                        else m.tag 
                    end shorttag
                from asset a 
                join asset_measurement am on 
                    am.asset=a.id and a.id in ({asset_ids_str}) 
                join measurement m on 
                    m.id=am.measurement and 
                    case
                        when position('\\' in m.tag)>0 then right(m.tag,position('\\' in reverse(m.tag))-1)
                        when position(':' in m.tag)>0 then right(m.tag,position(':' in reverse(m.tag))-1) 
                        when position('/' in m.tag)>0 then right(m.tag,position('/' in reverse(m.tag))-1) 
                        else m.tag
                    end in ({short_tags_str})
					and coalesce(m.enabled,'true')<>false
					and m.deleted_at is null
                join data_type d on m.data_type=d.id"""
    #print(asset_data_qry)
    # replace(
    # case
    #     when position('\' in m.tag)>0 then right(m.tag,position('\' in reverse(m.tag))-1)
    #     else m.tag
    # end,' ','')

    asset_meas={}
    # connect to db
    cur = None
    try:
        conn = hook if hasattr(hook, 'cursor') else hook.get_conn()
        # Select id,a_type,m_type,location,data_type by type
        # logger.info("Reading definitions for asset IDs", extra={"asset_ids": asset_ids_str})
        cur = conn.cursor()
        cur.execute(asset_data_qry)
        rows = cur.fetchall()
        short_tags_not_missing={}
        for row in rows:
                entry=AssetMeasurement(assetid=row[0],tsdbkey=row[1],tag=row[2],dtype=row[3],shorttag=row[4])
                if(not row[0] in asset_meas):
                    asset_meas[row[0]]={}
                if(not row[4] in asset_meas[row[0]]):
                    asset_meas[row[0]][row[4]]=entry
                if(not row[0] in short_tags_not_missing):
                    short_tags_not_missing[row[0]]=[]
                short_tags_not_missing[row[0]].append(entry.shorttag)
    except Exception as e:
        logger.exception("Exception occurred while fetching asset measurements", extra={"error": str(e)})
        raise(e)
    finally:
        if(cur):
            cur.close()
    # Mark missing items
    for asset in asset_tags.keys():
        asset_tags_expected=asset_tags[asset]
        for tag in asset_tags_expected:
            if (asset in short_tags_not_missing and tag in short_tags_not_missing[asset]):
                continue
            entry = AssetMeasurement(assetid=asset, shorttag=tag)
            if (not asset in asset_meas):
                asset_meas[asset] = {}
            if (not tag in asset_meas[asset]):  # and entry is made but is key is not set
                asset_meas[asset][tag] = entry
    return asset_meas
####


# TODO: Move to config
timestamp_handling={  # topic_prefix->{metadata}
    'VV100':{
        "name":'time',
        "format":"%Y:%m:%dT%H:%M:%S",
        "multiplier":1000},
    'ACCUVIMII':{
        "name":'timestamp',
        "format":None, # means it is in unix timestamp form
        "multiplier":1000}
}

#def extract_topics_from_db(interface:str,hook:PostgresHook)->List[str]:
def extract_topics_from_db(interface: str,hook) -> List[str]:
    # Get topic that matches interface name
    qry=f"""select distinct right(m.file_pattern,length(m.file_pattern)-position(':' in m.file_pattern)) || '/' || 
						case when right(m.file_pattern,length(m.file_pattern)-position(':' in m.file_pattern)) !='spBv1.0' 
							then a.id::text 
						else 
							a.tag || '/NDATA/#' 
						end topic,a.id
			from asset_interface ai
                join interface i on i.id=ai.interface and i.name='{interface.lower()}'
                join asset a on a.id=ai.asset
                join etl_file_column_map m on m.file_pattern like i.name || '%' and m.asset_type=a.a_type
            order by 2"""
    topics=[]
    # connect to db
    cur = None
    try:
        conn = hook if hasattr(hook, 'cursor') else hook.get_conn()
        # Select id,a_type,m_type,location,data_type by type
        logger.info("Reading definitions for interface", extra={"interface": interface})
        cur = conn.cursor()
        cur.execute(qry)
        rows = cur.fetchall()
        for row in rows:
            topics.append((row[0],row[1]))  # topic,dbId
    except Exception as e:
        logger.exception("Exception occurred while extracting topic from db", extra={"error": str(e)})
        raise(e)
    finally:
        if(cur):
            cur.close()
    return topics

#def extract_mapping_from_db(topics:List[str],hook:PostgresHook)->Dict[str,Dict[str,tuple]]: # topic->column (tag,id)
def extract_mapping_from_db(topics: List[str],hook) -> Dict[str, Dict[str, tuple]]:  # topic->column (tag,id)

    # Get topic that match interface name
    topics_list=",".join([f"\'{topic}\'" for topic in topics])
    qry=f"""select * from
            (select right(m.file_pattern,position(':' in m.file_pattern)-1) || '/' || a.id topic, m.column, t.tag, t.id,
                t.data_type
            from asset_interface ai
            join interface i on i.id=ai.interface and i.name='mqtt0' 
            join asset a on a.id=ai.asset
            join etl_file_column_map m on m.file_pattern like i.name || '%' and m.asset_type=a.a_type
            join v_measurements t on t.tag like '%' || m.tag and t.a_type=a.a_type
            and t.parent_asset=ai.asset) x
            where x.topic in ({topics_list}) order by x.topic"""
    topic_maps={}
    # connect to db
    cur = None
    try:
        conn = hook if hasattr(hook, 'cursor') else hook.get_conn()
#        conn = psycopg2.connect('host=************* dbname=DataLogger user=iot password=Br0mpt0n!0T port=5432')
        # Select id,a_type,m_type,location,data_type by type
        logger.info("Reading mappings of topic list ", extra={"topics_list": topics_list})
        cur = conn.cursor()
        cur.execute(qry)
        rows = cur.fetchall()
        for row in rows:
            if(not(row[0] in topic_maps)):
                topic_maps[row[0]]={}
            topic_maps[row[0]][row[1]]=(row[2],row[3],row[4])  # topic->column->(tag,id,data_type)
    except Exception as e:
        logger.exception("Exception occurred while extracting mapping from db", extra={"error": str(e)})
        raise(e)
    finally:
        if(cur):
            cur.close()
    return topic_maps

def extract_data_from_spB_messages(topic_messages:Dict[str, List[Any]], hook)->List[Tuple[str, int, Any, str]]: # [(key,ts,val,data_type)])
    data=[]
    for topic,msgs in topic_messages.items():
        tokens = topic.split("/")
        # Meter can be from tag names or from topic depending on gateway type
        # to differentiate which case, look for ':' in metric name
        meter = tokens[-1]  # node id or equipment id in the form name_id
        asset_tags={} # assetid->[tagsnames] where tagnames is the ending - USED to get keys from db
        asset_tag_data={} # assetid->tagname->[vt] ditto
                        # but with list of data - USED to match items returned from db with data in messages
        # Get mappings asset->[tags} for unique tags in the messages
        # Also grab message data asset->tag->vt
        timestamp = round(datetime.now().timestamp() * 1000) # in case no timestamp found
        for metric in msgs:
            if (metric['name'] != 'bdSeq' and not (metric['name'].startswith("Node "))):  # skips device metrics in NBIRTH
                metric_parts = metric['name'].split(":")
                if (len(metric_parts) > 1):  # assumes meter:tag in metric name
                    meter_str = ":".join(metric_parts[0:-1]) if len(metric_parts) > 2 else metric_parts[0]
                    tag_str = metric_parts[-1]
                else:
                    meter_str = meter
                    tag_str = metric['name']
                # Ending part should be the id of the asset
                meter_parts = meter_str.split("_")
                # TODO: remove spaces from tag string - to make query always work with or without spaces
                tag_str.replace(' ','')
                try:
                    asset_id=int(meter_parts[-1])
                except:
                    logger.info(f"Meter extracted from not a valid integer - skipping tag(s)",
                                extra={"meter": meter_parts[-1], "meter_str": meter_str})
                    if (len(metric_parts) > 1):  # assumes meter:tag in metric name
                        continue  # keep going through messages
                    else:
                        return data  # no sense in continuing with this meter
                if(not(asset_id) in asset_tags):
                    asset_tags[asset_id]=[]
                if(not(tag_str) in asset_tags[asset_id]):
                    asset_tags[asset_id].append(tag_str)
                # Grab data
                if(not(asset_id) in asset_tag_data):
                    asset_tag_data[asset_id]={}
                if(not(tag_str) in asset_tag_data[asset_id]):
                    asset_tag_data[asset_id][tag_str]=[]
                # TODO: optimize
                if('floatValue' in metric):
                    value=metric['floatValue']
                elif('doubleValue' in metric):
                    value=metric['doubleValue']
                elif('longValue' in metric):
                    value=metric['longValue']
                elif('intValue' in metric):
                    value=metric['intValue']
                elif('booleanValue' in metric):
                    value=metric['booleanValue']
                elif('stringValue' in metric):
                    value=metric['stringValue']
                else:
                    try:
                        value=metric['extensionValue']
                    except:
                        logger.exception(
                            "MQTT value type not defined for a metric",
                            exc_info=True,
                            extra={
                                "device_id": asset_id,
                                "metric_name": metric.get("name"),
                                "metric_details": metric
                            }
                        )
                        continue
                metric_timestamp=int(metric['timestamp'] if 'timestamp' in metric else timestamp)
                asset_tag_data[asset_id][tag_str].append((metric_timestamp, value))
            else:
                if ('timestamp' in metric):
                    timestamp = metric['timestamp']
        # TODO: consider caching to no have to read db all the time
        try:
            asset_measurements=get_asset_meas_from_db(asset_tags,hook) # Dict asset->shortag->AssetMeasurement object
        except Exception as e:
            logger.exception("Exception when reading mappings from database for tags", extra={"error": str(e), "asset_tags": asset_tags})
            # logger.exception(f"Exception when reading mappings from database for tags {asset_tags}:\n{e}", exc_info= True)
            continue
        # go through message data building the content
        for asset_id, tag_data in asset_tag_data.items():
            for tag, data_rows in tag_data.items():
                try:
                    metadata = asset_measurements[asset_id][tag]
                    if metadata.tsdbkey is None:
                        logger.exception(
                            "Unmatched device and tag",
                            exc_info=True,
                            extra={
                                "asset_id": asset_id,
                                "tag": tag
                            }
                        )
                        raise KeyError(f"Unmatched Device {asset_id} and tag {tag}")
                except KeyError:
                    logger.exception(
                        "Device tag not defined in metadata database",
                        exc_info=True,
                        extra={
                            "asset_id": asset_id,
                            "tag": tag
                        }
                    )
                    break
                for tup in data_rows:  # (metric_timestamp, value)
                    data.append((str(metadata.tsdbkey), tup[0], tup[1], metadata.dtype))

    return data

# This puts it in an easy format to inserts into the tds
def extract_data_from_message(topic:str,message:Any,
                              topic_mappings:Dict[str,tuple] # colum->(tag,id,data_type)
                              )->List[Tuple[str,int,Any,str]]: # [(id,ts,val,data_type)]
    # Topic prefix tells which is the specific meter so it can be handled in this function
    topic_prefix=topic.split("/")[0]
    # get timestamp from message
    ts_handling=timestamp_handling[topic_prefix]
    raw_ts=message[ts_handling['name']]
    ts=round((float(raw_ts) if not ts_handling['format'] else datetime.strptime(raw_ts,ts_handling['format']).timestamp())*ts_handling['multiplier'])
    message_data=[]
    for column,tag_id in topic_mappings.items():
        id=tag_id[1]
        val=message[column]
        message_data.append((id,ts,val,tag_id[2]))
    return message_data


if __name__=="__main__":
    interface='mqtt0'
#    topics=extract_topics_from_db(interface)
    topics=['VV100/267','VV100/284']
    msg='{"flow":"0.16","total":"5069.54","temp":"39","time":"2023:07:12T03:05:23","mac":"2A:31:2A:00:00:A5","kFactor":"1.0000","revs":"5069.54","minflow":"0.00"}'
    mappings=extract_mapping_from_db(topics)
    for topic in topics:
        message_data=extract_data_from_message(topic,json.loads(msg),mappings[topic])
        logger.info(f"message_data: {message_data}")