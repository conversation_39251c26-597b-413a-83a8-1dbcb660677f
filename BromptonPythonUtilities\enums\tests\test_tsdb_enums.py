import pytest
from BromptonPythonUtilities.enums.tsdb_enums import Aggregate, BucketSize

class TestAggregate:
    def test_aggregate_values(self):
        """Test that all Aggregate enum values are correct and accessible"""
        assert Aggregate.NONE.value == 'none'
        assert Aggregate.TOTAL.value == 'total'
        assert Aggregate.TWA.value == 'twa'
        assert Aggregate.MAX.value == 'max'
        assert Aggregate.MIN.value == 'min'
        assert Aggregate.AVG.value == 'avg'
        assert Aggregate.STD.value == 'std.p'
        assert Aggregate.RATETOTAL.value == 'ratetotal'
        assert Aggregate.DELTATWA.value == 'deltatwa'
        assert Aggregate.DELTAAVG.value == 'deltaavg'
        assert Aggregate.DELTAMAX.value == 'deltamax'
        assert Aggregate.DELTAMIN.value == 'deltamin'
        assert Aggregate.DELTASTD.value == 'deltastd'

    def test_aggregate_is_string_enum(self):
        """Test that Aggregate inherits from str for string comparison"""
        assert isinstance(Aggregate.TWA.value, str)
        assert Aggregate.TWA == 'twa'
        assert str(Aggregate.MAX) == 'max'

    def test_aggregate_iteration(self):
        """Test that we can iterate over all Aggregate values"""
        expected_values = {
            'none', 'total', 'twa', 'max', 'min', 'avg', 'std.p', 'ratetotal',
            'deltatwa', 'deltaavg', 'deltamax', 'deltamin', 'deltastd'
        }
        enum_values = {item.value for item in Aggregate}
        assert enum_values == expected_values

class TestBucketSize:
    def test_bucket_size_values(self):
        """Test that all BucketSize enum values are correct"""
        # 1 minute
        assert BucketSize._1M.value == 60 * 1000
        # 1 hour
        assert BucketSize._1H.value == 60 * 60 * 1000
        # 1 day
        assert BucketSize.DAILY.value == 24 * 60 * 60 * 1000
        # 1 week
        assert BucketSize.WEEKLY.value == 7 * 24 * 60 * 60 * 1000
        # 1 month (31 days)
        assert BucketSize.MONTHLY.value == 31 * 24 * 60 * 60 * 1000

    def test_bucket_size_comparisons(self):
        """Test that BucketSize values can be properly compared"""
        assert BucketSize._1M.value < BucketSize._1H.value
        assert BucketSize._1H.value < BucketSize.DAILY.value
        assert BucketSize.DAILY.value < BucketSize.WEEKLY.value
        assert BucketSize.WEEKLY.value < BucketSize.MONTHLY.value

    def test_bucket_size_arithmetic(self):
        """Test arithmetic operations with BucketSize values"""
        # Test that 2 hours equals 120 minutes
        assert BucketSize._2H.value == 2 * BucketSize._1H.value
        # Test that 1 day equals 24 hours
        assert BucketSize.DAILY.value == 24 * BucketSize._1H.value
        # Test that 1 week equals 7 days
        assert BucketSize.WEEKLY.value == 7 * BucketSize.DAILY.value

class TestEnumImports:
    def test_import_from_package(self):
        """Test importing enums from the package level"""
        from BromptonPythonUtilities.enums import Aggregate, BucketSize
        assert isinstance(Aggregate.TWA, Aggregate)
        assert isinstance(BucketSize._1H, BucketSize)

    def test_import_from_module(self):
        """Test importing enums directly from the module"""
        from BromptonPythonUtilities.enums.tsdb_enums import Aggregate, BucketSize
        assert isinstance(Aggregate.TWA, Aggregate)
        assert isinstance(BucketSize._1H, BucketSize)

def test_enum_usage_example():
    """Test a practical example of using both enums together"""
    # Example: Setting up time series aggregation
    agg_type = Aggregate.TWA
    bucket = BucketSize._1H

    # Check types
    assert isinstance(agg_type, Aggregate)
    assert isinstance(bucket, BucketSize)

    # Check values
    assert agg_type.value == 'twa'
    assert bucket.value == 60 * 60 * 1000  # 1 hour in milliseconds

def test_enum_string_representation():
    """Test string representation of enum values"""
    assert str(Aggregate.TWA) == 'twa'
    assert repr(Aggregate.TWA) == 'Aggregate.TWA'
    assert str(BucketSize._1H) == 'BucketSize._1H'
    assert repr(BucketSize._1H) == 'BucketSize._1H'