## Starting Services Locally (Windows)

### Celery Worker
```sh
set ENVIRONMENT=dev  # specify env name - dev/test/prod
celery -A app.celery_app worker --loglevel=info
```

### Celery Beat
```sh
set ENVIRONMENT=dev  # specify env name - dev/test/prod
celery -A app.celery_app beat --loglevel=info
```

### Python App (UI)
```sh
set ENVIRONMENT=dev  # specify env name - dev/test/prod
uvicorn app.main:app --reload
```

## Running with Kubernetes Redis Sentinel

We provide a script `start_dev.sh` that automates the setup process:

1. Sets up port forwarding for Redis Sentinel nodes:
   - redis-node-0: localhost:26379
   - redis-node-1: localhost:26380
   - redis-node-2: localhost:26381
   - Main Redis: localhost:19496

2. Initializes required submodules
3. Starts the FastAPI application
4. Starts the Celery worker

To run the development environment:

```sh
./start_dev.sh
```

The script will:
- Set up all required port forwarding
- Initialize git submodules
- Install required Python packages
- Start all services
- Clean up processes on exit

Requirements:
- kubectl configured with access to the k8s cluster
- Python 3.11+
- Redis CLI tools installed

## Creating Alerts

1. TWA, 15min
2. TWA, 15min

`8_twa_15min` - alert_ids [1, 2], TWA, 15min

1. TWA, 30min

`8_twa_15min` - alert_ids [2], TWA, 15min
`8_twa_30min` - alert_ids [1], TWA, 30min

# Alert Service Required Workflow

1. MQTT consumer on `create-alert-dev` topic - payload: `{ customer_id, alert_id, aggregate, aggregate_period }`
2. MQTT consumer on `update-alert-dev` topic - payload: `{ customer_id, alert_id, previous_aggregate, previous_aggregate_period, aggregate, aggregate_period }`
3. MQTT consumer on `delete-alert-dev` topic - payload: `{ customer_id, alert_id, aggregate, aggregate_period }`
4. Fetch all alerts for the customer
5. Group by aggregate, aggregate period
6. Delete existing task
   - For `create-alert-dev`: `customer_id_aggregate_aggregate_period`
   - For `update-alert-dev`: `customer_id_previous_aggregate_previous_aggregate_period`
   - For `delete-alert-dev`: `customer_id_aggregate_aggregate_period`
7. Schedule a task for each group with name as `customer_id_aggregate_aggregate_period`
   - Payload: `{ customer_id, aggregate, aggregate_period, alert_ids }`

# Profiling

## Start Celery Worker with Profiler
```sh
python -m cProfile -o worker_prof.prof -m celery -A app.celery_app worker -E --loglevel=INFO
```

## Start Celery Beat with Profiler
```sh
python -m cProfile -o beat_prof.prof -m celery -A app.celery_app beat --loglevel=INFO
```

## Start the UI with Profiler
```sh
python -m cProfile -o uvicorn_prof.prof -m uvicorn app.main:app --reload
```

## Visualize Profiler Results
```sh
snakeviz worker_prof.prof  # change file name as needed
```

# Debugging Steps

## Debugging with VSCode

To debug the application using VSCode, you need to set up the debugger configuration in the `.vscode` folder.

1. Create a folder named `.vscode` in the root of your project if it doesn't already exist.
2. Inside the `.vscode` folder, create a file named `launch.json` with the following content:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "main",
      "type": "debugpy",
      "request": "launch",
      "module": "uvicorn",
      "args": [
        "app.main:app",
        "--reload"
      ],
      "jinja": true
    },
    {
      "name": "worker",
      "type": "python",
      "request": "launch",
      "module": "celery",
      "console": "integratedTerminal",
      "args": [
        "-A",
        "app.celery_app",
        "worker",
        "-E",
        "--loglevel=INFO"
      ],
      "cwd": "${workspaceFolder}",
      "justMyCode": true
    }
  ]
}
```

This configuration allows you to start and debug the Celery Worker, and Uvicorn server directly from VSCode.


## Setting Up the Development Environment

### Create a Virtual Environment

To create a virtual environment for this project, run the following command in your terminal:

```sh
python -m venv venv
```

### Activate the Virtual Environment

Activate the virtual environment using the following command:

- On Windows:
  ```sh
  .\venv\Scripts\activate
  ```
- On macOS and Linux:
  ```sh
  source venv/bin/activate
  ```

### Install Required Packages

Once the virtual environment is activated, install the required packages using `requirements.txt`:

```sh
pip install -r requirements.txt