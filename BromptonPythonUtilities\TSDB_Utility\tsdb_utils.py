import pandas as pd
import redis
import time
from typing import Dict, List, Tuple, Any

# Async support
import redis.asyncio

try:
    from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
    from BromptonPythonUtilities.enums.tsdb_enums import Aggregate, BucketSize
except ImportError:
    from Logging_Utility.logging_config import setup_logging
    from enums.tsdb_enums import Aggregate, BucketSize

logger = setup_logging()

BATCH_SIZE = 50


def get_tsdb_data(conn: redis.Redis, meas_info: Dict[int, Dict], start: int, end: int,
                  agg: Aggregate = None, agg_period: BucketSize = BucketSize._1H,
                  lower_density=False, include_adjacents=False):
    """
    Read time series data from Redis TSDB.
    
    Args:
        conn (redis.Redis): Redis connection
        meas_info (Dict[int, Dict]): Measurement info dictionary
        start (int): Start timestamp in milliseconds
        end (int): End timestamp in milliseconds
        agg (Aggregate, optional): Aggregation type. Defaults to None.
        agg_period (BucketSize, optional): Aggregation bucket size. Defaults to 1 hour.
        lower_density (bool, optional): Whether to use lower density data. Defaults to False.
        include_adjacents (bool, optional): Whether to include adjacent points. Defaults to False.
    
    Returns:
        Dict[int, List]: Dictionary mapping measurement IDs to their data points
    """
    logger.info(f"dates for redis : start : {start} and end : {end}")
    try:
        ret = {}
        numerics = [id for id, info in meas_info.items() if info['data_type'] not in ['STRING', 'BOOLEAN']]
        non_numerics = [id for id, info in meas_info.items() if info['data_type'] in ['STRING', 'BOOLEAN']] if len(numerics) < len(meas_info) else None

        # Process numeric measurements in batches
        if numerics:
            for i in range(0, len(numerics), BATCH_SIZE):
                batch = numerics[i:i + BATCH_SIZE]
                pipe = conn.pipeline(transaction=False)
                for id in batch:
                    base_key = f"{{{id}}}" 
                    if agg is None:
                        id_to_use = base_key if not lower_density else f"{{{id}}}_twa"
                        pipe.ts().range(id_to_use, start, end)
                    else:
                        id_to_use = f"{{{id}}}_{agg.value}"
                        pipe.ts().range(id_to_use, start, end, 
                                        aggregation_type=agg.value, 
                                        bucket_size_msec=agg_period.value,
                                        bucket_timestamp='-', align='-')
                batch_results = pipe.execute(raise_on_error=False)
                ret.update(dict(zip(batch, batch_results)))
        logger.info(f"numeric data retrieved from TSDB : {ret}")
        
        # Process non-numeric measurements
        if non_numerics:
            results = {}                
            for i in range(0, len(non_numerics), BATCH_SIZE):
                batch = non_numerics[i:i + BATCH_SIZE]
                pipe = conn.pipeline(transaction=False)
                for id in batch:
                    stream_key = f"{{{id}}}" 
                    pipe.xrange(name=stream_key, min=start, max=end)
                non_numerics_results = pipe.execute(raise_on_error=False)

                for id, result in zip(batch, non_numerics_results):
                    if isinstance(result, redis.ResponseError):
                        results[id] = result
                    else:
                        non_numeric_results = [
                            (int(result[0].decode('utf-8')[:-2]),
                             _convert_to_original_type(result[1].get(b'value'))
                            ) 
                            for result in result
                        ]
                        results[id] = []
                        if agg:  # for the case of aggregate return only the most recent to the aggregate timestamps
                            df_results = pd.DataFrame(non_numeric_results, columns=['ts','val'])
                            df_results['ts'] = df_results['ts'].apply(lambda t: pd.Timestamp(t,unit='ms'))
                            
                            # generate array of timestamps between tstart and tend incrementing by aggregate_period
                            df_times = pd.date_range(
                                pd.Timestamp(start,unit='ms'),
                                pd.Timestamp(end,unit='ms'),
                                freq=pd.Timedelta(milliseconds=agg_period.value)
                            ).to_frame(name='ts_end',index=False)
                            
                            df_times['ts_start'] = df_times['ts_end'].shift()
                            df_times.dropna(inplace=True)
                            
                            # join it with the results iterating
                            df_joined_list = []
                            for tup in df_times.itertuples():
                                df_filtered = df_results[
                                    (df_results['ts'] >= tup.ts_start) & 
                                    (df_results['ts'] <= tup.ts_end)
                                ].copy()
                                df_filtered['ts_start'] = tup.ts_start
                                df_joined_list.append(df_filtered)
                            
                            df_joined = pd.concat(df_joined_list)
                            # grab nearest (last)
                            df_joined = df_joined.groupby('ts_start').tail(1).reset_index()
                            df_joined['ts'] = df_joined['ts_start'].apply(
                                lambda t: round(1000*t.timestamp())
                            )
                            results[id] = df_joined[['ts','val']].to_records(index=False).tolist()
                        else:
                            results[id] = non_numeric_results
            ret.update(results)
        
        # Convert timestamps to milliseconds
        logger.info(f"data retrieved from TSDB : {ret}")
        return ret
    except Exception as e:
        logger.error(f"Error in get_tsdb_data: {str(e)}")
        raise e

def store_in_tsdb(conn: redis.Redis, tvs: List[Tuple[str, int, Any, str]], batch_size: int = 100) -> Tuple[bool, List[Tuple[str, int, Any, str]]]:
    """
    Write time series data to Redis TSDB.
    
    Args:
        conn (redis.Redis): Redis connection
        tvs (List[Tuple[str, int, Any, str]]): List of (key, timestamp, value, data_type) tuples
        batch_size (int, optional): Number of items to process in each batch. Defaults to 100.
    
    Returns:
        Tuple[bool, List]: Tuple of (success_flag, failed_operations)
    """
    len_tvs = len(tvs)
    start_time = time.time()
    logger.debug(f"Starting TSDB storage operation for {len_tvs}", extra={
        "entries_count": len_tvs,
        "operation": "store_tsdb"
    })
    successful_cnt = 0
    failed_ops: List[Tuple[Any, ...]] = []

    # Process in batches
    for chunk_start in range(0, len_tvs, batch_size):
        chunk = tvs[chunk_start : chunk_start + batch_size]
        try:
            pipe = conn.pipeline(transaction=False)
            for tv in chunk:
                key_name, ts, val, dtype = tv[:4]                    
                key = f"{{{key_name}}}" 
                if dtype in ('STRING', 'BOOLEAN'):
                    pipe.xadd(key, {"value": str(val)}, id=f"{ts}-0")
                else:
                    pipe.ts().add(key, ts, val)
            # Execute, but don't raise on individual errors
            results = pipe.execute(raise_on_error=False)

            # Pair each input tv with its result
            for tv, result in zip(chunk, results):
                key_name, ts, val, dtype = tv[:4]
                if isinstance(result, Exception):
                    failed_ops.append((key_name, ts, val, dtype, str(result)))
                else:
                    successful_cnt += 1

        except (redis.ConnectionError, redis.TimeoutError) as e:
            logger.exception("Redis connection error during TSDB storage")
            # mark entire batch as failed
            for tv in chunk:
                failed_ops.append((*tv[:4], str(e)))

        except Exception as e:
            logger.exception("Unexpected error during TSDB storage")
            for tv in chunk:
                failed_ops.append((*tv[:4], str(e)))

    duration = time.time() - start_time
    logger.info(
        "TSDB storage completed in %.2fs — Success: %d, Failed: %d",
        duration, successful_cnt, len(failed_ops)
    )

    return len(failed_ops) == 0, failed_ops

def _convert_to_original_type(value: bytes):
    """Helper method for type conversion"""
    value_decoded = value.decode('utf-8')
    try:
        if '.' in value_decoded:
            return float(value_decoded)
        return int(value_decoded)
    except ValueError:
        return value_decoded  # Return as-is if not convertible
    
# ------------------------
# Async Support Functions
# ------------------------

async def get_tsdb_data_async(conn: redis.asyncio.Redis, meas_info: Dict[int, Dict], start: int, end: int,
                           agg: Aggregate = None, agg_period: BucketSize = BucketSize._1H,
                           lower_density: bool = False, include_adjacents: bool = False) -> Dict[int, List]:

    logger.info(f"dates for redis : start : {start} and end : {end}")
    try:
        ret={}
        numerics = [id for id, info in meas_info.items() if info['data_type'] not in ['STRING', 'BOOLEAN']]
        non_numerics = [id for id, info in meas_info.items() if info['data_type'] in ['STRING', 'BOOLEAN']] if len(numerics) < len(meas_info) else None

        # Process numeric measurements in batches
        if numerics:
            for i in range(0, len(numerics), BATCH_SIZE):
                batch = numerics[i:i + BATCH_SIZE]
                async with conn.pipeline(transaction=False) as pipe:
                    for id in batch:
                        base_key = f"{{{id}}}" 
                        # MB: Corrected so that it uses the pre-stored aggregates vs. forcing aggregation raw or twa to be always used
                        if agg is None:
                            id_to_use = base_key if not lower_density else f"{{{id}}}_twa"
                            await pipe.ts().range(id_to_use, start, end)
                        else:
                            # if aggregate is asking for < than 5 mins, need to ask for raw key to be aggregated on the fly
                            if(agg_period.value<=BucketSize._5M.value):  
                                id_to_use = base_key
                            else:
                                id_to_use = f"{base_key}_{agg.value}"
                            await pipe.ts().range(id_to_use, start, end, 
                                                aggregation_type=agg.value, 
                                                bucket_size_msec=agg_period.value,
                                                bucket_timestamp='-',align='-')
                    batch_results = await pipe.execute(raise_on_error=False)
                ret.update(dict(zip(batch, batch_results)))

        if non_numerics:
            results = {}                
            for i in range(0, len(non_numerics), BATCH_SIZE):
                batch = non_numerics[i:i + BATCH_SIZE]
                async with conn.pipeline(transaction=False) as pipe:
                    for id in batch:
                        stream_key = f"{{{id}}}" 
                        await pipe.xrange(name=stream_key,min=start,max=end)
                    non_numerics_results = await pipe.execute(raise_on_error=False)

                for id, non_numeric_results in zip(batch, non_numerics_results):
                    if isinstance(non_numeric_results,redis.ResponseError):
                        results[id]=non_numeric_results
                    else:
                        non_numeric_results = [
                            (int(result[0].decode('utf-8')[:-2]),
                            # result[1].get(b'value').decode('utf-8')
                            _convert_to_original_type(result[1].get(b'value'))  # Convert value
                            ) 
                            for result in non_numeric_results
                            ]
                        results[id]=[]
                        # print(non_numeric_results)
                        if agg:  # for the case of aggregate return only the most recent to the aggregate timestamps (i.e. LAST of discrete values)
                            df_results=pd.DataFrame(non_numeric_results,columns=['ts','val'])
                            df_results['ts']=df_results['ts'].apply(lambda t:pd.Timestamp(t,unit='ms'))
                            # generate array of timestamps between tstart and tend incrementing by aggregate_period
                            df_times=pd.date_range(
                                pd.Timestamp(start,unit='ms'),
                                pd.Timestamp(end,unit='ms'),
                                freq=pd.Timedelta(milliseconds=agg_period.value)
                                ).to_frame(name='ts_end',index=False)
                            df_times['ts_start']=df_times['ts_end'].shift()
                            df_times.dropna(inplace=True)
                            # join it with the results iterating
                            df_joined_list=[]
                            for tup in df_times.itertuples():
                                df_filtered=df_results[(df_results['ts']>=tup.ts_start) & (df_results['ts']<=tup.ts_end)].copy()
                                df_filtered['ts_start']=tup.ts_start
                                df_joined_list.append(df_filtered)
                            df_joined=pd.concat(df_joined_list)
                            # grab nearest (last)
                            df_joined=df_joined.groupby('ts_start').tail(1).reset_index()
                            df_joined['ts']=df_joined['ts_start'].apply(lambda t:round(1000*t.timestamp()))
                            results[id]=df_joined[['ts','val']].to_records(index=False).tolist()  # tuples
                        else:
                            results[id]=non_numeric_results
            # TODO: adjacents
            if(include_adjacents):
                pass
            ret.update(results)
        # Log success
        return ret
    except Exception as e:
        logger.error(f"Error in get_tsdb_data_async: {str(e)}")
        raise e

async def store_in_tsdb_async(conn: redis.Redis, tvs: List[Tuple[str, int, Any, str]], batch_size: int=100) -> Tuple[bool, List[Tuple[str, int, Any, str]]]:
    start_time = time.time()
    # unsuccessful_ops = tvs
    len_tvs = len(tvs)
    logger.debug("Starting TSDB storage operation for {len_tvs}", extra={
        "entries_count": len_tvs,
        "operation": "store_tsdb"
    })
    successful_cnt=0 # count of successful operations
    failed_ops: List[Tuple[Any, ...]] = []

    # Process in batches
    for chunk_start in range(0, len_tvs, batch_size):
        chunk = tvs[chunk_start : chunk_start + batch_size]
        try:
            async with conn.pipeline(transaction=False) as pipe:
                for tv in chunk:
                    key_name, ts, val, dtype = tv[:4]                    
                    key = f"{{{key_name}}}" 
                    if dtype in ('STRING', 'BOOLEAN'):
                        pipe.xadd(key, {"value": str(val)}, id=f"{ts}-0")
                    else:
                        pipe.ts().add(key, ts, val)
                # Execute, but don’t raise on individual errors
                results = await pipe.execute(raise_on_error=False)

                # Pair each input tv with its result
                for tv, result in zip(chunk, results):
                    key_name, ts, val, dtype = tv[:4]
                    if isinstance(result, Exception):
                        failed_ops.append((key_name, ts, val, dtype, str(result)))
                    else:
                        successful_cnt += 1

        except (redis.ConnectionError, redis.TimeoutError) as e:
            logger.exception("Redis connection error during TSDB storage")
            # mark entire batch as failed
            for tv in chunk:
                failed_ops.append((*tv[:4], str(e)))

        except Exception as e:
            logger.exception("Unexpected error during TSDB storage")
            for tv in chunk:
                failed_ops.append((*tv[:4], str(e)))

    duration = time.time() - start_time
    logger.info(
        "TSDB storage completed in %.2fs — Success: %d, Failed: %d",
        duration, successful_cnt, len(failed_ops)
    )

    # all_succeeded is True iff there were no failures
    return len(failed_ops) == 0, failed_ops