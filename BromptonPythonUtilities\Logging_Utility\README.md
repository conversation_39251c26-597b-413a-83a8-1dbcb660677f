# Brompton JSON Logging Utility

A Python logging utility that provides structured JSON logging with OpenTelemetry trace context integration. This utility is designed to be easily integrated into any Python application requiring structured logging.

## Features

- ✨ JSON structured logging format
- 🔍 OpenTelemetry trace context integration
- 🎚️ Configurable log levels
- 📦 Custom context fields grouping
- 🔄 Default trace ID fallback
- 🚀 Easy setup and integration

## Installation

### Requirements

```bash
pip install python-json-logger
pip install opentelemetry-api
```

## Quick Start

### Basic Usage

```python
from logging_config import setup_logging

# Initialize logger with debug level
logger = setup_logging(log_level='debug')

# Basic logging
logger.info("Application started")
```

### Logging with Context

```python
# Log with extra fields
logger.info("User action completed", extra={
    "user_id": "123",
    "action": "login",
    "duration_ms": 150
})
```

## Log Format

### Sample Output

```json
{
    "timestamp": "2024-05-22T19:30:00.123Z",
    "name": "root",
    "level": "INFO",
    "pathname": "app.py",
    "funcName": "handle_request",
    "lineno": 45,
    "trace_id": "abcdef0123456789",
    "span_id": "0123456789abcdef",
    "message": "User action completed",
    "context": {
        "user_id": "123",
        "action": "login",
        "duration_ms": 150
    }
}
```

## Configuration

### Log Levels

Supported log levels (case-insensitive):
- `debug`
- `info`
- `warning`
- `error`
- `critical`

```python
logger = setup_logging(log_level='info')
```

### Trace Context

The utility automatically includes OpenTelemetry trace context when available:
- `trace_id`: 32-character hex string
- `span_id`: 16-character hex string

Default values when no trace context is available:
- `DEFAULT_TRACE_ID = '00000000000000000000000000000000'`
- `DEFAULT_SPAN_ID = '0000000000000000'`

## Key Components

### CustomJsonFormatter

Extends `JsonFormatter` to provide:
- Timestamp in ISO format
- Trace context fields
- Grouped context fields under 'context' key

### TraceLoggingFilter

Adds OpenTelemetry trace context to log records:
- Extracts current span information
- Falls back to default IDs if no span is active

## Best Practices

1. **Initialization**: Always initialize the logger at application startup
   ```python
   logger = setup_logging(log_level='info')
   ```

2. **Log Levels**: Use appropriate log levels for different scenarios
   - DEBUG: Detailed information for debugging
   - INFO: General operational events
   - WARNING: Unexpected but handled events
   - ERROR: Errors that need attention
   - CRITICAL: System-critical issues

3. **Context**: Include relevant context in extra fields
   ```python
   logger.info("API request processed", extra={
       "method": "GET",
       "path": "/api/v1/users",
       "status_code": 200,
       "response_time_ms": 45
   })
   ```

4. **Sensitive Data**: Be cautious with sensitive information
   - Avoid logging passwords, tokens, or personal data
   - Use masking for sensitive fields when necessary

5. **Structured Data**: Use structured data in extra fields for better querying
   ```python
   # Good
   logger.info("Order created", extra={
       "order_id": "12345",
       "amount": 99.99,
       "currency": "USD"
   })
   
   # Avoid
   logger.info("Order 12345 created with amount 99.99 USD")
   ```

## Error Handling

The utility handles common scenarios:
- Invalid log levels default to DEBUG
- Missing trace context uses default IDs
