"""
Centralized datetime utilities for consistent UTC timestamp handling across the application.

This module provides timezone-aware datetime functions that ensure all timestamps
are handled consistently in UTC, avoiding local time discrepancies.
"""

from datetime import datetime, timezone
from typing import Optional


def utc_now() -> datetime:
    """
    Get current UTC time as timezone-naive datetime object.
    
    This function provides a consistent way to get UTC time across the application
    while maintaining compatibility with existing timezone-naive code.
    
    Returns:
        datetime: Current UTC time as timezone-naive datetime object
    """
    return datetime.now(timezone.utc).replace(tzinfo=None)


def utc_now_timestamp_ms() -> int:
    """
    Get current UTC timestamp in milliseconds.
    
    Returns:
        int: Current UTC timestamp in milliseconds
    """
    return int(utc_now().timestamp() * 1000)


def utc_fromtimestamp(timestamp: float) -> datetime:
    """
    Create timezone-naive UTC datetime from timestamp.
    
    Args:
        timestamp: Unix timestamp in seconds
        
    Returns:
        datetime: UTC datetime as timezone-naive object
    """
    return datetime.fromtimestamp(timestamp, tz=timezone.utc).replace(tzinfo=None)


def utc_fromtimestamp_ms(timestamp_ms: int) -> datetime:
    """
    Create timezone-naive UTC datetime from millisecond timestamp.
    
    Args:
        timestamp_ms: Unix timestamp in milliseconds
        
    Returns:
        datetime: UTC datetime as timezone-naive object
    """
    return utc_fromtimestamp(timestamp_ms / 1000)


def format_utc_timestamp(timestamp_ms: Optional[int], format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
    """
    Format UTC timestamp for logging/display.
    
    Args:
        timestamp_ms: Unix timestamp in milliseconds (can be None)
        format_str: Format string for datetime formatting
        
    Returns:
        str: Formatted timestamp string or 'None' if timestamp is None
    """
    if timestamp_ms is None:
        return 'None'
    
    try:
        dt = utc_fromtimestamp_ms(timestamp_ms)
        return dt.strftime(format_str)
    except (ValueError, TypeError, OSError):
        return 'Invalid'


def ensure_utc_naive(dt: datetime) -> datetime:
    """
    Ensure datetime is timezone-naive UTC.
    
    If the datetime is timezone-aware, convert to UTC and make naive.
    If already naive, assume it's UTC and return as-is.
    
    Args:
        dt: Input datetime object
        
    Returns:
        datetime: Timezone-naive UTC datetime
    """
    if dt.tzinfo is not None:
        # Convert to UTC and make naive
        return dt.astimezone(timezone.utc).replace(tzinfo=None)
    else:
        # Already naive, assume it's UTC
        return dt


def timestamp_to_utc_ms(dt: datetime) -> int:
    """
    Convert datetime to UTC timestamp in milliseconds.
    
    Args:
        dt: Datetime object (assumed to be UTC if timezone-naive)
        
    Returns:
        int: UTC timestamp in milliseconds
    """
    # Ensure we have a timezone-naive UTC datetime
    utc_dt = ensure_utc_naive(dt)
    return int(utc_dt.timestamp() * 1000)
