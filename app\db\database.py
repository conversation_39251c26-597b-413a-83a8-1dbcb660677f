import configparser
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import QueuePool 
from app.config import config

# Extract the database URL from the config file
DATABASE_URL = config['database']['url']
# DATABASE_URL = "postgresql://postgres:Br0mpt0n!<EMAIL>/dataloggertest"

# Set up SQLAlchemy engine, session, and base
engine = create_engine(
                        DATABASE_URL,
                        poolclass=QueuePool,          # Use QueuePool for connection pooling
                        pool_size=10,                 # Set the size of the connection pool
                        max_overflow=20,              # Set the maximum overflow size beyond pool_size
                        pool_timeout=30,              # Set the timeout for getting a connection from the pool
                        pool_recycle=1800             # Recycle connections after a specified number of seconds
                    )
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()