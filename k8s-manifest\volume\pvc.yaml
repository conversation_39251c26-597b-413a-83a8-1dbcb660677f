apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: celery-worker-pvc
  namespace: admin
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: gp2
--- 
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: scheduler-api-pvc
  namespace: admin
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: gp2
--- 
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: celery-beat-pvc
  namespace: admin
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: gp2

