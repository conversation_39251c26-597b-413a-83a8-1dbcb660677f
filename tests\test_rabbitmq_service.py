import pytest
from unittest.mock import patch, MagicMock, call
from app.rabbitmq.rabbitmq_service import send_rabbitmq_notification, RabbitMQConnectionManager
from app.LimitCheckerService import LimitCheckResult
import pika
import threading
import logging
import atexit

@pytest.fixture
def dummy_result():
    return LimitCheckResult(
        alert_id=1,
        state="EXCEEDED",
        timestamp=1234567890,
        measurement_id=101,
        input_value="42.0",
        limit=50.0,
        comparator=2,
        deadband=0.5,
        aggregate="AVG",
        period="15M",
        asset_id=10
    )

def test_send_rabbitmq_notification_success(dummy_result):
    with patch("app.rabbitmq.rabbitmq_service.RabbitMQConnectionManager.get_channel") as mock_get_channel, \
         patch("app.rabbitmq.rabbitmq_service.RabbitMQConnectionManager.close") as mock_close:
        mock_channel = MagicMock()
        mock_get_channel.return_value = mock_channel
        event_id = 123
        assert send_rabbitmq_notification(dummy_result, event_id) is True
        mock_channel.basic_publish.assert_called_once()
        mock_close.assert_not_called()

def test_send_rabbitmq_notification_event_id_none(dummy_result):
    with patch("app.rabbitmq.rabbitmq_service.RabbitMQConnectionManager.get_channel") as mock_get_channel:
        event_id = None
        assert send_rabbitmq_notification(dummy_result, event_id) is False
        mock_get_channel.assert_not_called()

def test_send_rabbitmq_notification_stream_lost_error(dummy_result):
    with patch("app.rabbitmq.rabbitmq_service.RabbitMQConnectionManager.get_channel") as mock_get_channel, \
         patch("app.rabbitmq.rabbitmq_service.RabbitMQConnectionManager.close") as mock_close:
        mock_channel = MagicMock()
        mock_channel.basic_publish.side_effect = [pika.exceptions.StreamLostError(), None]
        mock_get_channel.return_value = mock_channel
        event_id = 123
        assert send_rabbitmq_notification(dummy_result, event_id) is True
        assert mock_channel.basic_publish.call_count == 2
        mock_close.assert_called_once()

def test_send_rabbitmq_notification_general_exception(dummy_result):
    with patch("app.rabbitmq.rabbitmq_service.RabbitMQConnectionManager.get_channel") as mock_get_channel, \
         patch("app.rabbitmq.rabbitmq_service.RabbitMQConnectionManager.close") as mock_close:
        mock_channel = MagicMock()
        mock_channel.basic_publish.side_effect = Exception("Some error")
        mock_get_channel.return_value = mock_channel
        event_id = 123
        with pytest.raises(Exception):
            send_rabbitmq_notification(dummy_result, event_id)
        mock_close.assert_called()

def test_thread_safety_on_get_channel(dummy_result):
    # Simulate concurrent access to get_channel
    with patch("app.rabbitmq.rabbitmq_service.RabbitMQConnectionManager._connect") as mock_connect:
        manager = RabbitMQConnectionManager()
        manager.connection = MagicMock(is_closed=False)
        manager.channel = MagicMock(is_closed=False)
        def worker():
            for _ in range(10):
                manager.get_channel()
        threads = [threading.Thread(target=worker) for _ in range(5)]
        for t in threads:
            t.start()
        for t in threads:
            t.join()
        # _connect should not be called since connection/channel are mocked as open
        mock_connect.assert_not_called()

def test_logging_on_connection_and_publish(dummy_result, caplog):
    with patch("app.rabbitmq.rabbitmq_service.RabbitMQConnectionManager.get_channel") as mock_get_channel:
        mock_channel = MagicMock()
        mock_get_channel.return_value = mock_channel
        event_id = 123
        with caplog.at_level(logging.INFO):
            send_rabbitmq_notification(dummy_result, event_id)
            assert any("RabbitMQ alert sent:" in m for m in caplog.messages)
