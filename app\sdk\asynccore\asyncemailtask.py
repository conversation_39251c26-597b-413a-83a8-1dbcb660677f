# get list of recipients
# in a sequence it gets triggered when event passed to it
import smtplib
import datetime
from email.mime.text import MIMEText
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail
from typing import List
from .asynceventtasks import LimitState,CompareOperation
from .asyncevent import AsyncTask, PersistentObject, EventState
import re
EMAIL_REGEX = re.compile(r"[^@]+@[^@]+\.[^@]+")
SMTP_SERVER_REGEX = re.compile(r'([\w.-]+)(\.[\w.-]+)+([\/\w\.-]*)*\/?$')

class AsyncEmailTask(AsyncTask):
    def __init__(self,sender:str,recipients:List[str],completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        if not EMAIL_REGEX.match(sender):
            raise ValueError(f"{sender} is not a valid email address")
        self.recipients=recipients
        self.__frm__=sender
        super().__init__(completion_callback=completion_callback,timeout=timeout,persist_obj=persist_obj)

    @property
    def recipients(self):
        return self.__recipients__
    @recipients.setter
    def recipients(self,recipients):
        for subscriber in recipients:
            if not EMAIL_REGEX.match(subscriber):
                raise ValueError(f"{subscriber} is not a valid email address")
        self.__recipients__=recipients

    # TO BE OVERRIDEN IN Derived classes
    def __get_body__(self,state:EventState):
        raise NotImplementedError("This method is Abstract.  Must implement in derived class")

    def __get_subject__(self,state:EventState):
        raise NotImplementedError("This method is Abstract.  Must implement in derived class")

class AsyncLimitAlertEmailTask(AsyncEmailTask):
    # Define overriden methods
    '''
        {'state': <LimitState.NORMAL: 0>, 'limit': 300000.0, 'comparator': <CompareOperation.GT: 3>, 'deadband': 10000.0, 'input': 290000.0, 'source': {'asset': {'id': 519, 'tag': 'California:MicroGrid:BatteryPack'}, 'measurement': {'id': 23777, 'tag': 'MicroGrid\\BatteryPack\\LoadMeterRealPower (watts)'}}}
    '''
    def __get_body__(self,state:EventState):
        utcdatetime = datetime.datetime.fromtimestamp(state.timestamp,tz=datetime.timezone.utc).strftime("%Y-%m-%dT%H:%M:%S %Z")
        if(isinstance(state.source,EventState)):
            asset=state.source.source['asset']['tag']
            measurement=state.source.source['measurement']['tag']
            limit = state.state['limit']
            comparator = state.state['comparator']
            deadband = state.state['deadband']
            current_value = state.state['input']
            return_to_normal = ''
            if (comparator in [CompareOperation.GE, CompareOperation.GT]):
                return_to_normal = f"Return to Normal: LE {limit - deadband}"
            elif (comparator in [CompareOperation.LE, CompareOperation.LT]):
                return_to_normal = f"Return to Normal: GE {limit + deadband}"
            return f"At: {utcdatetime}\nAsset: {asset}\nMeasurement: {measurement}\nCurrentValue: {current_value}\nSettings:\n\tThreshold: {comparator.name} {limit}\n\t{return_to_normal}\n"
        elif(isinstance(state.source,PersistentObject)):
            return f"At: {utcdatetime}\n{state.source.description}" # TODO: redo later as more ideas of persistent object take shape

    def __get_subject__(self,state:EventState):
        state_desc="Limit Alert"
        if(state.state['state']==LimitState.NORMAL):
            state_desc="Returned to Normal"
        if(isinstance(state.source,EventState)):
            asset=state.source.source['asset']['tag']
            measurement=state.source.source['measurement']['tag']
            return f"{state_desc} {asset} {measurement}"
        elif(isinstance(state.source,PersistentObject)):
            return state.source.description # TODO: redo later as more ideas of persistent object take shape

class AsyncSMTPAlertEmailTask(AsyncLimitAlertEmailTask):

    def __init__(self,smtp_server:str,smtp_port:int,smtp_user:str,smtp_pwd:str,sender:str,recipients:List[str],completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        if not EMAIL_REGEX.match(smtp_user):
            raise ValueError(f"{smtp_user} is not a valid email address")
        self.__smtp_user__=smtp_user
        if not SMTP_SERVER_REGEX.match(smtp_server):
            raise ValueError(f"{smtp_server} is not a valid host address")
        self.__smtp_server__=smtp_server
        self.__smtp_port__=smtp_port
        self.__smtp_pwd__=smtp_pwd
        super().__init__(sender=sender,recipients=recipients,completion_callback=completion_callback,timeout=timeout,persist_obj=persist_obj)

    # overriden execute
    async def __execute__(self, state:EventState, *args, **kwargs):
        subject=self.__get_subject__(state)
        body=self.__get_body__(state)

        # Setup the MIME
        message = MIMEText(body)
        message['From'] = self.__frm__
        message['To'] = ", ".join(self.__recipients__)
        message['Subject'] = subject

        # SMTP server credentials
        smtp_server = self.__smtp_server__
        smtp_port = self.__smtp_port__
        username = self.__smtp_user__
        password = self.__smtp_pwd__

        # Send the email
        server=None
        try:
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.ehlo()
            server.login(username, password)
            server.sendmail(message['From'], message['To'], message.as_string())
        except Exception as e:
            raise e
        finally:
            if(not server is None):
                server.quit()
        return True


class AsyncTwilioAlertEmailTask(AsyncLimitAlertEmailTask):

    def __init__(self,api_key:str,sender:str,recipients:List[str],completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        self.__api_key__=api_key
        super().__init__(sender=sender,recipients=recipients,completion_callback=completion_callback,timeout=timeout,persist_obj=persist_obj)

    async def __execute__(self, state:EventState, *args, **kwargs):
        subject=self.__get_subject__(state)
        body=self.__get_body__(state)

        recipients=",".join(self.__recipients__)

        # Setup message
        message = Mail(
            from_email=self.__frm__,
            to_emails=self.__recipients__,
            subject=subject,
            plain_text_content=body)
        try:
            sg = SendGridAPIClient(self.__api_key__)
            response = sg.send(message)
            print(response.status_code)
            print(response.body)
            print(response.headers)
        except Exception as e:
            raise e
        return True
