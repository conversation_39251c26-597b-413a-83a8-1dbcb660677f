import pytest
from datetime import datetime
from app.DeadCheckerService import check_dead_measurement
from app.enums import LimitState

# Test scenarios with mocked timestamps
TEST_SCENARIOS = [
    # (time_diff, threshold_seconds, expected_state, description)
    (0,   60 * 1000,  "NORMAL",   "Current measurement should be NORMAL (within threshold)"),
    (300 * 1000, 60 * 1000,  "DEAD", "5 min old measurement should be DEAD (beyond threshold)"),
    (300 * 1000, 600 * 1000, "NORMAL",   "5 min old measurement should be NORMAL (within 10 min threshold)"),
    (600 * 1000, 300 * 1000, "DEAD", "10 min old measurement should be DEAD (beyond 5 min threshold)"),
    (None, 60 * 1000, "DEAD",   "Never seen measurement should be DEAD"),
    (30 * 1000,  60 * 1000,  "NORMAL",   "Recent measurement should be NORMAL (within threshold)")
]

@pytest.mark.parametrize("time_diff, threshold_ms, expected_state, description", TEST_SCENARIOS)
def test_dead_measurement_detection(time_diff, threshold_ms, expected_state, description):
    """Test dead measurement detection with various scenarios"""
    now = int(datetime.now().timestamp() * 1000)
    # Mock last_seen time based on time_diff
    last_seen = None if time_diff is None else now - time_diff
    dead_duration = threshold_ms
    result = check_dead_measurement(
        alert_id=1000,
        measurement_id=2001,
        now=now,
        last_seen=last_seen,
        dead_duration_seconds=dead_duration,
        aggregate="avg",
        period="1m",
        asset_id=1,
        comparator_id=1,
        current_value=1.0 if last_seen else None
    )
    assert result.state == expected_state, \
        f"{description}: time_diff={time_diff}ms, threshold={threshold_ms}ms, expected={expected_state}"

def test_state_transitions():
    """Test state transitions between NORMAL and DEAD"""
    now = int(datetime.now().timestamp() * 1000)
    threshold_ms = 60 * 1000  # 1 minute threshold in ms
    dead_duration = threshold_ms
    last_seen = now  # Fixed last_seen time (ms)

    transitions = [
        (0,  "NORMAL",   "Initial state (within threshold)"),
        (30 * 1000, "NORMAL",   "30 seconds old (within threshold)"),
        (60 * 1000, "NORMAL", "Exactly at threshold"),
        (90 * 1000, "DEAD", "Beyond threshold")
    ]
    for time_diff, expected_state, description in transitions:
        check_time = now + time_diff
        result = check_dead_measurement(
            alert_id=2100,
            measurement_id=2002,
            now=check_time,
            last_seen=now,
            dead_duration_seconds=dead_duration,
            aggregate="avg",
            period="1m",
            asset_id=1,
            comparator_id=1,
            current_value=1.0
        )
        assert result.state == expected_state, \
            f"{description}: time_diff={time_diff}ms, expected={expected_state}"


def test_edge_cases():
    """Test edge cases for dead measurement detection"""
    now = int(datetime.now().timestamp() * 1000)
    last_seen = now  # Base timestamp (ms)

    test_cases = [
        # (time_diff_ms, threshold_ms, expected_state, description)
        (60 * 1000,  60 * 1000, "NORMAL", "Exactly at threshold"),
        (59 * 1000,  60 * 1000, "NORMAL",   "Just before threshold"),
        (61 * 1000,  60 * 1000, "DEAD", "Just after threshold"),
        (0,   0,  "NORMAL", "Zero duration"),
        (300 * 1000, 600 * 1000, "NORMAL",  "Within large threshold")
    ]
    for time_diff, threshold, expected_state, description in test_cases:
        check_time = last_seen + time_diff
        dead_duration = threshold
        result = check_dead_measurement(
            alert_id=2200,
            measurement_id=2003,
            now=check_time,
            last_seen=last_seen,
            dead_duration_seconds=dead_duration,
            aggregate="avg",
            period="1m",
            asset_id=1,
            comparator_id=1,
            current_value=1.0
        )
        assert result.state == expected_state, \
            f"{description}: time_diff={time_diff}ms, threshold={threshold}ms, expected={expected_state}"


def test_none_last_seen():
    """Test behavior when last_seen is None"""
    now = int(datetime.now().timestamp() * 1000)
    dead_duration = 60 * 1000
    
    result = check_dead_measurement(
        alert_id=2300,
        measurement_id=2004,
        now=now,
        last_seen=None,
        dead_duration_seconds=dead_duration,
        aggregate="avg",
        period="1m",
        asset_id=1,
        comparator_id=1,
        current_value=None
    )
    assert result.state == "DEAD", "Measurement with no last_seen time should be DEAD"
def test_empty_values_scenario():
    """Test dead alert when no data points exist (empty values)"""
    now = int(datetime.now().timestamp() * 1000)
    dead_duration = 60 * 1000
    result = check_dead_measurement(
        alert_id=2400,
        measurement_id=2005,
        now=now,
        last_seen=None,
        dead_duration_seconds=dead_duration,
        aggregate="avg",
        period="1m",
        asset_id=1,
        comparator_id=1,
        current_value=None
    )
    assert result.state == "DEAD", "No data points should result in DEAD state"