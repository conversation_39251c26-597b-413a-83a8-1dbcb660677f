import pytest
import asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from app import TSDBReaderService
from app.enums import Aggregate, AggregatePeriod

@pytest.mark.asyncio
async def test_read_data_new_makes_correct_api_call():
    """
    Test that read_data_new constructs the correct API URL and makes the correct GET request
    for an aggregated query (TWA, 15M period).
    This test mocks aiohttp.ClientSession to avoid real HTTP requests and inspects the called URL.
    """
    customer = 123
    measurements = [1, 2, 3]
    aggregate = Aggregate.TWA
    period = AggregatePeriod._15M
    expected_host = TSDBReaderService.TS_API_HOST
    expected_meas_ids = ','.join(map(str, measurements))
    # Calculate expected start and end
    end = round(TSDBReaderService.datetime.datetime.now().timestamp() * 1000)
    bucket_size = TSDBReaderService.BucketSize_Dict[period.name].value
    start = max(end - TSDBReaderService.MINSPAN, end - bucket_size)
    expected_url_start = f"{expected_host}/timeseries/agg/{customer}?meas_id={expected_meas_ids}"

    with patch('aiohttp.ClientSession', autospec=True) as mock_session_cls:
        mock_session = MagicMock()
        mock_session_cls.return_value.__aenter__.return_value = mock_session
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=[])
        # Use MagicMock for session.get and set up async context manager
        mock_session.get = MagicMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        mock_session.get.return_value.__aexit__.return_value = None

        await TSDBReaderService.read_data_new(customer, measurements, aggregate, period)
        called_url = mock_session.get.call_args[0][0]
        assert called_url.startswith(expected_url_start), (
            f"URL called was {called_url}, expected to start with {expected_url_start}")
        assert "agg=" in called_url and "agg_period=" in called_url, (
            f"URL called was {called_url}, expected to contain 'agg=' and 'agg_period='")

@pytest.mark.asyncio
async def test_read_data_new_max_1h_aggregate():
    """
    Test that read_data_new constructs the correct API URL for MAX aggregate and 1H period.
    """
    customer = 456
    measurements = [10, 20]
    aggregate = Aggregate.MAX
    period = AggregatePeriod._1H
    expected_host = TSDBReaderService.TS_API_HOST
    expected_meas_ids = ','.join(map(str, measurements))
    end = round(TSDBReaderService.datetime.datetime.now().timestamp() * 1000)
    bucket_size = TSDBReaderService.BucketSize_Dict[period.name].value
    start = max(end - TSDBReaderService.MINSPAN, end - bucket_size)
    expected_url_start = f"{expected_host}/timeseries/agg/{customer}?meas_id={expected_meas_ids}"

    with patch('aiohttp.ClientSession', autospec=True) as mock_session_cls:
        mock_session = MagicMock()
        mock_session_cls.return_value.__aenter__.return_value = mock_session
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=[])
        mock_session.get = MagicMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        mock_session.get.return_value.__aexit__.return_value = None

        await TSDBReaderService.read_data_new(customer, measurements, aggregate, period)
        called_url = mock_session.get.call_args[0][0]
        assert called_url.startswith(expected_url_start), (
            f"URL called was {called_url}, expected to start with {expected_url_start}")
        assert "agg=max" in called_url and "agg_period=1hr" in called_url, (
            f"URL called was {called_url}, expected to contain 'agg=max' and 'agg_period=1hr'")

@pytest.mark.asyncio
async def test_read_data_new_none_aggregate_history():
    """
    Test that read_data_new uses the /timeseries/history/ endpoint when Aggregate.NONE is used.
    """
    customer = 789
    measurements = [100]
    aggregate = Aggregate.NONE
    period = AggregatePeriod.DAILY
    expected_host = TSDBReaderService.TS_API_HOST
    expected_meas_ids = ','.join(map(str, measurements))
    end = round(TSDBReaderService.datetime.datetime.now().timestamp() * 1000)
    start = end - TSDBReaderService.MINSPAN
    expected_url_start = f"{expected_host}/timeseries/history/{customer}?meas_id={expected_meas_ids}"

    with patch('aiohttp.ClientSession', autospec=True) as mock_session_cls:
        mock_session = MagicMock()
        mock_session_cls.return_value.__aenter__.return_value = mock_session
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=[])
        mock_session.get = MagicMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        mock_session.get.return_value.__aexit__.return_value = None

        await TSDBReaderService.read_data_new(customer, measurements, aggregate, period)
        called_url = mock_session.get.call_args[0][0]
        assert called_url.startswith(expected_url_start), (
            f"URL called was {called_url}, expected to start with {expected_url_start}")
        assert "agg=" not in called_url and "agg_period=" not in called_url, (
            f"URL called was {called_url}, expected not to contain 'agg=' or 'agg_period='")

@pytest.mark.asyncio
async def test_read_data_new_raw_period_history():
    """
    Test that read_data_new uses the /timeseries/history/ endpoint when period is 'raw'.
    """
    customer = 321
    measurements = [200, 300]
    aggregate = Aggregate.TWA
    period = AggregatePeriod.raw
    expected_host = TSDBReaderService.TS_API_HOST
    expected_meas_ids = ','.join(map(str, measurements))
    end = round(TSDBReaderService.datetime.datetime.now().timestamp() * 1000)
    start = end - TSDBReaderService.MINSPAN
    expected_url_start = f"{expected_host}/timeseries/history/{customer}?meas_id={expected_meas_ids}"

    with patch('aiohttp.ClientSession', autospec=True) as mock_session_cls:
        mock_session = MagicMock()
        mock_session_cls.return_value.__aenter__.return_value = mock_session
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=[])
        mock_session.get = MagicMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        mock_session.get.return_value.__aexit__.return_value = None

        await TSDBReaderService.read_data_new(customer, measurements, aggregate, period)
        called_url = mock_session.get.call_args[0][0]
        assert called_url.startswith(expected_url_start), (
            f"URL called was {called_url}, expected to start with {expected_url_start}")
        assert "agg=" not in called_url and "agg_period=" not in called_url, (
            f"URL called was {called_url}, expected not to contain 'agg=' or 'agg_period='")

@pytest.mark.asyncio
@pytest.mark.parametrize("aggregate", [
    Aggregate.TOTAL, Aggregate.TWA, Aggregate.MAX, Aggregate.MIN, Aggregate.AVG, Aggregate.STD,
    Aggregate.RATETOTAL, Aggregate.DELTATWA, Aggregate.DELTAAVG, Aggregate.DELTAMAX, Aggregate.DELTAMIN, Aggregate.DELTASTD
])
async def test_read_data_new_all_aggregates(aggregate):
    """
    Test that read_data_new constructs the correct API URL for all Aggregate values (except NONE).
    """
    customer = 111
    measurements = [42]
    period = AggregatePeriod._15M
    expected_host = TSDBReaderService.TS_API_HOST
    expected_meas_ids = ','.join(map(str, measurements))
    end = round(TSDBReaderService.datetime.datetime.now().timestamp() * 1000)
    bucket_size = TSDBReaderService.BucketSize_Dict[period.name].value
    start = max(end - TSDBReaderService.MINSPAN, end - bucket_size)
    expected_url_start = f"{expected_host}/timeseries/agg/{customer}?meas_id={expected_meas_ids}"

    with patch('aiohttp.ClientSession', autospec=True) as mock_session_cls:
        mock_session = MagicMock()
        mock_session_cls.return_value.__aenter__.return_value = mock_session
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=[])
        mock_session.get = MagicMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        mock_session.get.return_value.__aexit__.return_value = None

        await TSDBReaderService.read_data_new(customer, measurements, aggregate, period)
        called_url = mock_session.get.call_args[0][0]
        assert called_url.startswith(expected_url_start), (
            f"Aggregate {aggregate}: URL called was {called_url}, expected to start with {expected_url_start}")
        assert f"agg={aggregate.value}" in called_url and f"agg_period={period.value}" in called_url, (
            f"Aggregate {aggregate}: URL called was {called_url}, expected to contain 'agg={aggregate.value}' and 'agg_period={period.value}'")

@pytest.mark.asyncio
@pytest.mark.parametrize("period", [
    AggregatePeriod._1M, AggregatePeriod._2M, AggregatePeriod._5M, AggregatePeriod._10M, AggregatePeriod._15M,
    AggregatePeriod._20M, AggregatePeriod._30M, AggregatePeriod._1H, AggregatePeriod._2H, AggregatePeriod._4H,
    AggregatePeriod._6H, AggregatePeriod._8H, AggregatePeriod._12H, AggregatePeriod.DAILY, AggregatePeriod.WEEKLY, AggregatePeriod.MONTHLY
])
async def test_read_data_new_all_periods(period):
    """
    Test that read_data_new constructs the correct API URL for all AggregatePeriod values (except NONE and raw).
    """
    customer = 222
    measurements = [99]
    aggregate = Aggregate.TWA
    expected_host = TSDBReaderService.TS_API_HOST
    expected_meas_ids = ','.join(map(str, measurements))
    end = round(TSDBReaderService.datetime.datetime.now().timestamp() * 1000)
    bucket_size = TSDBReaderService.BucketSize_Dict[period.name].value if period.name in TSDBReaderService.BucketSize_Dict else None
    start = max(end - TSDBReaderService.MINSPAN, end - bucket_size) if bucket_size else end - TSDBReaderService.MINSPAN
    expected_url_start = f"{expected_host}/timeseries/agg/{customer}?meas_id={expected_meas_ids}"

    with patch('aiohttp.ClientSession', autospec=True) as mock_session_cls:
        mock_session = MagicMock()
        mock_session_cls.return_value.__aenter__.return_value = mock_session
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=[])
        mock_session.get = MagicMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        mock_session.get.return_value.__aexit__.return_value = None

        await TSDBReaderService.read_data_new(customer, measurements, aggregate, period)
        called_url = mock_session.get.call_args[0][0]
        assert called_url.startswith(expected_url_start), (
            f"Period {period}: URL called was {called_url}, expected to start with {expected_url_start}")
        assert f"agg={aggregate.value}" in called_url and f"agg_period={period.value}" in called_url, (
            f"Period {period}: URL called was {called_url}, expected to contain 'agg={aggregate.value}' and 'agg_period={period.value}'")
