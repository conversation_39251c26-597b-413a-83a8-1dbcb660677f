name: Deployment

on:
  pull_request:
    types: [opened, reopened, edited, synchronize]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment Environment'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - stage
          - prod
      release-tag:
        description: 'Release Tag to deploy - Required for Prod'
        required: false
        default: ''

env:
  AWS_REGION: 'us-east-1'
  ECR_REGISTRY: '067172429169.dkr.ecr.us-east-1.amazonaws.com'

permissions:
  id-token: write   # This is required for requesting the JWT
  contents: read    # This is required for actions/checkout
  issues: read
  checks: write
  pull-requests: write

jobs:
  deploy:
    name: Build and Deploy
    if: github.event_name != 'pull_request'
    runs-on: ${{ github.event.inputs.environment == 'stage' && 'stage' || 'on-prem' }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
        with:
          ref: ${{ github.event.inputs.release-tag || github.sha }}
          token: ${{ secrets.GHA_TOKEN }}
          fetch-depth: 0
          submodules: recursive

      - name: Set environment variables
        run: |
          if [ "${{ github.event.inputs.environment }}" == "prod" ]; then
            echo "ENV=prod" >> $GITHUB_ENV
            echo "IMAGE_TAG=${{ github.event.inputs.release-tag }}" >> $GITHUB_ENV
            echo "CLUSTER_NAME=brompton-energy-eks-prod" >> $GITHUB_ENV
          elif [ "${{ github.event.inputs.environment }}" == "stage" ]; then
            echo "ENV=stage" >> $GITHUB_ENV
            echo "IMAGE_TAG=${{ github.sha }}" >> $GITHUB_ENV
            echo "CLUSTER_NAME=staging" >> $GITHUB_ENV
          else
            echo "ENV=dev" >> $GITHUB_ENV
            echo "IMAGE_TAG=${{ github.sha }}" >> $GITHUB_ENV
            echo "CLUSTER_NAME=default" >> $GITHUB_ENV
          fi

      - name: Set ECR_REPO_NAME
        run: echo "ECR_REPO_NAME=brompton-energy/celery-${{ env.ENV }}" >> $GITHUB_ENV

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v1.7.0
        with:
          role-to-assume: ${{ secrets.PIPELINE_ROLE }} 
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push Docker image
        run: |
          docker build -t $ECR_REPO_NAME:$IMAGE_TAG -f Dockerfile .
          docker tag $ECR_REPO_NAME:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPO_NAME:$IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPO_NAME:$IMAGE_TAG

      - name: Setup kubectl
        if: ${{ github.event.inputs.environment != 'dev' && github.event.inputs.environment != 'stage' }}
        uses: azure/setup-kubectl@v1
        with:
          version: "latest"

      - name: Update or Check Kubernetes Context
        run: |
          if [[ "${{ github.event.inputs.environment }}" == "prod" ]]; then
            aws eks update-kubeconfig --name "$CLUSTER_NAME" --region "${{ env.AWS_REGION }}"
          elif [[ "${{ github.event.inputs.environment }}" == "stage" ]]; then
            kubectl config use-context staging
          else
            kubectl config current-context
          fi

      - name: Install envsubst
        run: |
          if ! command -v envsubst &> /dev/null; then
            echo "Installing gettext package for envsubst..."
            apt-get update && apt-get install -y gettext-base
          fi

      - name: Apply ConfigMap
        run: |
          echo "Applying ConfigMap for $ENV environment..."
          kubectl apply -f k8s-manifest/config/$ENV-configmap.yaml 

      - name: Deploy Kubernetes resources
        run: |
          echo "Deploying Kubernetes resources..."
          cd k8s-manifest
          
          # Export all environment variables for envsubst
          export ENV=$ENV
          export IMAGE_TAG=$IMAGE_TAG
          export ECR_REGISTRY=$ECR_REGISTRY
          export ECR_REPO_NAME=$ECR_REPO_NAME
          
          # Deploy scheduler-api
          echo "Deploying scheduler-api..."
          envsubst < scheduler-api/deployment.yaml | kubectl apply -f - 
          
          # Deploy celery-beat
          echo "Deploying celery-beat..."
          envsubst < celery-beat/deployment.yaml | kubectl apply -f - 
          
          # Deploy alert-engine-worker
          echo "Deploying alert-engine-worker..."
          envsubst < alert-engine-worker/statefulset.yaml | kubectl apply -f - 
          
          # Deploy services if they exist
          if [ -f scheduler-api/service.yaml ]; then
            echo "Deploying scheduler-api service..."
            envsubst < scheduler-api/service.yaml | kubectl apply -f - 
          fi
          
          if [ -f celery-beat/service.yaml ]; then
            echo "Deploying celery-beat service..."
            envsubst < celery-beat/service.yaml | kubectl apply -f - 
          fi
          
          if [ -f alert-engine-worker/service.yaml ]; then
            echo "Deploying alert-engine-worker service..."
            envsubst < alert-engine-worker/service.yaml | kubectl apply -f - 
          fi

  test_and_notify:
    name: Run Tests and Notify
    runs-on: ${{ github.event.inputs.environment == 'stage' && 'stage' || 'on-prem' }}
    if: github.event_name == 'pull_request'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          repository: bromptonenergy/alert-service
          ref: ${{ github.event.inputs.release-tag || github.sha }}
          token: ${{ secrets.GHA_TOKEN }}
          fetch-depth: 0
          submodules: recursive

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Create virtual environment and run tests
        run: |
          export PYTHONPATH=$(pwd)
          python -m venv .venv
          source .venv/bin/activate
          pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-html pytest-asyncio pytest-cov
          pytest tests \
            --html=pytest-report.html \
            --self-contained-html \
            --junitxml=pytest-report.xml \
            --cov=. \
            --cov-report=html || true

      - name: Set shared environment variables
        run: |
          echo "GH_SHA=${{ github.sha }}" >> $GITHUB_ENV
          echo "GH_REPO=${{ github.repository }}" >> $GITHUB_ENV
          echo "GH_RUN_ID=${{ github.run_id }}" >> $GITHUB_ENV
          echo "GH_ACTOR=${{ github.actor }}" >> $GITHUB_ENV
          echo "GH_PR_NUMBER=${{ github.event.pull_request.number }}" >> $GITHUB_ENV
          echo "ENVIRONMENT=${{ github.event.inputs.environment || 'N/A' }}" >> $GITHUB_ENV
          echo "BRANCH_NAME=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_ENV
      
      - name: Upload Pytest HTML Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: pytest-report
          path: pytest-report.html
      
      - name: Upload Coverage Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-html
          path: htmlcov
      
      - name: Ensure report is in workspace root
        if: always()
        run: |
          if [ -f TSDB_Utility/tests/pytest-report.xml ]; then
            cp TSDB_Utility/tests/pytest-report.xml .
          fi
      
      - name: List Workspace Contents
        if: always()
        run: |
          echo "Current directory: $(pwd)"
          echo "Listing files:"
          ls -lah
      
      - name: Get current time (IST)
        id: current_time
        run: echo "time=$(TZ=Asia/Kolkata date)" >> $GITHUB_OUTPUT
      
      - name: Publish Test Results to PR
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always()
        with:
          files: pytest-report.xml
          comment_mode: always
          check_name: Alert-service Test Results
      
      - name: Send PR Test Summary to Teams
        if: github.event_name == 'pull_request'
        env:
          GH_PR_NUMBER: ${{ github.event.pull_request.number }}
          GH_ACTOR: ${{ github.actor }}
          GH_REPO: ${{ github.repository }}
          GH_RUN_ID: ${{ github.run_id }}
        run: |
          python3 - <<EOF
          import xml.etree.ElementTree as ET
          import json
      
          xml_path = "pytest-report.xml"
          tree = ET.parse(xml_path)
          suite = tree.getroot()
          if suite.tag != "testsuite":
              suite = suite.find("testsuite")
      
          tests = int(suite.attrib.get("tests", 0))
          errors = int(suite.attrib.get("errors", 0))
          failures = int(suite.attrib.get("failures", 0))
          skipped = int(suite.attrib.get("skipped", 0))
          passed = tests - errors - failures - skipped
      
          summary = f"""
          **Total:** {tests}  
          ✅ **Passed:** {passed}  
          ❌ **Failed:** {failures}  
          ⚠️ **Errors:** {errors}  
          🚫 **Skipped:** {skipped}
          """
      
          pr_number = "${GH_PR_NUMBER}"
          actor = "${GH_ACTOR}"
          repo = "${GH_REPO}"
          run_id = "${GH_RUN_ID}"
      
          card = {
              "@type": "MessageCard",
              "@context": "http://schema.org/extensions",
              "summary": "PR Pytest Results",
              "themeColor": "0076D7",
              "title": f"📦 PR #{pr_number} Test Summary",
              "sections": [{
                  "activityTitle": f"Pull Request #{pr_number} by {actor}",
                  "text": summary
              }],
              "potentialAction": [{
                  "@type": "OpenUri",
                  "name": "View Workflow",
                  "targets": [{
                      "os": "default",
                      "uri": f"https://github.com/{repo}/actions/runs/{run_id}"
                  }]
              }]
          }
      
          with open("summary.json", "w") as f:
              json.dump(card, f, indent=2)
          EOF
      
          curl -H "Content-Type: application/json" \
                -d @summary.json \
                "${{ secrets.GH_TEAMS_INTEGRATION }}"
                
  notification:
    name: Teams Notification
    needs: [deploy]
    if: github.event_name != 'pull_request'
    runs-on: ${{ github.event.inputs.environment == 'stage' && 'stage' || 'on-prem' }}
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
        with:
          ref: ${{ github.event.inputs.release-tag || github.sha }}
          fetch-depth: 0

      - name: Set variables
        id: set_vars
        run: |
          BN="${GITHUB_REF#refs/heads/}"
          RT="${{ github.event.inputs.release-tag || github.sha }}"
          CM="$(git log -1 --pretty=%B | tr '\n' ' ' | sed 's/  */ /g')"
          echo "BRANCH_NAME=$BN" >> $GITHUB_ENV
          echo "RELEASE_TAG=$RT" >> $GITHUB_ENV
          echo "COMMIT_MESSAGE=$CM" >> $GITHUB_ENV
          echo "ENVIRONMENT=${{ github.event.inputs.environment }}" >> $GITHUB_ENV

      - name: Get current time (IST)
        id: current_time
        run: echo "time=$(TZ=Asia/Kolkata date)" >> $GITHUB_OUTPUT

      - name: Send Teams Notification
        run: |
          STATUS=${{ needs.deploy.result }}
          COLOR="00FF00"
          TITLE="✅ Deployment Succeeded"
          MESSAGE="${{ needs.deploy.outputs.success_reason || 'Deployment process Succeeded' }}"
          
          if [ "$STATUS" != "success" ]; then
            COLOR="FF0000"
            TITLE="❌ Deployment Failed"
            MESSAGE="${{ needs.deploy.outputs.failure_reason || 'Deployment process failed' }}"
          fi
          
          ESCAPED_COMMIT_MESSAGE=$(echo "${{ env.COMMIT_MESSAGE }}" | sed 's/"/\\"/g')
          
          cat <<EOF > card_payload.json
          {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "themeColor": "$COLOR",
            "summary": "$TITLE",
            "sections": [{
              "activityTitle": "$TITLE",
              "facts": [
                { "name": "Environment", "value": "${{ env.ENVIRONMENT }}" },
                { "name": "Branch Name", "value": "${{ env.BRANCH_NAME }}" },
                { "name": "Repository", "value": "${GITHUB_REPOSITORY}" },
                { "name": "Tag/Commit", "value": "${{ env.RELEASE_TAG }}" },
                { "name": "Commit Link", "value": "[${GITHUB_SHA}](${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/commit/${GITHUB_SHA})" },
                { "name": "Commit Message", "value": "${ESCAPED_COMMIT_MESSAGE}" },
                { "name": "Time (IST)", "value": "${{ steps.current_time.outputs.time }}" },
                { "name": "Author", "value": "${GITHUB_ACTOR}" },
                { "name": "Status", "value": "$MESSAGE" }
              ]
            }]
          }
          EOF
          
          curl -H "Content-Type: application/json" -X POST -d @card_payload.json "${{ secrets.GH_TEAMS_INTEGRATION }}"
