"""
Utility functions for alert processing to ensure consistency across all alert types.
"""

from datetime import datetime
from typing import Optional
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging

logger = setup_logging()


def get_event_timestamp(ts: Optional[int], alert_type: str) -> int:
    """
    Standardize timestamp handling across all alert types.
    
    Args:
        ts: Provided timestamp in milliseconds (can be None)
        alert_type: Type of alert for logging purposes
        
    Returns:
        int: Timestamp in milliseconds
    """
    if ts is not None:
        return ts
    else:
        # Use current time as fallback
        current_ts = int(datetime.utcnow().timestamp() * 1000)
        logger.warning(f"[{alert_type}] No timestamp provided, using current time: {current_ts}")
        return current_ts


def get_event_input_value(alert_type: str, measurement_value: Optional[float], 
                         current_state: str, last_known_value: Optional[float] = None) -> float:
    """
    Standardize input value handling across all alert types.
    
    Args:
        alert_type: Type of alert (NOMINAL, DEAD, STALE)
        measurement_value: Current measurement value
        current_state: Current alert state
        last_known_value: Last known good value (for DEAD alerts)
        
    Returns:
        float: Value to store in event
    """
    # If we have a current measurement value, use it
    if measurement_value is not None:
        logger.debug(f"[{alert_type}] Using measurement value: {measurement_value}")
        return measurement_value
    
    # For DEAD alerts, try to preserve last known value
    elif current_state == "DEAD" and last_known_value is not None:
        logger.info(f"[{alert_type}] DEAD state - using last known value: {last_known_value}")
        return last_known_value
    
    # For DEAD alerts with no data at all, use 0
    elif current_state == "DEAD":
        logger.info(f"[{alert_type}] DEAD state - no data available, using 0")
        return 0.0
    
    # For other states with no measurement value, use 0 as fallback
    else:
        logger.warning(f"[{alert_type}] No measurement value available for state {current_state}, using 0")
        return 0.0


def validate_state_transition(alert_type: str, prev_state: str, new_state: str) -> bool:
    """
    Validate that state transitions are logical.
    
    Args:
        alert_type: Type of alert
        prev_state: Previous state
        new_state: New state
        
    Returns:
        bool: True if transition is valid
    """
    # Define valid transitions for each alert type
    valid_transitions = {
        "NOMINAL": {
            "NORMAL": ["EXCEEDED"],
            "EXCEEDED": ["NORMAL"]
        },
        "DEAD": {
            "NORMAL": ["DEAD"],
            "DEAD": ["NORMAL"]
        },
        "STALE": {
            "NORMAL": ["STALE"],
            "STALE": ["NORMAL"]
        }
    }
    
    if alert_type not in valid_transitions:
        logger.warning(f"Unknown alert type: {alert_type}")
        return True  # Allow unknown types for now
    
    if prev_state not in valid_transitions[alert_type]:
        logger.warning(f"Unknown previous state {prev_state} for {alert_type}")
        return True  # Allow unknown states for now
    
    if new_state in valid_transitions[alert_type][prev_state]:
        return True
    
    logger.warning(f"Invalid state transition for {alert_type}: {prev_state} -> {new_state}")
    return False


def log_event_creation(alert_type: str, event_id: Optional[int], alert_id: int, 
                      state: str, value: float, timestamp: int) -> None:
    """
    Standardize event creation logging across all alert types.
    
    Args:
        alert_type: Type of alert
        event_id: Created event ID (can be None if creation failed)
        alert_id: Alert ID
        state: Alert state
        value: Input value
        timestamp: Event timestamp
    """
    if event_id is not None:
        logger.info(
            f"[EVENT-INSERT] type={alert_type} event_id={event_id} alert_id={alert_id} "
            f"state={state} value={value} timestamp={timestamp}"
        )
    else:
        logger.error(
            f"[EVENT-INSERT-FAILED] type={alert_type} alert_id={alert_id} "
            f"state={state} value={value} timestamp={timestamp}"
        )


def log_excursion_creation(alert_type: str, alert_id: int, end_time: int) -> None:
    """
    Standardize excursion creation logging across all alert types.
    
    Args:
        alert_type: Type of alert
        alert_id: Alert ID
        end_time: Excursion end time
    """
    logger.info(f"[EXCURSION-INSERT] type={alert_type} alert_id={alert_id} end_time={end_time}")


def log_notification_sent(alert_type: str, event_id: int, alert_id: int, 
                         state: str, timestamp: int) -> None:
    """
    Standardize notification logging across all alert types.
    
    Args:
        alert_type: Type of alert
        event_id: Event ID
        alert_id: Alert ID
        state: Alert state
        timestamp: Event timestamp
    """
    logger.info(
        f"[NOTIFICATION-SENT] type={alert_type} event_id={event_id} alert_id={alert_id} "
        f"state={state} timestamp={timestamp}"
    )
