{"app/tests/test_bug_1573_alert_creation.py::test_create_nominal_alert_no_aggregation": true, "app/tests/test_bug_1573_alert_creation.py::test_create_stale_alert": true, "app/tests/test_bug_1573_alert_creation.py::test_create_dead_alert": true, "app/tests/test_bug_1573_alert_creation.py::test_create_nominal_alert_with_twa": true, "app/tests/test_bug_1573_alert_creation.py::test_invalid_threshold_type": true, "app/tests/test_bug_1573_alert_creation.py::test_invalid_aggregation": true, "tests/test_none_aggregate_fix.py::TestNoneAggregateFix::test_check_limit_with_none_aggregate": true, "tests/test_none_aggregate_fix.py::TestNoneAggregateFix::test_get_measurements_and_alerts_new_with_none_aggregate": true, "tests/test_none_aggregate_fix.py::TestNoneAggregateFix::test_read_data_with_none_aggregate": true, "tests/test_dead_alert.py::test_dead_alert_scenarios": true, "tests/test_dead_alert.py::test_dead_alert_api": true, "tests/test_nominal_alert.py::test_nominal_alert_api": true, "tests/test_nominal_alert.py::test_nominal_alert_scenarios": true, "tests/test_alert_creation.py": true, "tests/test_alert_deletion.py": true, "tests/test_alert_update.py": true, "tests/test_alert_evaluation.py": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[5-LimitState.DEAD]": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[20-LimitState.NORMAL]": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_alert_with_no_data": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[5-LimitState.NORMAL]": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[15-LimitState.DEAD]": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_alert_api": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check": true, "tests/test_nominal_alert.py::TestNominalAlert::test_create_nominal_alert_api": true, "tests/test_stale_alert.py::TestStaleAlert::test_stale_threshold_check": true, "tests/test_stale_alert.py::TestStaleAlert::test_create_stale_alert_api": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_alert_create": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_evaluation[5-LimitState.NORMAL]": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_evaluation[15-LimitState.DEAD]": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_evaluation[60-LimitState.DEAD]": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_recovery": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_create": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_evaluation[50.0-50.0-LimitState.NORMAL]": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_with_deadband": true, "tests/test_stale_alert.py::TestStaleAlert::test_stale_alert_create": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_alert_creation": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[50.0-50.0-1.0-LimitState.NORMAL]": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[50.0-49.5-1.0-LimitState.EXCEEDED]": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_alert_creation": true, "tests/test_stale_alert.py::TestStaleAlert::test_stale_alert_creation": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_threshold_check[50.0-50.0-1.0-LimitState.EXCEEDED]": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_threshold_check[test_case4]": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_timestamp_validation[test_case0]": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_timestamp_validation[test_case3]": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_value_validation[test_case3]": true, "tests/test_dead_alert.py::TestDeadAlert::test_dead_alert_creation_failures": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_invalid_combinations[test_case0]": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_invalid_combinations[test_case3]": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_deadband_validation[test_case1]": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_deadband_validation[test_case2]": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_deadband_validation[test_case3]": true, "tests/test_nominal_alert.py::TestNominalAlert::test_nominal_alert_creation_failures": true, "tests/test_stale_alert.py::TestStaleAlert::test_stale_redis_failures[test_case0]": true, "tests/test_stale_alert.py::TestStaleAlert::test_stale_redis_failures[test_case1]": true, "tests/test_stale_alert.py::TestStaleAlert::test_stale_redis_failures[test_case2]": true, "tests/test_stale_alert.py::TestStaleAlert::test_stale_timestamp_validation[test_case0]": true, "tests/test_stale_alert.py::TestStaleAlert::test_stale_timestamp_validation[test_case1]": true, "tests/test_stale_alert.py::TestStaleAlert::test_stale_alert_creation_failures": true, "tests/test_alerts.py::test_alert_detection[measurement:1001-*-5.0-10.0-True]": true, "tests/test_alerts.py::test_alert_detection[measurement:1001-*-15.0-10.0-False]": true, "tests/test_alerts.py::test_alert_detection[measurement:1001-*--5.0-10.0-True]": true, "tests/test_alerts.py::test_alert_detection[measurement:1001-*--15.0-10.0-False]": true, "tests/test_alerts.py::test_alert_detection[measurement:1001-*-0.0-10.0-True]": true, "tests/test_alerts.py::test_alert_detection[measurement:1001-*-10.0-10.0-True]": true, "tests/test_alerts.py::test_multiple_measurements": true, "tests/test_alerts.py::test_range_values": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2001-60-DEAD-Current measurement should be DEAD due to < threshold]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2002-60-NORMAL-5 min old measurement should be NORMAL when threshold is 1 min]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2002-600-DEAD-5 min old measurement should be DEAD when threshold is 10 min]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2003-300-NORMAL-10 min old measurement should be NORMAL when threshold is 5 min]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2005-60-DEAD-Recently seen measurement should be DEAD due to < threshold]": true, "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1004-10.0-EXCEEDED-Value well beyond negative threshold]": true, "tests/test_nominal_alert.py::test_nominal_alert_with_redis[measurement:1004-10.0-EXCEEDED-Value beyond negative threshold]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2002-60-NORMAL-5 min old measurement should be normal (> threshold)]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2003-300-NORMAL-10 min old measurement should be normal with 5 min threshold]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2001-60-NORMAL-Current measurement (0s old) should be NORMAL with 60s threshold]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2002-60-DEAD-5 min old measurement should be DEAD with 1 min threshold]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2002-600-NORMAL-5 min old measurement should be NORMAL with 10 min threshold]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2003-300-DEAD-10 min old measurement should be DEAD with 5 min threshold]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2005-60-NORMAL-Recent measurement (30s old) should be NORMAL with 60s threshold]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2001-dead_duration0-NORMAL-Current measurement (0s old) should be NORMAL with 60s threshold]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2002-dead_duration1-DEAD-5 min old measurement should be DEAD with 1 min threshold]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2002-dead_duration2-NORMAL-5 min old measurement should be NORMAL with 10 min threshold]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2003-dead_duration3-DEAD-10 min old measurement should be DEAD with 5 min threshold]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[2005-dead_duration5-NORMAL-Recent measurement (30s old) should be NORMAL with 60s threshold]": true, "tests/test_nominal_alert.py::test_nominal_alerts[-15.0--10.0-EXCEEDED-Below negative threshold]": true, "tests/test_stale_alert.py::test_stale_measurement_detection[30-5.0-5.0-15-CompareOperation.GT-STALE-30 min old unchanged value should be STALE]": true, "tests/test_stale_alert.py::test_stale_measurement_detection[30-5.0-5.0-30-CompareOperation.GE-STALE-Exactly at threshold with GE should be STALE]": true, "tests/test_stale_alert.py::test_stale_measurement_detection[5-7.0-7.0-5-CompareOperation.GE-STALE-Just at threshold with GE should be STALE]": true, "tests/test_nominal_alert.py::test_nominal_alerts[-15.0-10.0-EXCEEDED-Negative value above absolute threshold]": true, "tests/test_nominal_alert.py::test_absolute_value_comparison": true, "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff1-5.0-15-CompareOperation.GT-STALE-30 min old unchanged value should be STALE]": true, "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff5-5.0-30-CompareOperation.GE-STALE-Exactly at threshold with GE should be STALE]": true, "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff6-7.0-5-CompareOperation.GE-STALE-Just at threshold with GE should be STALE]": true, "tests/test_dead_alert_state_events_data.py::TestDeadAlertStateTransitions::test_dead_alert_state_transitions_with_events": true, "tests/test_dead_alert.py::test_dead_measurement_detection[0-60-DEAD-Current measurement should be DEAD (within threshold)]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[300-60-NORMAL-5 min old measurement should be NORMAL (beyond threshold)]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[300-600-DEAD-5 min old measurement should be DEAD (within 10 min threshold)]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[600-300-NORMAL-10 min old measurement should be NORMAL (beyond 5 min threshold)]": true, "tests/test_dead_alert.py::test_dead_measurement_detection[30-60-DEAD-Recent measurement should be DEAD (within threshold)]": true, "tests/test_alert_e2e.py": true, "tests/test_alert_limit_field.py": true, "tests/test_alert_redis.py": true, "tests/test_alert_redis_integration.py": true, "tests/test_alert_core.py::test_alert_flow": true, "tests/test_alert.py": true, "tests/test_nominal_alert_flow.py::TestNominalAlerts::test_nominal_alert_exceeded_state": true, "tests/test_nominal_alert_flow.py::TestNominalAlerts::test_nominal_alert_return_to_normal": true, "tests/test_nominal_alert_flow.py::TestNominalAlerts::test_nominal_alert_deadband": true, "tests/test_nominal_alert_flow.py::TestNominalAlerts::test_nominal_alert_no_data": true, "tests/test_dead_alert_sequence.py::test_dead_alert_custom": true, "tests/test_stale_alert_sequence.py::test_stale_alert_sequence": true, "tests/Intergration_test/test_stale_e2e.py": true, "tests/test_stale_e2e.py::test_stale_alert_triggers_only_on_state_change": true, "tests/test_stale_alert_sequence.py": true, "tests/test_dead_alert_sequence.py::test_dead_alert_sequence": true, "tests/test_dead_e2e.py::test_evaluate_alert_task_dead_state_transition": true, "tests/test_nominal_e2e.py::test_evaluate_alert_task_nominal_state_transition": true, "tests/test_stale_e2e.py::test_evaluate_alert_task_stale_state_transition": true, "tests/test_dead_alert.py::test_dead_alert_input_values": true, "tests/test_dead_alert_values.py::test_dead_alert_values[100.5-30-60-DEAD-0-DEAD state should show 0 even with non-zero current value]": true, "tests/test_dead_alert_values.py::test_dead_alert_values[0-30-60-DEAD-0-DEAD state should show 0 when current value is 0]": true, "tests/test_dead_alert_values.py::test_dead_alert_values[None-30-60-DEAD-0-DEAD state should show 0 when no current value]": true, "tests/test_dead_alert_values.py::test_dead_alert_values[100.5-120-60-NORMAL-100.5-NORMAL state should show actual current value]": true, "tests/test_dead_alert_values.py::test_dead_alert_values[0-120-60-NORMAL-0-NORMAL state should show 0 when current value is 0]": true, "tests/test_dead_alert_values.py::test_dead_alert_values[None-120-60-NORMAL-0-NORMAL state should show 0 when no current value]": true, "tests/test_dead_alert_values.py::test_state_transition_value_changes": true, "tests/test_dead_alert_values.py": true, "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff0-10.0-15-CompareOperation.GT-NORMAL-Current measurement should be NORMAL]": true, "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff1-10.0-15-CompareOperation.GT-STALE-30 min old unchanged value should be STALE]": true, "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff3-10.0-15-CompareOperation.GT-NORMAL-Recent measurement should be NORMAL]": true, "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff4-10.0-30-CompareOperation.GT-STALE-Old unchanged measurement should be STALE]": true, "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff5-10.0-30-CompareOperation.GE-STALE-Exactly at threshold with GE should be STALE]": true, "tests/test_stale_alert.py::test_stale_measurement_detection[time_diff6-10.0-5-CompareOperation.GE-STALE-Just at threshold with GE should be STALE]": true, "tests/test_stale_logic_paths.py::test_complete_stale_workflow": true, "tests/test_stale_logic_paths.py::test_both_comparison_operators": true, "tests/test_window_evaluation.py::TestWindowEvaluation::test_window_boundary_conditions": true, "tests/test_window_evaluation.py::TestWindowEvaluation::test_multiple_points_in_window": true, "tests/test_window_evaluation.py::TestWindowEvaluation::test_window_overlap_handling": true, "tests/test_timestamp_tracking.py::TestTimestampTracking::test_last_processed_logic": true, "tests/test_timestamp_tracking.py::TestTimestampTracking::test_timestamp_race_conditions": true, "tests/test_timestamp_tracking.py::TestTimestampTracking::test_timestamp_update_verification": true, "tests/test_timestamp_tracking.py::TestTimestampTracking::test_timestamp_order_validation": true, "tests/test_late_data_handling.py::TestLateDataHandling::test_out_of_order_data_processing": true, "tests/test_late_data_handling.py::TestLateDataHandling::test_late_data_filtering": true, "tests/test_late_data_handling.py::TestLateDataHandling::test_duplicate_notification_prevention": true, "tests/test_late_data_handling.py::TestLateDataHandling::test_full_window_evaluation": true, "tests/test_windowed_evaluation.py::TestWindowedEvaluation::test_multiple_points_in_window_all_processed": true, "tests/test_windowed_evaluation.py::TestWindowedEvaluation::test_window_boundary_conditions": true, "tests/test_windowed_evaluation.py::TestWindowedEvaluation::test_window_overlap_handling": true, "tests/test_windowed_evaluation.py::TestWindowedEvaluation::test_empty_window_handling": true, "tests/test_windowed_evaluation.py::TestWindowedEvaluation::test_large_window_performance": true}