import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta, timezone

# Import the function to test
from app.db.db_service import insert_state_execution

@pytest.fixture
def mock_db(monkeypatch):
    # Patch the database session and models
    mock_session = MagicMock()
    monkeypatch.setattr('app.db.db_service.Session', lambda: mock_session)
    monkeypatch.setattr('app.db.db_service.database.get_db', lambda: iter([mock_session]))
    return mock_session

@pytest.fixture
def mock_alert():
    alert = MagicMock()
    alert.aggregate_enum.value = 'max'
    alert.aggregate_period_enum.value = '1h'
    alert.aggregate_period_enum.label = '1h'
    alert.customer_id = 1
    alert.measurement_id = 100
    return alert

@pytest.fixture
def mock_event():
    event = MagicMock()
    event.timestamp = datetime(2025, 6, 26, 12, 0, 0)
    return event

@patch('app.db.db_service.requests.get')
def test_insert_state_execution_exceeded(mock_requests_get, mock_db, mock_alert, mock_event, monkeypatch):
    # Setup mocks
    mock_db.query().filter().order_by().first.return_value = mock_event
    mock_db.query().filter().first.return_value = mock_alert
    mock_requests_get.return_value.status_code = 200
    mock_requests_get.return_value.json.return_value = [
        {'ts,val': [[1, 10.0], [2, 20.0], [3, 30.0]]}
    ]
    # Should not raise
    insert_state_execution(alert_id=1, end_time=1750948800000)
    assert mock_db.add.called
    assert mock_db.commit.called

@patch('app.db.db_service.requests.get')
def test_insert_state_execution_dead(mock_requests_get, mock_db, mock_alert, mock_event, monkeypatch):
    mock_db.query().filter().order_by().first.return_value = mock_event
    mock_db.query().filter().first.return_value = mock_alert
    mock_requests_get.return_value.status_code = 200
    mock_requests_get.return_value.json.return_value = [
        {'ts,val': [[1, 5.0], [2, 15.0]]}
    ]
    insert_state_execution(alert_id=2, end_time=1750948800000)
    assert mock_db.add.called
    assert mock_db.commit.called

@patch('app.db.db_service.requests.get')
def test_insert_state_execution_dead_uses_event_timestamp(mock_requests_get, mock_db, mock_alert, mock_event, monkeypatch):
    """Ensure DEAD excursion uses event timestamp as end_time, not meas_timestamp."""
    mock_db.query().filter().order_by().first.return_value = mock_event
    mock_db.query().filter().first.return_value = mock_alert
    mock_requests_get.return_value.status_code = 200
    mock_requests_get.return_value.json.return_value = [
        {'ts,val': [[1, 5.0], [2, 15.0]]}
    ]
    # The event timestamp in mock_event is used as end_time
    end_time = int(mock_event.timestamp.timestamp() * 1000)
    insert_state_execution(alert_id=2, end_time=end_time)
    assert mock_db.add.called
    assert mock_db.commit.called

@patch('app.db.db_service.requests.get')
def test_insert_state_execution_stale(mock_requests_get, mock_db, mock_alert, mock_event, monkeypatch):
    mock_db.query().filter().order_by().first.return_value = mock_event
    mock_db.query().filter().first.return_value = mock_alert
    mock_requests_get.return_value.status_code = 200
    mock_requests_get.return_value.json.return_value = [
        {'ts,val': [[1, 1.0], [2, 2.0]]}
    ]
    insert_state_execution(alert_id=3, end_time=1750948800000)
    assert mock_db.add.called
    assert mock_db.commit.called

@patch('app.db.db_service.requests.get')
def test_insert_state_execution_no_event(mock_requests_get, mock_db, mock_alert, monkeypatch):
    mock_db.query().filter().order_by().first.return_value = None
    result = insert_state_execution(alert_id=4, end_time=1750948800000)
    assert result is None

@patch('app.db.db_service.requests.get')
def test_insert_state_execution_no_alert(mock_requests_get, mock_db, mock_event, monkeypatch):
    mock_db.query().filter().order_by().first.return_value = mock_event
    mock_db.query().filter().first.return_value = None
    result = insert_state_execution(alert_id=5, end_time=1750948800000)
    assert result is None

@patch('app.db.db_service.requests.get')
def test_insert_state_execution_no_data(mock_requests_get, mock_db, mock_alert, mock_event, monkeypatch):
    mock_db.query().filter().order_by().first.return_value = mock_event
    mock_db.query().filter().first.return_value = mock_alert
    mock_requests_get.return_value.status_code = 200
    mock_requests_get.return_value.json.return_value = [
        {'ts,val': []}
    ]
    insert_state_execution(alert_id=6, end_time=1750948800000)
    assert mock_db.add.called
    assert mock_db.commit.called
