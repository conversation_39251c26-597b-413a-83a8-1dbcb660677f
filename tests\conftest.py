# conftest.py
"""
Global pytest fixtures for mocking Redis and DB connections to avoid real connections during test collection and execution.
"""
import sys
import pytest
from unittest.mock import MagicMock, patch

# Patch redis and redis.exceptions globally before any app import
sys.modules['redis'] = MagicMock()
sys.modules['redis.exceptions'] = MagicMock()
sys.modules['redis.sentinel'] = MagicMock()
sys.modules['redis.asyncio'] = MagicMock()
sys.modules['redis.asyncio.sentinel'] = MagicMock()
sys.modules['redis.client'] = MagicMock()

# Optionally, patch get_redis_client and any DB connection utility
@pytest.fixture(autouse=True, scope='session')
def patch_redis_and_db():
    with patch('app.redis.get_redis_client', return_value=MagicMock()):
        # Add more patches here if you have other global DB connections
        yield
