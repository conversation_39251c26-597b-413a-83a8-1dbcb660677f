# BromptonPythonUtilities

## Git Submodule Documentation

### What are Git Submodules?
A Git submodule allows you to keep a Git repository as a subdirectory of another Git repository. This enables you to clone another repository into your project and keep your commits separate.

### Adding this Repository as a Submodule
To add this repository as a submodule to your project:
```bash
git submodule add https://github.com/your-username/BromptonPythonUtilities.git
```

### Cloning a Repository with Submodules
When cloning a repository that contains submodules:
```bash
# Clone the main repository and submodules in one command
git clone --recursive https://github.com/your-username/your-repo.git

# Or, if already cloned, initialize and update submodules
git submodule init
git submodule update
```

### Updating Submodules
To update a submodule to its latest changes:
```bash
# Navigate into the submodule directory
cd BromptonPythonUtilities

# Fetch and merge changes
git fetch
git merge origin/main

# Return to parent project
cd ..

# Commit the updated submodule reference
git add BromptonPythonUtilities
git commit -m "Update BromptonPythonUtilities submodule"
```

### Making Changes to Submodules
When making changes to a submodule:
```bash
# Navigate to submodule directory
cd BromptonPythonUtilities

# Create a branch for your changes
git checkout -b feature-branch

# Make your changes, commit them
git add .
git commit -m "Your commit message"

# Push changes to the submodule repository
git push origin feature-branch

# Return to parent project and update the submodule reference
cd ..
git add BromptonPythonUtilities
git commit -m "Update BromptonPythonUtilities submodule"
```

### Best Practices
1. Always make changes to submodules in their own branch
2. Push submodule changes before pushing the parent repository
3. Communicate submodule updates to other team members
4. Use `git status` and `git diff` to track submodule changes
5. Regularly update submodules to stay in sync with upstream changes

### Common Submodule Commands
```bash
# Check submodule status
git submodule status

# Update all submodules
git submodule update --remote

# Remove a submodule
git submodule deinit BromptonPythonUtilities
git rm BromptonPythonUtilities
git commit -m "Remove BromptonPythonUtilities submodule"

# Synchronize submodules' remote info
git submodule sync
```

## Redis/TSDB Connection Utility

This utility provides a flexible Redis client that supports both standalone and sentinel configurations, with both synchronous and asynchronous modes.

### Usage

```python
from Redis_Connection_Utility.tsdb_connection import get_redis_client

# For synchronous client
redis_client = get_redis_client()

# For async client (with async/await)
redis_client = await get_redis_client()
```

### Environment Variables Configuration

#### Standalone Mode
Required variables for basic Redis connection:
```
redis_host=localhost      # Redis host (defaults to 'localhost')
redis_port=6379          # Redis port
redis_password=yourpass  # Authentication password
redis_db=0              # Redis database number (defaults to 0)
async_mode=false        # Set to 'true' for async client (optional)
```

#### Sentinel Mode
Required variables for Redis Sentinel configuration:
```
sentinel=true                    # Enable sentinel mode
redis_sentinel_host_0=host1      # First sentinel host
redis_sentinel_host_1=host2      # Second sentinel host (optional)
redis_sentinel_host_2=host3      # Third sentinel host (optional)
redis_port=26379                 # Port for sentinel hosts
redis_password=yourpass          # Authentication password
redis_sentinel_master=mymaster   # Name of the sentinel master
redis_db=0                       # Redis database number (defaults to 0)
async_mode=false                 # Set to 'true' for async client (optional)
```

### Features
- Supports both standalone and sentinel Redis configurations
- Provides both synchronous and asynchronous client options
- Automatic health checking with PING
- Connection pooling for better performance
- Configurable socket timeout and max connections
- Environment-based configuration using dotenv

### Example Usage

1. Create a `.env` file in your project root:
```
# For standalone mode
redis_host=localhost
redis_port=6379
redis_password=yourpassword
redis_db=0

# OR for sentinel mode
sentinel=true
redis_sentinel_host_0=sentinel1.example.com
redis_sentinel_host_1=sentinel2.example.com
redis_port=26379
redis_password=yourpassword
redis_sentinel_master=mymaster
redis_db=0
```

2. Use in your code:
```python
# Synchronous usage
from Redis_Connection_Utility.tsdb_connection import get_redis_client

redis = get_redis_client()
redis.set('key', 'value')
value = redis.get('key')

# Asynchronous usage
async def main():
    redis = await get_redis_client()
    await redis.set('key', 'value')
    value = await redis.get('key')
```
