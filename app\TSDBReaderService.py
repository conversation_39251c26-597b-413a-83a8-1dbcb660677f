import datetime
import ssl
from typing import Dict, Tuple, List, Any
import logging
import aiohttp

from .config import config
from .enums import Aggregate, AggregatePeriod, BucketSize_Dict, MINSPAN
from app.prometheus_metrices import TASK_EXECUTION_TIME

from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
logger= setup_logging()
# Initialize logger
# logger = logging.getLogger(__name__)

TS_API_HOST = config.get('api', 'api_host')
CSRF_TOKEN = config.get('api', 'csrf_token')

COOKIE = config.get('api', 'cookie')

ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE


async def read_data(customer: int,
                    measurements: Dict[Tuple[str, int], List[Tuple[str, int]]],
                    aggregate: Aggregate = Aggregate.TWA,
                    period: AggregatePeriod = AggregatePeriod._15M,
                    timeout: int = 30
                    ) -> Dict[str, Any]:
    """
    Reads data from the TSDB (Time Series Database) for the given measurement ID.
    Returns the data as a dictionary.
    """
    task_name = "read_data"
    # Use UTC for consistent timestamp handling
    start_time = datetime.datetime.utcnow()
    try:
        # Use UTC timestamp for consistent time handling
        end = round(datetime.datetime.utcnow().timestamp() * 1000)
        start = end - MINSPAN

        meas_ids = ",".join(
            [str(meas_id[1]) for asset, meas_list in measurements.items() for meas_id in meas_list])

        if aggregate == Aggregate.NONE:
            url = f"{TS_API_HOST}/timeseries/history/{customer}?meas_id={meas_ids}&start={start}&end={end}&asset_tz=false"
        else:
            start = max(start, end - BucketSize_Dict[period.name].value)
            url = f"{TS_API_HOST}/timeseries/agg/{customer}?meas_id={meas_ids}&start={start}&end={end}&asset_tz=false"

        headers = {
            "BE-CSRFToken": CSRF_TOKEN,
            "Connection": "keep-alive"
        }
        cookies = COOKIE

        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            async with session.get(url, headers=headers, cookies=cookies, ssl=ssl_context) as response:
                if response.status == 200:
                    data = await response.json()
                    meas_id_to_data = {}
                    for meas_obj in data:
                        if 'ts,val' in meas_obj:
                            meas_id_to_data[meas_obj['tag']] = {
                                "status": "success",
                                "timestamp": meas_obj['ts,val'][-1][0],
                                "value": meas_obj['ts,val'][-1][1]
                            }
                        elif 'error' in meas_obj:
                            meas_id_to_data[meas_obj['tag']] = {
                                "status": "error",
                            }
                            # raise Exception(f"Error in data for meas_id {meas_id}: {meas_obj['error']}")

                    return meas_id_to_data
                else:
                    raise Exception(f"Failed to fetch data: {await response.text()}")
    except Exception as e:
        logger.error(f"Error fetching data: {e}")
        raise
    finally:
        # Use UTC for consistent timestamp handling
        end_time = datetime.datetime.utcnow()
        elapsed_time = (end_time - start_time).total_seconds()
        TASK_EXECUTION_TIME.labels(task_name=task_name).observe(elapsed_time)

async def read_data_new(customer: int,
                    measurements: List[int],
                    aggregate: Aggregate = Aggregate.TWA,
                    period: AggregatePeriod = AggregatePeriod._15M,
                    timeout: int = 30
                    ) -> Dict[str, Any]:
    """
    Reads data from the TSDB (Time Series Database) for the given measurement ID.
    Returns the data as a dictionary with all values in the window.
    """
    task_name = "read_data_new"
    start_time = datetime.datetime.utcnow()
    logger.info(f"Starting task {task_name} for customer {customer} with timeout {timeout}s")
    try:
        end = round(datetime.datetime.utcnow().timestamp() * 1000)
        start = end - MINSPAN

        meas_ids = ",".join([str(meas_id) for meas_id in measurements])

        if aggregate == Aggregate.NONE:
            url = f"{TS_API_HOST}/timeseries/history/{customer}?meas_id={meas_ids}&start={start}&end={end}&asset_tz=false"
        else:
            try:
                bucket_size = BucketSize_Dict.get(period.name)
                if bucket_size and bucket_size.value:
                    logger.info(f"Period: {period}, Bucket size: {bucket_size.value}ms")
                    start = max(start, end - bucket_size.value)
                    url = f"{TS_API_HOST}/timeseries/agg/{customer}?meas_id={meas_ids}&start={start}&end={end}&asset_tz=false&agg={aggregate.value}&agg_period={period.value}"
                else:
                    logger.info(f"Using default query for period: {period}")
                    url = f"{TS_API_HOST}/timeseries/history/{customer}?meas_id={meas_ids}&start={start}&end={end}&asset_tz=false"
            except Exception as e:
                logger.warning(f"Error handling period {period}: {e}. Using default query.")
                url = f"{TS_API_HOST}/timeseries/history/{customer}?meas_id={meas_ids}&start={start}&end={end}&asset_tz=false"
        logger.info(f"URL: {url}")  

        headers = {
            "Be-Csrftoken": CSRF_TOKEN,
            "Cookie": COOKIE,
            "Connection": "keep-alive"
        }

        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            logger.info(f"Sending GET request to {url}")
            async with session.get(url, headers=headers, ssl=ssl_context) as response:
                logger.info(f"Received response with status {response.status}")
                if response.status == 200:
                    data = await response.json()
                    logger.debug(f"Response data: {data}")
                    meas_id_to_data = {}
                    for meas_obj in data:
                        if 'ts,val' in meas_obj:
                            ts_vals = meas_obj['ts,val']
                            if len(ts_vals) == 0:
                                meas_id_to_data[meas_obj['tag']] = {
                                    "status": "nodata",
                                    "values": []
                                }
                            else:
                                values = [
                                    {"timestamp": ts, "value": val}
                                    for ts, val in ts_vals
                                ]
                                meas_id_to_data[meas_obj['tag']] = {
                                    "status": "success",
                                    "values": values
                                }
                        elif 'error' in meas_obj:
                            meas_id_to_data[meas_obj['tag']] = {
                                "status": "error",
                                "values": []
                            }
                            logger.warning(f"Error in data for meas_id {meas_obj['tag']}: {meas_obj['error']}")
                    logger.info(f"Successfully processed data for {len(meas_id_to_data)} measurements")
                    return meas_id_to_data
                else:
                    error_message = await response.text()
                    logger.error(f"Request failed with status {response.status}: {error_message}")
                    raise Exception(f"Failed to fetch data: {error_message}")
    except Exception as e:
        logger.exception(f"Error during task {task_name}: {e}")
        raise
    finally:
        end_time = datetime.datetime.utcnow()
        elapsed_time = (end_time - start_time).total_seconds()
        logger.info(f"Task {task_name} completed in {elapsed_time:.2f} seconds")
        TASK_EXECUTION_TIME.labels(task_name=task_name).observe(elapsed_time)