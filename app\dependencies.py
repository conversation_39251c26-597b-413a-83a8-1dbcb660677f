#The goal of this file is to check whether the reques tis authorized or not [ verification of the proteced route]
from fastapi import Request
import jwt as jwt_token_
from base64 import b64encode
from hashlib import sha256
from os import getenv
from datetime import datetime
from typing import Callable, List
from functools import wraps
from urllib.parse import unquote
from prometheus_client import Counter,Histogram
from app.config import jwt_secret, jwt_algorithm

class UnicornException(Exception):
    def __init__(self, message: str, status_code: int):
        self.message = message
        self.status_code = status_code
        
def decode_jwt(token: str) -> dict:
    try:
        res = jwt_token_.decode(token, jwt_secret, algorithms=[jwt_algorithm])
        return res
    except Exception as e:
        raise UnicornException(
            {
                "statusCode": 401,
                "createdBy": "JWTAuth",
                "message": "JWT decode failed",
                "details": str(e),
                "timestamp": str(datetime.now())
            }, 
            status_code=401
        )

def compare_hash(access_token: str, csrf_token: str) -> bool:
    if b64encode(sha256(access_token.encode('utf-8')).digest()).decode('utf-8') == csrf_token:
        return True
    raise UnicornException(
        {
            "statusCode": 401,
            "createdBy": "JWTAuth",
            "message": "Failed CSRF validation",
            "details": "Incorrect CSRF token",
            "timestamp": str(datetime.now())
        }, 
        status_code=401
    )

        
class JWTAuth():
    def __call__(self, request: Request):
        access_token = request.cookies.get('BE-AccessToken')
        if not access_token:
            raise missing_token_exception("BE-AccessToken")

        csrf_token = request.headers.get('be-csrftoken')
        if csrf_token:
            return self.verify_tokens(csrf_token=csrf_token, jwt_token=access_token)
        
        raise missing_token_exception("BE-CSRFToken", in_header=True)
        
    def verify_tokens(self, csrf_token: str, jwt_token: str) -> bool:
        return decode_jwt(jwt_token) and compare_hash(jwt_token, csrf_token)

class JWTAuthForRealtime():
    def __call__(self, request: Request):
        access_token = request.cookies.get('BE-AccessToken')
        if not access_token:
            raise missing_token_exception("BE-AccessToken")

        csrf_token = request.cookies.get('BE-CSRFToken')
        if csrf_token:
            return self.verify_tokens(csrf_token=unquote(csrf_token), jwt_token=access_token)
        raise missing_token_exception("BE-CSRFToken", in_cookie=True)
        
    def verify_tokens(self, csrf_token: str, jwt_token: str) -> bool:
        return decode_jwt(jwt_token) and compare_hash(jwt_token, csrf_token)

role_hierarchy = {
    "USER": ["USER", "POWER_USER", "ADMIN"],
    "POWER_USER": ["POWER_USER", "ADMIN"],
    "ADMIN": ["ADMIN"]
}
def hasCustomerScope(func:Callable)-> Callable:  
    @wraps(func)
    async def wrapper(*args, **kwargs):
        request: Request = kwargs.get('request')
        access_token = request.cookies.get("BE-AccessToken")
        if not access_token:
            raise missing_token_exception("BE-AccessToken")
        user_data = decode_jwt(access_token)
        customer_id = kwargs['customer_id']
        # Check if the customer_id exists in any role
        if not any(customer_id in role_ids for role_ids in user_data['roles'].values()):
            raise unauthorized_access_exception()
        return await func(*args, **kwargs)
    return wrapper

def hasCustomerScopeWithRole(roles: List[str] = ["USER"] )-> Callable: 
    def decorator(func: Callable)-> Callable:  
        @wraps(func)
        async def wrapper(*args, **kwargs):
            request: Request = kwargs.get('request')
            access_token = request.cookies.get("BE-AccessToken")
            if not access_token:
                raise missing_token_exception("BE-AccessToken")
            user_data = decode_jwt(access_token)
            customer_id = kwargs['customer_id']
            # Extract user roles once
            user_roles = user_data.get('roles', {})
            # Check if user has the required role
            for role in roles:
                if any(customer_id in user_roles.get(req_role, []) for req_role in role_hierarchy.get(role, [])):
                    return await func(*args, **kwargs)
            raise unauthorized_access_exception()
        return wrapper
    return decorator

def missing_token_exception(token_name: str, in_cookie: bool = False, in_header: bool = False):
    location = "cookies" if in_cookie else "headers" if in_header else "request"
    return UnicornException(
        {
            "statusCode": 401,
            "createdBy": "JWTAuth",
            "message": f"Missing {token_name} in {location}",
            "timestamp": str(datetime.now())
        },
        status_code=401
    )

def unauthorized_access_exception():
    return UnicornException(
        {
            "statusCode": 403,
            "createdBy": "JWTAuth",
            "message": "Unauthorized access",
            "timestamp": str(datetime.now())
        },
        status_code=403
    )