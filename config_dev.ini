[celery]
redis_password=test@123
redis_sentinel_master=mymaster
broker_host=redis-node-0.redis-headless.redis.svc.cluster.local
broker_port=6379
broker_db=0
backend_host=redis-node-0.redis-headless.redis.svc.cluster.local
backend_port=6379
backend_db=0
retry_enabled = False
retry_max_retries = 5
retry_countdown = 60


[api]
api_host = http://timeseries-api-service.application.svc.cluster.local/api/v1_0
csrf_token = Lm6Kbx6xXhlnraTe0LpAgxjg0VWp1Od9SGIsJmPZZXc=
cookie = BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.roR6lVxwnMowbW_V4wwben-PD90HhjfIdV6-aB3cbZo; BE-CSRFToken=Lm6Kbx6xXhlnraTe0LpAgxjg0VWp1Od9SGIsJmPZZXc=


[database]
url = postgresql://postgres:Br0mpt0n!<EMAIL>/dataloggertest
db_name=dataloggertest
db_host=dataloggertest.clclbj3j3ehf.us-east-1.rds.amazonaws.com
db_port=5432
db_user=postgres
db_password=Br0mpt0n!0T
db_ssl=true

[jwt]
jwt_secret = this is a very secret secret
jwt_algorithm = HS256

[kafka]
broker=strimzi-cluster-kafka-brokers.kafka-strimzi.svc.cluster.local:9092
alert_topic=be-alert-dev-new

[rabbitmq]
host=bromptonenergy.io
port=8884
username=mosquitto
password=Br0mpt0n!0T
exchange=notifications_topic_exchange
routing_key=alert.exceeded


; [celery]
; redis_password=test@123
; redis_sentinel_master=mymaster
; broker_host=redis.redis.svc.cluster.local
; broker_port=26379
; broker_db=0
; backend_host=redis.redis.svc.cluster.local
; backend_port=26379
; backend_db=0
; retry_enabled = False
; retry_max_retries = 5
; retry_countdown = 60

; [api]
; api_host = http://timeseries-api-service.application.svc.cluster.local/api/v1_0
; csrf_token = Lm6Kbx6xXhlnraTe0LpAgxjg0VWp1Od9SGIsJmPZZXc=
; cookie = BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.roR6lVxwnMowbW_V4wwben-PD90HhjfIdV6-aB3cbZo; BE-CSRFToken=Lm6Kbx6xXhlnraTe0LpAgxjg0VWp1Od9SGIsJmPZZXc=


; [mqtt]
; alert_topic = be-alert-dev-new
; broker = 127.0.0.1
; port = 1883
; keepalive = 60
; username = user
; password = user
; qos = 2

; [database]
; url = postgresql://postgres:Br0mpt0n!<EMAIL>/dataloggertest
; db_name=dataloggertest
; db_host=dataloggertest.clclbj3j3ehf.us-east-1.rds.amazonaws.com
; db_port=5432
; db_user=postgres
; db_password=Br0mpt0n!0T
; db_ssl=true

; [jwt]
; jwt_secret = this is a very secret secret
; jwt_algorithm = HS256

; [kafka]
; broker=strimzi-cluster-kafka-brokers.kafka-strimzi.svc.cluster.local:9092
; alert_topic=be-alert-dev-new