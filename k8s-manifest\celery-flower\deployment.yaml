apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-flower
  namespace: application
  labels:
    app: celery-flower
spec:
  replicas: 1
  selector:
    matchLabels:
      app: celery-flower
  template:
    metadata:
      labels:
        app: celery-flower
    spec:
      containers:
      - name: celery-flower
        image: mher/flower:latest
        args:
          - "--port=5555" # Flower will run on port 5555
        env:
        - name: CELERY_BROKER_URL
          value: redis://:test@<EMAIL>:6379/0 # Replace with your Redis broker URL
        resources:
          requests:
            memory: "128Mi"
            cpu: "250m"
          limits:
            memory: "256Mi"
            cpu: "500m"
