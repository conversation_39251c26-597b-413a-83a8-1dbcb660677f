# Alert Services Documentation

## Overview

The Alert Service is a comprehensive monitoring system that evaluates measurement data against configurable thresholds and triggers notifications when conditions are met. The system supports four types of alerts: NOMINAL, DEAD, STALE, and ANOMALY (not yet implemented).

## Architecture

### Core Components

```
Alert Service Architecture
├── tasks.py                    # Main alert processing orchestrator
├── Alert Checker Services      # Individual alert type processors
│   ├── LimitCheckerService.py  # NOMINAL alerts (threshold-based)
│   ├── DeadCheckerService.py   # DEAD alerts (data availability)
│   └── StaleCheckerService.py  # STALE alerts (data freshness)
├── Database Services           # Data persistence layer
│   └── db_service.py          # Event and excursion management
└── Supporting Services         # External integrations
    ├── TSDBReaderService.py   # Time series data retrieval
    └── rabbitmq_service.py    # Notification delivery
```

## Alert Types

### 1. NOMINAL Alerts 

**Purpose**: Monitor measurement values against configurable thresholds.

**Triggers**: When measurement values cross defined limits using comparison operations.

**States**:

- `NORMAL`: Value within acceptable range
- `EXCEEDED`: Value has crossed the threshold

**Configuration**:

- `threshold_value`: The limit to compare against
- `comparison_operation`: GT, GE, LT, LE, EQ
- `deadband`: Hysteresis value to prevent oscillation

**Example Use Cases**:

- Temperature monitoring (> 80°C triggers alert)
- Pressure monitoring (< 10 PSI triggers alert)
- Flow rate monitoring (= 0 triggers alert)

### 2. DEAD Alerts

**Purpose**: Detect when measurement data is missing or too old.

**Triggers**: When no data is received within a specified time window.

**States**:

- `NORMAL`: Data is current and available
- `DEAD`: No data or data is too old

**Configuration**:

- `threshold_value`: Maximum age in minutes before considering data dead
- No comparison operation needed

**Example Use Cases**:

- Sensor connectivity monitoring
- Data logger health checks
- Communication link monitoring

### 3. STALE Alerts

**Purpose**: Detect when measurement values haven't changed for an extended period.

**Triggers**: When the same value persists longer than expected.

**States**:

- `NORMAL`: Value is changing appropriately
- `STALE`: Value hasn't changed within time threshold

**Configuration**:

- `threshold_value`: Maximum time in minutes for unchanged values
- `stale_band`: Optional tolerance band for "unchanged" detection
- `comparison_operation`: GT or GE for time threshold

**Example Use Cases**:

- Stuck sensor detection
- Process monitoring (values should vary)
- Equipment malfunction detection

### 4. ANOMALY Alerts 

**Status**: Not yet implemented

**Purpose**: Detect unusual patterns or outliers in measurement data.

**Planned Features**:

- Statistical anomaly detection
- Machine learning-based pattern recognition
- Configurable sensitivity levels

## Service Details

### LimitCheckerService.py

**Responsibility**: NOMINAL alert processing

**Key Functions**:

- `check_limit()`: Core threshold comparison logic
- Deadband handling for hysteresis
- Support for all comparison operations

**Input Validation**:

- Validates numeric and boolean inputs
- Handles null value scenarios
- Type checking for parameters

### DeadCheckerService.py

**Responsibility**: DEAD alert processing

**Key Functions**:

- `check_dead_measurement()`: Determines if data is too old or missing
- Timestamp comparison in milliseconds
- Last known value preservation

**Logic**:

```python
if last_seen is None or (now - last_seen) > threshold:
    state = DEAD
else:
    state = NORMAL
```

### StaleCheckerService.py

**Responsibility**: STALE alert processing

**Key Functions**:

- `check_stale_measurement()`: Detects unchanged values
- Redis integration for value tracking
- Optional stale band support

**Redis Storage**:

- `measurement:{id}:last_value`: Last recorded value
- `measurement:{id}:last_changed`: When value last changed
- `measurement:{id}:initial_value`: Reference value for band comparison

**Logic**:

```python
if value_unchanged_time > threshold:
    state = STALE
else:
    state = NORMAL
```

## Data Flow

### Alert Processing Workflow

```mermaid
graph TD
    A[Measurement Data] --> B[tasks.py]
    B --> C{Alert Type?}
  
    C -->|NOMINAL| D[LimitCheckerService]
    C -->|DEAD| E[DeadCheckerService]
    C -->|STALE| F[StaleCheckerService]
    C -->|ANOMALY| G[Skip - Not Implemented]
  
    D --> H[State Evaluation]
    E --> H
    F --> H
  
    H --> I{State Changed?}
    I -->|Yes| J[Create Event]
    I -->|No| K[Continue Processing]
  
    J --> L[Update Alert State]
    L --> M{Returning to NORMAL?}
    M -->|Yes| N[Create Excursion]
    M -->|No| O[Send Notification]
    N --> O
    O --> P[End]
    K --> P
```

### Event and Excursion Management

**Events**: Created on every state change

- Timestamp of the change
- Input value at time of change
- New state information
- Alert configuration details

**Excursions**: Created when returning to NORMAL state

- Represents a complete alert cycle (NORMAL → ALERT → NORMAL)
- Contains aggregated data (min, max, avg) for the excursion period
- Used for historical analysis and reporting

## Configuration

### Alert Configuration Parameters

| Parameter                | Type   | Description                       | Used By   |
| ------------------------ | ------ | --------------------------------- | --------- |
| `threshold_value`      | float  | Limit value or time threshold     | All types |
| `comparison_operation` | enum   | GT, GE, LT, LE, EQ                | NOMINAL   |
| `deadband`             | float  | Hysteresis value                  | NOMINAL   |
| `stale_band`           | float  | Tolerance for unchanged detection | STALE     |
| `aggregate`            | string | Data aggregation type             | All types |
| `period`               | string | Time period for aggregation       | All types |

### Database Schema

**alerts table**: Alert configuration
**events table**: State change records
**excursions table**: Complete alert cycles
**measurements table**: Source data definitions

## Error Handling

### Robust Error Management

**Input Validation**:

- Type checking for all parameters
- Null value handling
- Range validation for thresholds

**Database Errors**:

- Transaction rollback on failures
- Graceful degradation for connection issues
- Comprehensive error logging

**External Service Failures**:

- TSDB connection timeouts
- Redis unavailability fallbacks
- RabbitMQ notification failures

## Performance Characteristics

### Benchmarks

Based on performance testing:

- **NOMINAL Alerts**: >200 alerts/second
- **DEAD Alerts**: >100 alerts/second
- **STALE Alerts**: >50 alerts/second (Redis overhead)
- **Mixed Processing**: >100 alerts/second

### Optimization Features

- Efficient timestamp handling (millisecond precision)
- Minimal memory footprint
- Redis connection pooling
- Batch processing capabilities

## Monitoring and Observability

### Logging Standards

All services use BromptonPythonUtilities logging:

```python
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
logger = setup_logging()
```

**Log Levels**:

- `INFO`: Normal operations, state changes
- `WARNING`: Missing data, fallback scenarios
- `ERROR`: Processing failures, invalid configurations
- `DEBUG`: Detailed processing information

### Key Log Messages

```
[EVENT-INSERT] type=NOMINAL event_id=123 alert_id=456 state=EXCEEDED value=15.5 timestamp=1234567890
[EXCURSION-INSERT] alert_id=456 end_time=1234567890
[NOTIFICATION-SENT] type=NOMINAL event_id=123 alert_id=456 state=EXCEEDED timestamp=1234567890
```

## Testing

### Comprehensive Test Suite

**137 total tests** covering:

- **Unit Tests**: Individual service logic
- **Integration Tests**: End-to-end workflows
- **Performance Tests**: Scalability validation
- **Edge Case Tests**: Boundary conditions

**Test Categories**:

- Alert type-specific tests (NOMINAL, DEAD, STALE)
- Database integration tests
- RabbitMQ notification tests
- TSDB reader service tests
- Enum and configuration tests

### Running Tests

```bash
# Run all tests
pytest tests/ -v

# Run specific alert type tests
pytest tests/test_nominal_alert.py -v
pytest tests/test_dead_alert.py -v
pytest tests/test_stale_alert.py -v

# Run performance tests
pytest tests/test_alert_performance.py -v

# Run with coverage
pytest tests/ --cov=app --cov-report=html
```

## Recent Improvements

### Bug Fixes Implemented

1. **Timestamp Consistency**: Standardized timestamp handling across all alert types
2. **Input Value Handling**: Fixed DEAD alert value preservation
3. **STALE Alert Logic**: Corrected value change detection
4. **Logging Standardization**: Maintained original logging formats
5. **STALE Event Timestamp Fix**: Fixed STALE alerts to use detection timestamp instead of measurement timestamp for event records

### Code Quality Enhancements

1. **Helper Functions**: Added private helper functions in tasks.py
2. **Documentation**: Comprehensive docstrings added
3. **Type Safety**: Enhanced type hints throughout
4. **Error Handling**: Improved exception management

## Future Enhancements

### Planned Features

1. **ANOMALY Alert Implementation**: Statistical anomaly detection
2. **Advanced Stale Detection**: Pattern-based stale detection
3. **Performance Optimization**: Further scalability improvements
4. **Enhanced Monitoring**: Additional observability features

### Scalability Roadmap

1. **Horizontal Scaling**: Multi-instance processing
2. **Caching Optimization**: Enhanced Redis utilization
3. **Batch Processing**: Improved throughput for high-volume scenarios
4. **Real-time Streaming**: Event-driven processing architecture
