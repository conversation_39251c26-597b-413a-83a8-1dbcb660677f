import pytest
import docker
import redis
import os
import pendulum
from testcontainers.core.container import DockerContainer
from testcontainers.core.waiting_utils import wait_for_logs
try:
    from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
except ImportError:
    from Logging_Utility.logging_config import setup_logging

try:
    from BromptonPythonUtilities.TSDB_Utility.tsdb_utils import (
    get_tsdb_data,store_in_tsdb, Aggregate, BucketSize
)
except ImportError:
    from TSDB_Utility.tsdb_utils import (
        get_tsdb_data, store_in_tsdb, Aggregate, BucketSize
    )
    
logger = setup_logging()

# Calculate start and end times for tests - last 30 days
now = pendulum.now()
TEST_END = int(now.timestamp() * 1000)  # Current time in milliseconds
TEST_START = int(now.subtract(days=10).timestamp() * 1000)  # 10 days ago in milliseconds

class TestTsdbIntegration:
    """Integration tests with real Redis instance using dump.rdb"""
    
    # Test configuration
    REDIS_PORT = 6379
    RETRY_COUNT = 30
    RETRY_DELAY = 2
    REDIS_IMAGE = "redis/redis-stack-server:7.4.0-v0"

    @pytest.fixture(scope="class")
    def setup_redis_container(self):
        """Create Redis container with dump.rdb"""
        container = None
        try:
            # Use dump.rdb from tests directory
            dump_path = os.path.abspath("TSDB_Utility/tests/data/dump.rdb")
            dump_dir = os.path.dirname(dump_path)
            logger.info(f"Using dump.rdb from: {dump_dir}")

            # Create container
            container = (
                DockerContainer(self.REDIS_IMAGE)
                .with_name('redis_test')
                .with_exposed_ports(self.REDIS_PORT)
                .with_volume_mapping(dump_dir, '/data')
                .with_command("redis-stack-server --dir /data --save '' --rdb-save-incremental-fsync yes")
            )
            
            container.start()

            logger.info("Waiting for container to be ready...")
            wait_for_logs(container, "Ready to accept connections")
            logger.info("Container is ready")
            
            yield container
            
        except Exception as e:
            logger.error(f"Container setup failed: {e}")
            if container:
                try:
                    logs = container.logs().decode('utf-8')
                    logger.error(f"Container logs at failure: {logs}")
                except Exception as log_e:
                    logger.error(f"Error reading container logs: {log_e}")
            raise
        finally:
            if container:
                try:
                    logger.info("Cleaning up container...")
                    docker_client = docker.from_env()
                    container_name = 'redis_test'
                    for c in docker_client.containers.list(all=True):
                        if c.name == container_name:
                            c.stop()
                            c.remove(force=True)
                except Exception as e:
                    logger.error(f"Error during container cleanup: {e}")

    @pytest.fixture
    def setup_redis_connection(self, setup_redis_container):
        """Create Redis connection"""
        mapped_port = setup_redis_container.get_exposed_port(self.REDIS_PORT)
        
        conn = redis.Redis(
            host='localhost',
            port=mapped_port,
            decode_responses=False,
            socket_timeout=30,
            socket_keepalive=True,
            retry_on_timeout=True,
            socket_connect_timeout=30
        )

        # Verify connection
        if not conn.ping():
            raise redis.ConnectionError("Could not ping Redis server")
            
        yield conn
        conn.close()

    def test_read_dump_data(self, setup_redis_connection):
        """Test reading data from restored dump.rdb"""
        logger.info("Starting test_read_dump_data...")
        
        # Test reading numeric data
        logger.info("Reading numeric data from key 10102...")
        meas_info = {10102: {"data_type": "REAL"}}
        numeric_data = get_tsdb_data(
            conn=setup_redis_connection,
            meas_info=meas_info,
            agg=Aggregate.AVG,
            agg_period=BucketSize._5M,
            start=TEST_START,
            end=TEST_END
        )
        logger.info(f"Received numeric data: {len(numeric_data)} records")
        assert len(numeric_data) > 0, "No numeric data found in dump.rdb"

    def test_write_and_read(self, setup_redis_connection):
        """Test writing new data and reading it back"""
        logger.info("Starting test_write_and_read...")
        
        # Write new data
        current_time = int(pendulum.now().timestamp() * 1000)
        logger.info(f"Creating test data at timestamp {current_time}")
        test_data = [
            ("10102", current_time, 123.45, "REAL"),
            ("10102", current_time + 1000, 67.89, "REAL")
        ]
        
        success, failures = store_in_tsdb(setup_redis_connection, tvs=test_data)
        logger.info("Write operation completed")
        assert success is True, f"Write failed: {failures}"
        
        # Read it back
        meas_info = {10102: {"data_type": "REAL"}}
        results = get_tsdb_data(
            conn=setup_redis_connection,
            meas_info=meas_info,
            # agg=Aggregate.AVG,
            # agg_period=BucketSize._1M,
            start=current_time - 1000,
            end=current_time + 2000
        )
        
        assert len(results) > 0
        assert 10102 in results, "Expected key 10102 not found in results"
        data_points = results[10102]
        logger.info(f"Received {len(data_points)} data points")
        values = [float(val.decode()) if isinstance(val, bytes) else val for ts, val in data_points]
        assert any(abs(v - 123.45) < 0.001 for v in values), "First value not found"
        assert any(abs(v - 67.89) < 0.001 for v in values), "Second value not found"
        logger.info("Data verification completed successfully")

    def test_write_and_read_recent_data(self, setup_redis_connection):
        """Test writing and reading data points in the last 30 minutes"""
        # Write test data
        base_time = int(pendulum.now().timestamp() * 1000)
        start_time = base_time - (30 * 60 * 1000)  # 30 minutes ago
        
        # Create exactly 30 test data points, one per minute
        test_data = []
        for i in range(30):
            timestamp = start_time + (i * 60 * 1000)  # One entry per minute
            value = 100 + i  # Different value for each minute
            test_data.append(("10102", timestamp, value, "REAL"))
            
        # Write data to Redis
        success, failures = store_in_tsdb(setup_redis_connection, test_data)
        assert success is True, f"Write failed: {failures}"
        assert len(failures) == 0, f"Some writes failed: {failures}"
        
        meas_info = {10102: {"data_type": "REAL"}}
        results = get_tsdb_data(
            conn=setup_redis_connection,
            meas_info=meas_info,
            start=start_time,
            end=start_time + (29 * 60 * 1000)  # End at last data point
        )
        
        # Verify results
        assert len(results) > 0, "No data returned"
        assert 10102 in results, "Expected key 10102 not found in results"
        
        data_points = results[10102]
        assert len(data_points) == 30, f"Expected 30 data points, got {len(data_points)}"
        
        # Create map of test data for comparison
        test_data_map = {timestamp: value for measurement_id, timestamp, value, data_type in test_data}
        
        # Verify each data point
        for ts, val in data_points:
            assert ts in test_data_map, f"Timestamp {ts} not found in original test data"
            expected_val = test_data_map[ts]
            actual_val = float(val.decode()) if isinstance(val, bytes) else val
            assert abs(actual_val - expected_val) < 0.001, f"Value mismatch at {ts}: expected {expected_val}, got {actual_val}"