import logging
import os
from typing import Union, Coroutine

from dotenv import load_dotenv
from redis import Redis as SyncRedis, <PERSON><PERSON><PERSON> as SyncPool
from redis.sentinel import Sentinel as SyncSentinel
from redis.asyncio import Redis as AsyncRedis
from redis.asyncio.sentinel import Sentinel as AsyncSentinel

try:
    from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
except ImportError:
    from Logging_Utility.logging_config import setup_logging

# Load environment variables
load_dotenv()

logger = setup_logging()

def _get_sentinel_hosts():
    """Get sentinel hosts from environment variables"""
    hosts = []
    i = 0
    while True:
        host = os.getenv(f'redis_sentinel_host_{i}')
        if not host:
            break
        port = int(os.getenv('redis_port', '26379'))
        hosts.append((host, port))
        i += 1
    return hosts

def get_redis_client() -> Union[SyncRedis, Coroutine]:
    """
    Create either a synchronous or async Redis client using configuration from environment variables.
    
    Required environment variables:
        For Sentinel mode (when sentinel=true):
            redis_sentinel_host_0: First sentinel host
            redis_sentinel_host_1: Second sentinel host (optional)
            redis_sentinel_host_2: Third sentinel host (optional)
            redis_port: Port for sentinel hosts
            redis_password: Authentication password
            redis_sentinel_master: Name of the sentinel master
            redis_db: Redis database number (defaults to 0)
            sentinel: Set to 'true' to enable sentinel mode
            async_mode: Set to 'true' for async client (optional, defaults to false)
        
        For Standalone mode (when sentinel=false or not set):
            redis_host: Redis host (defaults to 'localhost')
            redis_port: Redis port
            redis_password: Authentication password
            redis_db: Redis database number (defaults to 0)
            async_mode: Set to 'true' for async client (optional, defaults to false)
    """
    # Get configuration from environment
    password = os.getenv('redis_password')
    port = int(os.getenv('redis_port', '26379'))
    db = int(os.getenv('redis_db', '0'))
    use_sentinel = os.getenv('sentinel', '').lower() == 'true'
    async_mode = os.getenv('async_mode', '').lower() == 'true'
    socket_timeout = 10
    max_connections = 50

    # ─── Sentinel Mode ─────────────────────────────────────────────────────────
    if use_sentinel:
        sentinel_master = os.getenv('redis_sentinel_master')
        if not sentinel_master:
            raise ValueError("redis_sentinel_master must be provided when using Sentinel mode")

        endpoints = _get_sentinel_hosts()
        if not endpoints:
            raise ValueError("No sentinel hosts configured. At least redis_sentinel_host_0 must be set")

        if async_mode:
            sentinel = AsyncSentinel(
                sentinels=endpoints,
                socket_timeout=socket_timeout,
                password=password,
                sentinel_kwargs={'password': password},
            )
            client = sentinel.master_for(
                service_name=sentinel_master,
                db=db,
                socket_timeout=socket_timeout,
            )
            logger.info(f"Prepared async Redis via Sentinel using db {db}")
        else:
            sentinel = SyncSentinel(
                sentinels=endpoints,
                socket_timeout=socket_timeout,
                password=password,
                sentinel_kwargs={'password': password},
            )
            client = sentinel.master_for(
                service_name=sentinel_master,
                db=db,
                socket_timeout=socket_timeout,
            )
            logger.info(f"Prepared sync Redis via Sentinel using db {db}")

    # ─── Standalone Mode ───────────────────────────────────────────────────────
    else:
        host = os.getenv('redis_host', 'localhost')
        if async_mode:
            client = AsyncRedis(
                host=host,
                port=port,
                db=db,
                password=password,
                socket_timeout=socket_timeout,
                max_connections=max_connections,
                decode_responses=False,
            )
            logger.info(f"Prepared async standalone Redis at {host}:{port} using db {db}")
        else:
            pool = SyncPool(
                host=host,
                port=port,
                db=db,
                password=password,
                socket_timeout=socket_timeout,
                max_connections=max_connections,
            )
            client = SyncRedis(connection_pool=pool)
            logger.info(f"Prepared sync standalone Redis at {host}:{port} using db {db}")

    # ─── Health-check & Return ─────────────────────────────────────────────────
    if async_mode:
        async def _check_and_return():
            if not await client.ping():
                raise ConnectionError("Async Redis PING failed")
            return client
        return _check_and_return()

    if not client.ping():
        raise ConnectionError("Sync Redis PING failed")
    return client
