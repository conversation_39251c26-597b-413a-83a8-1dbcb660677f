from datetime import datetime, timezone, timedelta
from typing import Optional
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
from app.enums import LimitState, CompareOperation
from app.LimitCheckerService import LimitCheckResult
from app.redis import master

logger = setup_logging()

def get_last_value(measurement_id: int) -> Optional[float]:
    """Get last stored value from Redis."""
    key = f"measurement:{measurement_id}:last_value"
    value = master.get(key)
    return float(value) if value is not None else None

def store_value(measurement_id: int, value: float) -> None:
    """Store current value in Redis."""
    key = f"measurement:{measurement_id}:last_value"
    master.set(key, str(value), ex=86400)  # Expire after 24 hours

def get_last_changed_time(measurement_id: int) -> Optional[datetime]:
    """Get last changed timestamp from Redis."""
    key = f"measurement:{measurement_id}:last_changed"
    value = master.get(key)
    if value is not None:
        try:
            return datetime.fromtimestamp(float(value), tz=timezone.utc).replace(tzinfo=None)
        except (ValueError, TypeError):
            return None
    return None

def store_last_changed_time(measurement_id: int, timestamp: datetime) -> None:
    """Store last changed timestamp in Redis."""
    key = f"measurement:{measurement_id}:last_changed"
    master.set(key, str(timestamp.timestamp()), ex=86400)  # Expire after 24 hours

def store_initial_value(measurement_id: int, value: float) -> None:
    """Store initial value for stale band comparison."""
    key = f"measurement:{measurement_id}:initial_value"
    master.set(key, str(value), ex=86400)  # Expire after 24 hours

def get_initial_value(measurement_id: int) -> Optional[float]:
    """Get initial value for stale band comparison."""
    key = f"measurement:{measurement_id}:initial_value"
    value = master.get(key)
    return float(value) if value is not None else None


def check_stale_measurement(
    alert_id: int,
    measurement_id: int,
    now: datetime,
    last_changed: Optional[datetime],
    stale_duration_minutes: int,
    aggregate: str,
    period: str,
    asset_id: int,
    comparator_id: int,
    comparison_op: CompareOperation,
    input_val: Optional[float] = None,
    stale_band: Optional[float] = None
) -> Optional[LimitCheckResult]:
    """
    Determines if a measurement should be marked STALE based on configurable stale detection logic.

    Args:
        alert_id: Alert identifier
        measurement_id: Measurement to monitor
        now: Current timestamp
        last_changed: Last time the measurement value actually changed (can be None)
        stale_duration_minutes: Duration in minutes after which measurement is considered stale
        aggregate: Aggregation type (avg, max, min, etc.)
        period: Aggregation period (1min, 5min, etc.)
        asset_id: Asset identifier
        comparator_id: Comparator identifier for database
        comparison_op: CompareOperation.GT for strictly greater than, or CompareOperation.GE for greater or equal
        input_val: Current measurement value (can be None for no data)
        stale_band: Optional band range for stale detection (None = exact value matching)

    A measurement is considered stale if:
    1. No data available (input_val is None), OR
    2. Value hasn't changed for longer than stale_duration_minutes

    The measurement returns to NORMAL state when:
    1. A new distinct value is received (outside stale_band if configured)

    Returns a LimitCheckResult compatible with the existing event processing.
    """
    logger.debug(f"[STALE-CHECK] alert_id={alert_id}, meas_id={measurement_id}, input_val={input_val}, "
                f"last_changed={last_changed}, duration={stale_duration_minutes}min, band={stale_band}")

    # Handle no data case
    if input_val is None:
        logger.warning(f"[STALE] Measurement {measurement_id} - No data available")
        return LimitCheckResult(
            alert_id=alert_id,
            measurement_id=measurement_id,
            timestamp=int(now.timestamp() * 1000),
            state=LimitState.STALE.name,
            limit=0,
            comparator=comparator_id,
            input_value=0,  # Use 0 when no data
            deadband=0,
            aggregate=aggregate,
            period=period,
            asset_id=asset_id
        )

    # Get previous value and last changed time from storage
    prev_value = get_last_value(measurement_id)
    stored_last_changed = get_last_changed_time(measurement_id)

    # Use provided last_changed or fall back to stored value
    effective_last_changed = last_changed if last_changed is not None else stored_last_changed

    current_val = float(input_val)
    new_state = LimitState.NORMAL

    # For STALE alerts: Track when fresh data last arrived, not just value changes
    # This fixes the bug where repeated aggregated values trigger STALE incorrectly

    # Always update last_changed when we receive fresh data (input_val is not None)
    # This ensures STALE detection is based on data arrival, not value changes
    if input_val is not None:
        effective_last_changed = now
        store_value(measurement_id, current_val)
        store_last_changed_time(measurement_id, now)
        logger.debug(f"[STALE] Fresh data received: {current_val} at {now}")
    else:
        # No fresh data - keep existing last_changed time
        logger.debug(f"[STALE] No fresh data, using existing last_changed: {effective_last_changed}")

    # If we still don't have a last_changed time, use current time (shouldn't happen)
    if effective_last_changed is None:
        effective_last_changed = now
        store_last_changed_time(measurement_id, now)
        logger.warning(f"[STALE] No last_changed time available, using current time for measurement {measurement_id}")

    # Calculate time since last change
    time_since_change = now - effective_last_changed
    time_minutes = time_since_change.total_seconds() / 60.0

    # Check if time threshold exceeded based on comparison operation
    time_exceeded = (
        time_minutes > stale_duration_minutes if comparison_op == CompareOperation.GT
        else time_minutes >= stale_duration_minutes
    )

    logger.debug(f"[STALE] time_since_change={time_minutes:.2f}min, threshold={stale_duration_minutes}min, "
                f"exceeded={time_exceeded}, comparison_op={comparison_op.name}")

    # Stale band detection (range-based)
    if stale_band is not None and stale_band > 0:
        initial_value = get_initial_value(measurement_id)
        if initial_value is None:
            # First value - set reference
            store_initial_value(measurement_id, current_val)
            logger.info(f"[STALE-BAND] Set initial reference value {current_val} for measurement {measurement_id}")
        else:
            initial_value = float(initial_value)
            is_within_band = abs(current_val - initial_value) <= stale_band

            if not is_within_band:
                # Value moved outside band - reset tracking and update reference
                store_initial_value(measurement_id, current_val)
                store_last_changed_time(measurement_id, now)
                effective_last_changed = now  # Update local variable to reflect the reset
                logger.info(f"[STALE-BAND] Value {current_val} moved outside band {initial_value}±{stale_band}, resetting reference")
            elif time_exceeded:
                # Value stuck within band for too long
                new_state = LimitState.STALE
                logger.warning(f"[STALE-BAND] Value {current_val} stuck within band {initial_value}±{stale_band} for {time_minutes:.1f}min")

    # Standard stale detection (exact value matching)
    else:
        if time_exceeded and not value_changed:
            new_state = LimitState.STALE
            logger.warning(f"[STALE] Value {current_val} unchanged for {time_minutes:.1f}min (threshold: {stale_duration_minutes}min)")

    logger.info(f"[STALE-RESULT] alert_id={alert_id}, meas_id={measurement_id}, value={current_val}, "
               f"time_since_change={time_minutes:.1f}min, state={new_state.name}")

    return LimitCheckResult(
        alert_id=alert_id,
        measurement_id=measurement_id,
        timestamp=int(now.timestamp() * 1000),
        state=new_state.name,
        limit=0,
        comparator=comparator_id,
        input_value=current_val,
        deadband=0,
        aggregate=aggregate,
        period=period,
        asset_id=asset_id
    )
