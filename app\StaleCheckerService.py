from datetime import datetime, timezone, timedelta
from typing import Optional
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
from app.enums import LimitState, CompareOperation
from app.LimitCheckerService import LimitCheckResult
from app.redis import master

logger = setup_logging()

def get_last_value(measurement_id: int) -> Optional[float]:
    """Get last stored value from Redis."""
    key = f"measurement:{measurement_id}:last_value"
    value = master.get(key)
    return float(value) if value is not None else None

def store_value(measurement_id: int, value: float) -> None:
    """Store current value in Redis."""
    key = f"measurement:{measurement_id}:last_value"
    master.set(key, str(value), ex=86400)  # Expire after 24 hours

def get_last_changed_time(measurement_id: int) -> Optional[datetime]:
    """Get last changed timestamp from Redis."""
    key = f"measurement:{measurement_id}:last_changed"
    value = master.get(key)
    if value is not None:
        try:
            return datetime.fromtimestamp(float(value), tz=timezone.utc).replace(tzinfo=None)
        except (ValueError, TypeError):
            return None
    return None

def store_last_changed_time(measurement_id: int, timestamp: datetime) -> None:
    """Store last changed timestamp in Redis."""
    key = f"measurement:{measurement_id}:last_changed"
    master.set(key, str(timestamp.timestamp()), ex=86400)  # Expire after 24 hours

def store_initial_value(measurement_id: int, value: float) -> None:
    """Store initial value for stale band comparison."""
    key = f"measurement:{measurement_id}:initial_value"
    master.set(key, str(value), ex=86400)  # Expire after 24 hours

def get_initial_value(measurement_id: int) -> Optional[float]:
    """Get initial value for stale band comparison."""
    key = f"measurement:{measurement_id}:initial_value"
    value = master.get(key)
    return float(value) if value is not None else None


def _check_stale_exact_value(
    measurement_id: int,
    current_val: float,
    now: datetime,
    effective_last_changed: datetime,
    stale_duration_minutes: int,
    comparison_op: CompareOperation,
    prev_value: Optional[float]
) -> LimitState:
    """
    Mode 1: Without Stale Band (Exact Value Matching)
    Monitor if the aggregated value remains exactly unchanged for the specified duration.
    """
    # Calculate time since last change
    time_since_change = now - effective_last_changed
    time_minutes = time_since_change.total_seconds() / 60.0

    # Check if time threshold exceeded based on comparison operation
    time_exceeded = (
        time_minutes > stale_duration_minutes if comparison_op == CompareOperation.GT
        else time_minutes >= stale_duration_minutes
    )

    # Determine if value has actually changed (exact matching)
    value_changed = prev_value is None or abs(current_val - prev_value) > 1e-10

    logger.debug(f"[STALE-EXACT] meas_id={measurement_id}, time_since_change={time_minutes:.2f}min, "
                f"threshold={stale_duration_minutes}min, exceeded={time_exceeded}, value_changed={value_changed}")

    if value_changed:
        # Value changed - reset the timer and stay NORMAL
        store_last_changed_time(measurement_id, now)
        logger.debug(f"[STALE-EXACT] Value changed from {prev_value} to {current_val}, resetting timer")
        return LimitState.NORMAL
    elif time_exceeded:
        # Value unchanged for too long - trigger STALE
        logger.warning(f"[STALE-EXACT] Value {current_val} unchanged for {time_minutes:.1f}min (threshold: {stale_duration_minutes}min)")
        return LimitState.STALE
    else:
        # Value unchanged but within time threshold - stay NORMAL
        logger.debug(f"[STALE-EXACT] Value {current_val} unchanged for {time_minutes:.1f}min (within threshold)")
        return LimitState.NORMAL


def _check_stale_with_band(
    measurement_id: int,
    current_val: float,
    now: datetime,
    effective_last_changed: datetime,
    stale_duration_minutes: int,
    comparison_op: CompareOperation,
    stale_band: float
) -> LimitState:
    """
    Mode 2: With Stale Band (Range-Based Monitoring)
    Monitor if the aggregated value remains within a specified range (±stale_band) for the specified duration.
    """
    # Calculate time since last change
    time_since_change = now - effective_last_changed
    time_minutes = time_since_change.total_seconds() / 60.0

    # Check if time threshold exceeded based on comparison operation
    time_exceeded = (
        time_minutes > stale_duration_minutes if comparison_op == CompareOperation.GT
        else time_minutes >= stale_duration_minutes
    )

    # Get the reference value for band comparison
    reference_value = get_initial_value(measurement_id)

    if reference_value is None:
        # First value - set as reference and stay NORMAL
        store_initial_value(measurement_id, current_val)
        store_last_changed_time(measurement_id, now)
        logger.info(f"[STALE-BAND] Set initial reference value {current_val} for measurement {measurement_id}")
        return LimitState.NORMAL

    reference_value = float(reference_value)
    is_within_band = abs(current_val - reference_value) <= stale_band

    logger.debug(f"[STALE-BAND] meas_id={measurement_id}, current={current_val}, reference={reference_value}, "
                f"band=±{stale_band}, within_band={is_within_band}, time_since_change={time_minutes:.2f}min")

    if not is_within_band:
        # Value moved outside band - reset tracking and update reference, stay NORMAL
        store_initial_value(measurement_id, current_val)
        store_last_changed_time(measurement_id, now)
        logger.info(f"[STALE-BAND] Value {current_val} moved outside band {reference_value}±{stale_band}, resetting reference")
        return LimitState.NORMAL
    elif time_exceeded:
        # Value stuck within band for too long - trigger STALE
        logger.warning(f"[STALE-BAND] Value {current_val} stuck within band {reference_value}±{stale_band} for {time_minutes:.1f}min")
        return LimitState.STALE
    else:
        # Value within band but within time threshold - stay NORMAL
        logger.debug(f"[STALE-BAND] Value {current_val} within band {reference_value}±{stale_band} for {time_minutes:.1f}min (within threshold)")
        return LimitState.NORMAL


def check_stale_measurement(
    alert_id: int,
    measurement_id: int,
    now: datetime,
    last_changed: Optional[datetime],
    stale_duration_minutes: int,
    aggregate: str,
    period: str,
    asset_id: int,
    comparator_id: int,
    comparison_op: CompareOperation,
    input_val: Optional[float] = None,
    stale_band: Optional[float] = None
) -> Optional[LimitCheckResult]:
    """
    Determines if a measurement should be marked STALE based on two distinct modes of operation.

    **Mode 1: Without Stale Band (Exact Value Matching)**
    - Monitor if the aggregated value remains exactly unchanged for the specified duration
    - If the same aggregated value persists beyond the duration threshold, trigger STALE state
    - Otherwise, maintain NORMAL state

    **Mode 2: With Stale Band (Range-Based Monitoring)**
    - Monitor if the aggregated value remains within a specified range (±stale_band) for the specified duration
    - If the aggregated value stays within the band range beyond the duration threshold, trigger STALE state
    - If the aggregated value moves outside the band range, reset the timer and maintain NORMAL state

    Args:
        alert_id: Alert identifier
        measurement_id: Measurement to monitor
        now: Current timestamp
        last_changed: Last time the measurement value actually changed (can be None)
        stale_duration_minutes: Duration in minutes after which measurement is considered stale
        aggregate: Aggregation type (avg, max, min, etc.)
        period: Aggregation period (1min, 5min, etc.)
        asset_id: Asset identifier
        comparator_id: Comparator identifier for database
        comparison_op: CompareOperation.GT for strictly greater than, or CompareOperation.GE for greater or equal
        input_val: Current measurement value (can be None for no data)
        stale_band: Optional band range for stale detection (None/0 = exact value matching, >0 = range-based)

    Returns a LimitCheckResult compatible with the existing event processing.
    """
    # Determine operation mode based on stale_band configuration
    has_stale_band = stale_band is not None and stale_band > 0
    mode_description = "Range-Based (Band)" if has_stale_band else "Exact Value Matching"

    logger.debug(f"[STALE-CHECK] alert_id={alert_id}, meas_id={measurement_id}, input_val={input_val}, "
                f"last_changed={last_changed}, duration={stale_duration_minutes}min, band={stale_band}, mode={mode_description}")

    # Handle no data case - always STALE regardless of mode
    if input_val is None:
        logger.warning(f"[STALE] Measurement {measurement_id} - No data available")
        return LimitCheckResult(
            alert_id=alert_id,
            measurement_id=measurement_id,
            timestamp=int(now.timestamp() * 1000),
            state=LimitState.STALE.name,
            limit=0,
            comparator=comparator_id,
            input_value=0,  # Use 0 when no data
            deadband=0,
            aggregate=aggregate,
            period=period,
            asset_id=asset_id
        )

    # Get previous value and last changed time from storage
    prev_value = get_last_value(measurement_id)
    stored_last_changed = get_last_changed_time(measurement_id)

    # Use provided last_changed or fall back to stored value
    effective_last_changed = last_changed if last_changed is not None else stored_last_changed

    current_val = float(input_val)
    new_state = LimitState.NORMAL

    # Always track when fresh data arrives to fix the bug where repeated aggregated values
    # would trigger STALE even though fresh raw data was coming in regularly
    # This ensures STALE detection is based on data arrival, not just value changes
    if input_val is not None:
        # We have fresh data - always update the current value storage
        store_value(measurement_id, current_val)
        logger.debug(f"[STALE] Fresh data received: {current_val} at {now}")

    # Initialize effective_last_changed if not available
    if effective_last_changed is None:
        effective_last_changed = now
        store_last_changed_time(measurement_id, now)
        logger.info(f"[STALE] First measurement for {measurement_id}, initializing timestamp")

    # Now implement the two distinct modes of operation
    if has_stale_band:
        # **Mode 2: With Stale Band (Range-Based Monitoring)**
        new_state = _check_stale_with_band(
            measurement_id, current_val, now, effective_last_changed,
            stale_duration_minutes, comparison_op, stale_band
        )
    else:
        # **Mode 1: Without Stale Band (Exact Value Matching)**
        new_state = _check_stale_exact_value(
            measurement_id, current_val, now, effective_last_changed,
            stale_duration_minutes, comparison_op, prev_value
        )

    # Calculate final time since change for logging
    final_time_since_change = now - effective_last_changed
    final_time_minutes = final_time_since_change.total_seconds() / 60.0

    logger.info(f"[STALE-RESULT] alert_id={alert_id}, meas_id={measurement_id}, value={current_val}, "
               f"time_since_change={final_time_minutes:.1f}min, state={new_state.name}, mode={mode_description}")

    return LimitCheckResult(
        alert_id=alert_id,
        measurement_id=measurement_id,
        timestamp=int(now.timestamp() * 1000),
        state=new_state.name,
        limit=0,
        comparator=comparator_id,
        input_value=current_val,
        deadband=0,
        aggregate=aggregate,
        period=period,
        asset_id=asset_id
    )
