from datetime import datetime, timed<PERSON>ta
from typing import Optional
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
from app.enums import LimitState, CompareOperation
from app.LimitCheckerService import LimitCheckResult
from app.redis import master

logger = setup_logging()

def get_last_value(measurement_id: int) -> Optional[float]:
    """Get last stored value from Redis."""
    key = f"measurement:{measurement_id}:last_value"
    value = master.get(key)
    return float(value) if value is not None else None

def store_value(measurement_id: int, value: float) -> None:
    """Store current value in Redis."""
    key = f"measurement:{measurement_id}:last_value"
    master.set(key, str(value), ex=86400)  # Expire after 24 hours

def store_initial_value(measurement_id: int, value: float) -> None:
    """Store initial value for stale band comparison."""
    key = f"measurement:{measurement_id}:initial_value"
    master.set(key, str(value), ex=86400)  # Expire after 24 hours

def get_initial_value(measurement_id: int) -> Optional[float]:
    """Get initial value for stale band comparison."""
    key = f"measurement:{measurement_id}:initial_value"
    value = master.get(key)
    return float(value) if value is not None else None


def check_stale_measurement(
    alert_id: int,
    measurement_id: int,
    now: datetime,
    last_changed: Optional[datetime],
    stale_duration_minutes: int,
    aggregate: str,
    period: str,
    asset_id: int,
    comparator_id: int,
    comparison_op: CompareOperation,
    input_val: Optional[float] = None,
    stale_band: Optional[float] = None
) -> Optional[LimitCheckResult]:
    """
    Determines if a measurement should be marked STALE based on last update time and comparison operation.
    
    Args:
        stale_duration_minutes: Duration in minutes after which measurement is considered stale
        comparison_op: CompareOperation.GT for strictly greater than, or CompareOperation.GE for greater or equal
    
    A measurement is considered stale if:
    1. No last_changed timestamp exists (no data)
    2. Time since last update exceeds stale_duration_minutes based on comparison operation
    3. Value hasn't changed since last check
    
    The measurement returns to NORMAL state when:
    1. Time since update is within threshold
    2. A new distinct value is received
    
    Returns a LimitCheckResult compatible with the existing event processing.
    """
    # Convert duration from minutes to seconds
    stale_duration_seconds = stale_duration_minutes * 60

    # If we have no last_changed timestamp or no input value, mark as stale immediately
    if last_changed is None or input_val is None:
        logger.warning(f"[STALE] Measurement {measurement_id} - No data available - last_changed: {last_changed}, input_val: {input_val}")
        new_state = LimitState.STALE
        # Always set input value to 0 when there is no data
        input_val_to_store = 0
        return LimitCheckResult(
            alert_id=alert_id,
            measurement_id=measurement_id,
            timestamp=int(now.timestamp() * 1000),  # Convert to millisecond timestamp
            state=new_state.name,
            limit=0,  # Not required for stale alerts
            comparator=comparator_id,
            input_value=input_val_to_store,
            deadband=0,  # Required to be 0 for stale alerts
            aggregate=aggregate,
            period=period,
            asset_id=asset_id
        )

    # Get previous value for comparison
    prev_value = get_last_value(measurement_id)
    
    # Initialize state and values
    new_state = LimitState.NORMAL
    current_val = input_val if input_val is not None else 0
    prev_value = get_last_value(measurement_id)
    if input_val is not None and (prev_value is None or float(input_val) != float(prev_value)):
        store_value(measurement_id, float(current_val))

    # Get time difference
    time_since_update = now - last_changed
    time_seconds = time_since_update.total_seconds()
    stale_seconds = stale_duration_minutes * 60

    # First check if time threshold exceeded
    time_exceeded = (
        time_seconds > stale_seconds if comparison_op == CompareOperation.GT
        else time_seconds >= stale_seconds
    )

    # If stale_band is configured, check for values stuck within band
    if stale_band is not None and input_val is not None:
        initial_value = get_initial_value(measurement_id)
        if initial_value is None:
            # First value - set reference
            store_initial_value(measurement_id, current_val)
            logger.info(f"[STALE-BAND] Initial value {current_val} for measurement {measurement_id}")
        else:
            initial_value = float(initial_value)
            is_within_band = abs(current_val - initial_value) <= stale_band

            if not is_within_band:
                # Value moved outside band - reset tracking
                store_initial_value(measurement_id, current_val)
                logger.info(f"[STALE-BAND] Value {current_val} outside band {initial_value}±{stale_band}")
            elif time_exceeded:
                # Value stuck within band for too long
                new_state = LimitState.STALE
                logger.warning(f"[STALE-BAND] Value stuck near {initial_value} for {time_seconds/60:.1f}min")
    
    # Standard stale check for exact value changes
    elif time_exceeded and prev_value is not None:
        if current_val == prev_value:
            new_state = LimitState.STALE
            logger.warning(f"[STALE] No change for {time_seconds/60:.1f}min")

    # Return result
    return LimitCheckResult(
        alert_id=alert_id,
        measurement_id=measurement_id,
        timestamp=int(now.timestamp() * 1000),
        state=new_state.name,
        limit=0,
        comparator=comparator_id,
        input_value=current_val,
        deadband=0,
        aggregate=aggregate,
        period=period,
        asset_id=asset_id
    )
