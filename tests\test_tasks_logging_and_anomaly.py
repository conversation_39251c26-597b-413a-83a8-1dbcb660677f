import pytest
from unittest.mock import MagicMock, patch
from app.tasks import evaluate_alert_task_async
from app.enums import ThresholdType
import logging
import asyncio

@pytest.mark.asyncio
def test_warning_logged_for_missing_measurement_data(caplog, monkeypatch):
    # Setup a mock task and alert config
    mock_task = MagicMock()
    mock_task.customer_id = 1
    mock_task.alert_ids = [1]
    mock_task.aggregate = 'max'
    mock_task.aggregate_period = '2hr'

    mock_alert_config = MagicMock()
    mock_alert_config.id = 1
    mock_alert_config.measurement_id = 1
    mock_alert_config.threshold_value = 10
    mock_alert_config.reset_deadband = 0.5
    mock_alert_config.comparison_enum = MagicMock(condition='GE')
    mock_alert_config.asset_id = 1
    mock_alert_config.agg = None
    mock_alert_config.period = None
    mock_alert_config.threshold_type_enum = MagicMock(threshold='NOMINAL')
    monkeypatch.setattr('app.tasks.fetch_alerts_by_ids', lambda ids: [mock_alert_config])

    # Patch read_data_new to return empty data (simulate missing measurement)
    async def mock_read_data_new(**kwargs):
        return {}
    monkeypatch.setattr('app.tasks.read_data_new', mock_read_data_new)

    with caplog.at_level(logging.WARNING):
        asyncio.run(evaluate_alert_task_async(mock_task))
    assert any('Failed to fetch data for measurement ID' in r.message for r in caplog.records)
    assert not any(r.levelname == 'ERROR' for r in caplog.records)

@pytest.mark.asyncio
def test_anomaly_threshold_type_skipped(caplog, monkeypatch):
    mock_task = MagicMock()
    mock_task.customer_id = 1
    mock_task.alert_ids = [2]
    mock_task.aggregate = 'max'
    mock_task.aggregate_period = '2hr'

    mock_alert_config = MagicMock()
    mock_alert_config.id = 2
    mock_alert_config.measurement_id = 2
    mock_alert_config.threshold_value = 10
    mock_alert_config.reset_deadband = 0.5
    mock_alert_config.comparison_enum = None  # Should not matter for ANOMALY
    mock_alert_config.asset_id = 1
    mock_alert_config.agg = None
    mock_alert_config.period = None
    mock_alert_config.threshold_type_enum = MagicMock(threshold=ThresholdType.ANOMALY)
    monkeypatch.setattr('app.tasks.fetch_alerts_by_ids', lambda ids: [mock_alert_config])

    # Patch read_data_new to return some data
    async def mock_read_data_new(**kwargs):
        return {2: {'status': 'success', 'timestamp': 1234567890, 'value': 42}}
    monkeypatch.setattr('app.tasks.read_data_new', mock_read_data_new)

    with caplog.at_level(logging.INFO):
        asyncio.run(evaluate_alert_task_async(mock_task))
    assert any('Skipping alert ID 2 with threshold type ANOMALY' in r.message for r in caplog.records)
    assert not any('Comparison enum is None for alert ID 2' in r.message for r in caplog.records)

@pytest.mark.asyncio
def test_no_error_for_missing_comparison_enum_on_anomaly(monkeypatch, caplog):
    mock_task = MagicMock()
    mock_task.customer_id = 1
    mock_task.alert_ids = [3]
    mock_task.aggregate = 'max'
    mock_task.aggregate_period = '2hr'

    mock_alert_config = MagicMock()
    mock_alert_config.id = 3
    mock_alert_config.measurement_id = 3
    mock_alert_config.threshold_value = 10
    mock_alert_config.reset_deadband = 0.5
    mock_alert_config.comparison_enum = None
    mock_alert_config.asset_id = 1
    mock_alert_config.agg = None
    mock_alert_config.period = None
    mock_alert_config.threshold_type_enum = MagicMock(threshold=ThresholdType.ANOMALY)
    monkeypatch.setattr('app.tasks.fetch_alerts_by_ids', lambda ids: [mock_alert_config])

    async def mock_read_data_new(**kwargs):
        return {3: {'status': 'success', 'timestamp': 1234567890, 'value': 42}}
    monkeypatch.setattr('app.tasks.read_data_new', mock_read_data_new)

    with caplog.at_level(logging.INFO):
        asyncio.run(evaluate_alert_task_async(mock_task))
    assert not any('Comparison enum is None for alert ID 3' in r.message for r in caplog.records)
    assert any('Skipping alert ID 3 with threshold type ANOMALY' in r.message for r in caplog.records)
