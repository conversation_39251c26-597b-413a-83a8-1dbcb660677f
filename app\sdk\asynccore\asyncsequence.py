# A task that runs a sequence of tasks and adds control
from typing import Dict
from .asyncevent import *

# Dict has task and flag indicating if it should pass results as argument to next task
class AsyncSequence(AsyncTaskWithEvent):
    def __init__(self, event_context:str, tasks:Dict[AsyncTask, bool],
                 completion_callback=None,timeout=None, persist_obj:PersistentObject=None):
        if(not tasks or len(tasks)<2):
            raise ValueError("Sequences require a minimum of two tasks")
        self.__running__=False
        self.__start_task__=0
        self.tasks=tasks  # Task and wether or not to pass state to the next task
        self.__prev_result__=None
        super().__init__(event_context=event_context,completion_callback=completion_callback,timeout=timeout,persist_obj=persist_obj)

    @property
    def persist_obj(self):
        return self.__persist_obj__

    @property
    def tasks(self):
        return self.__tasks__

    @tasks.setter
    def tasks(self,tasks:Dict[AsyncTask, bool]):
        if(not(tasks) or not(isinstance(tasks,dict)) or not(isinstance(list(tasks.keys())[0],AsyncTask)) or not(isinstance(list(tasks.values())[0],bool))):
            raise ValueError("Task can only be a dictionary of AsyncTask->bool")
        self.__tasks__=tasks
        self.__task_list__=list(tasks.keys())
        self.__pass_result_list__=list(tasks.values())
        self.__paused__=asyncio.Event()
        self.__resumed__=asyncio.Event()
        self.__cancelled__=asyncio.Event()
        self.__idle__=asyncio.Event()
        self.__done__(self.__idle__)
        self.state=None
        self.__failed__=False # indicates that running failed (exception in step)

    @property
    def state(self):
        return self.__state__

    @state.setter
    def state(self,state):
        self.__state__=state

    @property
    def running(self):
        return not(self.__idle__.is_set())

    @property
    def cancelled(self):
        return self.__cancelled__.is_set()

    @property
    def failed(self):
        return self.__failed__

    @property
    def paused(self):
        return self.__paused__.is_set()

    # Control Methods
    # Stop execution and restart
    async def restart(self):
        try:
            await self.cancel()  # cancel and wait to cancelled to be done
            self.__start_task__ = 0
            await self.execute(self.state)
        except ValueError:
            raise ValueError("Task does not exist")

    # Re-executes given step
    async def retry(self,task):
        try:
            self.__task_list__.index(task)
            await task.execute(state=self.state)
        except ValueError:
            raise ValueError("Task does not exist")

    # Executes given step and continues the sequence from that step
    async def jump_to(self,task):
        try:
            task_idx=self.__task_list__.index(task)
            await self.cancel()  # cancel and wait to cancelled to be done
            self.__start_task__ = task_idx
            await self.execute(self.state)
        except ValueError:
            raise ValueError("Task does not exist")

    # Cancels/aborts sequence after completion of current task
    async def cancel(self):
        # only if running
        if(self.running):
            self.__cancel__=True

    # Pauses sequence after completion of current task
    async def pause(self):
        # only if running
        if(self.running):
            self.__paused__.clear()
            self.__pause__=True
            self.__resumed__.clear()
            await self.__wait_for__(self.__paused__)
            self.__pause__=False

    def resume(self):
        self.__paused__.clear()
        self.__resumed__.clear()
        self.__resumed__.set()

    # private methods
    async def __wait_for__(self,event: asyncio.Event):
        await event.wait()

    def __done__(self,event: asyncio.Event):
        event.set()

    # overriden methods
    async def __execute__(self, state:EventState, *args, **kwargs):
        # if running await until done - uses asyncio queue
        await self.__wait_for__(self.__idle__)
        self.__idle__.clear()
        self.__failed__=False
        self.__pause__=False
        self.__cancel__=False
        try:
            # execute in sequence
            for task,pass_result in zip(self.__task_list__[self.__start_task__:],self.__pass_result_list__[self.__start_task__:]):
                if(pass_result and not(self.__prev_result__ is None)):
                    self.__current__task__=task.execute(self.state,self.__prev_result__)
                else:
                    self.__current__task__=task.execute(self.state)
                self.__prev_result__, state = await (wrapper_w_timeout(self.__current__task__) if task.timeout else self.__current__task__)
                if(isinstance(self.__prev_result__,Exception)):
                    self.__failed__=True
                    break
                if(self.__pause__):
                    self.__done__(self.__paused__)
                    await self.__wait_for__(self.__resumed__)
                if(self.__cancel__):
                    break
        except Exception as e:
            self.__prev_result__=e
            self.__failed__=True
        finally:
            self.__done__(self.__idle__)
            state="FAILED" if self.__failed__ else ("CANCELLED" if self.__cancel__ else "COMPLETED")
            await self.__trigger_event__(state)
        return {'last_completed_task_result':self.__prev_result__,'status':self.__event__.state.state} # return result of last task