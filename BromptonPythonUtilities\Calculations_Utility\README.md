# Calculations Utility

This module provides utilities for working with calculations in the Brompton Python Utilities system.

## Features

- Fetch calculation metadata from database
- Build calculation dependency graphs
- Handle offline calculations
- Support for interpolation methods (linear, fill)
- Compatible with both SQLAlchemy and direct database cursor connections

## Usage

### Get Calculation Graph

```python
from BromptonPythonUtilities.Calculations_Utility.calc_utils_updated import get_calcs_graph
from sqlalchemy import Table, MetaData, Column, Integer, String, Float, create_engine

# Option 1: Using SQLAlchemy
engine = create_engine('postgresql://user:password@host:port/database')
metadata = MetaData()
calc_table = Table(
    'v_calculations', 
    metadata,
    Column('meas_id', Integer),
    # ... other columns ...
)

calc_nodes, meas_nodes, calc_layers = get_calcs_graph(
    connection=engine,
    ids=[100, 101],  # List of calculation IDs
    table=calc_table
)

# Option 2: Using database hook with cursor
from airflow.providers.postgres.hooks.postgres import PostgresHook

hook = PostgresHook(postgres_conn_id='your_connection')
calc_nodes, meas_nodes, calc_layers = get_calcs_graph(
    connection=hook,
    ids=[100, 101]  # List of calculation IDs
)

# Working with the results
for calc_id, calc_info in calc_nodes.items():
    print(f"Calculation: {calc_info['tag']} ({calc_id})")
    print(f"Expression: {calc_info['expression']}")
    for input_name, input_details in calc_info['inputs'].items():
        print(f"  - {input_name}: {input_details}")
```

### Get Offline Calculations

```python
from BromptonPythonUtilities.Calculations_Utility.calc_utils_updated import get_offline_calcs

hook = PostgresHook(postgres_conn_id='your_connection')
offline_calcs = get_offline_calcs(hook)

for poll_period, customers in offline_calcs.items():
    for cust_id, measurements in customers.items():
        print(f"Poll period: {poll_period}, Customer: {cust_id}")
        for meas_id, data_type in measurements:
            print(f"  - Measurement ID: {meas_id}, Type: {data_type}")
```

## Running Tests

The module includes pytest tests to verify functionality. Follow these steps to run the tests:

### Prerequisites

- Python 3.7+
- pytest
- SQLAlchemy
- Mock

### Running Tests from Command Line

From the project root directory:

```bash
# Run all tests in the Calculations_Utility module
python -m pytest BromptonPythonUtilities/Calculations_Utility/tests/ -v

# Run a specific test file
python -m pytest BromptonPythonUtilities/Calculations_Utility/tests/test_calc_utils.py -v

# Run a specific test function
python -m pytest BromptonPythonUtilities/Calculations_Utility/tests/test_calc_utils.py::test_get_calcs_graph_with_cursor -v
```

### Running Tests with Coverage Report

```bash
# Install pytest-cov if you don't have it
pip install pytest-cov

# Run tests with coverage report
python -m pytest BromptonPythonUtilities/Calculations_Utility/tests/ --cov=BromptonPythonUtilities.Calculations_Utility --cov-report=html
```

## Troubleshooting

If you encounter import errors when running tests:
1. Make sure your Python environment has all required dependencies
2. Check that the module is in your Python path
3. If using relative imports, ensure you're running pytest from the correct directory

## Note About Module Versions

This module uses `calc_utils_updated.py` which is an updated version of the original `calc_utils.py`. The updated version includes:
- Enhanced interpolation logic
- Better error handling
- Improved compatibility with both SQLAlchemy and direct database connections
- Graceful handling of SQLAlchemy import failures (falls back to using `typing.Any` for `Engine` type)

### Test Organization

The tests are organized into separate files for different aspects:

1. `test_calc_utils.py`: Core functionality tests
   - Calculation graph building
   - Offline calculations
   - Database interaction patterns
   - Data type handling

2. `test_calc_utils_import_handling.py`: SQLAlchemy import tests
   - SQLAlchemy Engine import success scenarios
   - Graceful fallback to typing.Any when Engine import fails
   - Preservation of other SQLAlchemy functionality

Run specific test suites with:
```bash
# Core functionality tests
python -m pytest BromptonPythonUtilities/Calculations_Utility/tests/test_calc_utils.py -v

# Import handling tests
python -m pytest BromptonPythonUtilities/Calculations_Utility/tests/test_calc_utils_import_handling.py -v
```
