import re
import contextvars
import logging
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
logger= setup_logging()
# logger = logging.getLogger(__name__)

# Context variables for dynamic fields
customer_id_context = contextvars.ContextVar("customer_id", default="00")
alert_id_context = contextvars.ContextVar("alert_id", default=None)

# Middleware to extract customerId and set in context
class CustomMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Initialize default values
        customer_id = "00"
        alert_id = None

        # Parse JSON body for supported methods including DELETE
        if request.method in ["POST", "PUT", "PATCH", "DELETE"]:  # DELETE now included
            try:
                # Read and parse the request body
                body = await request.json()
                # print("Request JSON:", body)

                # Extract alert_id and customer_id
                alert_id = body.get("alert_id",None)
                customer_id = body.get("customer_id", "00")  # Default to "00" if not found

            except Exception as e:
                print("Error parsing JSON:", e)

        # Set values in the context variables
        customer_id_token = customer_id_context.set(customer_id)
        alert_id_token = alert_id_context.set(alert_id)


        try:
            response = await call_next(request)
        finally:
             # Reset context variables to previous states
            customer_id_context.reset(customer_id_token)
            alert_id_context.reset(alert_id_token)
        return response