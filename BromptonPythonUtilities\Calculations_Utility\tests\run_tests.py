import pytest
import os
import sys

# Add parent directories to path to ensure modules can be found
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(os.path.dirname(parent_dir))
sys.path.extend([parent_dir, root_dir])

if __name__ == "__main__":
    # Run the tests in this directory
    print(f"Running tests from {current_dir}")
    pytest.main([current_dir, "-v"])
    
    # Alternatively, run just the calc_utils tests
    # pytest.main(["test_calc_utils.py", "-v"])
