# EventMonitor based on old Scheduler triggers and event
# TODO: Stream Event Monitor Task (parent to many interesting classes) - MQTT Event Monitor (more specialized)
# TODO: VALUE STALENESS TASK WITH TARGET (OPTIONAL)  USES VALUE CHANGE EVENT MONITOR
import async<PERSON>
from datetime import datetime
from .asyncevent import *
from typing import Union
import logging
logger=logging.getLogger('asyncevent')


class AsyncEventScheduler():
    """ Abstract class that defines a core event monitor that:
        raises events via their trigger callback.
        The callback must be a function in the format: 'callback(state)',
        where 'state' is any object to be passed to the callback.
    """
    def __init__(self,event:AsyncEvent,persist_obj:PersistentObject=None):
        if(not(event) or not(isinstance(event,AsyncEvent))):
            raise ValueError("Event must be of type AsyncEvent")
        self.__persist_obj__=persist_obj
        self.__event__=event
        self.__running__=False

    # Properties
    @property
    def persist_obj(self):
        return self.__persist_obj__

    # Called by monitor to trigger event
    # This method can be overriden for more specialized forms
    async def __trigger_event__(self):
        self.__event__.state.timestamp=datetime.now().timestamp()
        if(self.persist_obj):
            self.__event__.state.source=self.persist_obj.id
        await self.__event__.trigger(self.__event__.__state__)

class AsyncControlledEventScheduler(AsyncEventScheduler):
    """  The Monitor processing is controllable via a 'start()' method and continues to run until
        it's 'stop()' method is called'
    """
    def __init__(self, event: AsyncEvent, persist_obj: PersistentObject = None):
        if (not (event) or not (isinstance(event, AsyncEvent))):
            raise ValueError("Event must be of type AsyncEvent")
        self.__persist_obj__ = persist_obj
        self.__event__ = event
        self.__running__ = False
        super().__init__(event,persist_obj)

    @property
    def running(self):
        return self.__running__

    # Abstract method pythonic way
    async def start(self):
        raise NotImplementedError("This method is Abstract.  Must implement in derived class")

    # Abstract method pythonic way
    async def stop(self):
        raise NotImplementedError("This method is Abstract.  Must implement in derived class")

# Poll core
class AsyncPollScheduler(AsyncControlledEventScheduler):
    """ A type of core that triggers an event at some predefined interval
        in seconds defined by the 'poll_secs' parameter.  It executes the passed
        event trigger function at the predefined interval and also at first time
        it is started if the 'skip_start_event' parameter is False.
        It executest only once and stops if 'one_shot' is True.
        The AsyncPollMonitor implements an async context manager such that it can be
        invoked using 'with'.
    """
    def __init__(self, event:AsyncEvent, poll_secs:float=5, skip_start_event=True, one_shot=False,persist_obj:PersistentObject=None):
        self.__persist_obj__=persist_obj
        self.__running__=False # for proper initialization
        self.poll_secs=poll_secs
        self.skip_start_event=skip_start_event
        self.one_shot=one_shot
        super().__init__(event,persist_obj)

    # Properties
    @property
    def persist_obj(self):
        return self.__persist_obj__

    @property
    def poll_secs(self):
        return self.__pollSecs__

    @poll_secs.setter
    def poll_secs(self, pollSecs):
        if not isinstance(pollSecs,(int,float)):
            raise ValueError("poll_secs must be a numeric type")
        if pollSecs<0:
            self.__pollSecs__=1  # minmum poll period
        else:
            self.__pollSecs__=pollSecs

    @property
    def skip_start_event(self):
        return self.__skip_start_event__

    @skip_start_event.setter
    def skip_start_event(self, skip_start_event:bool):
        if not isinstance(skip_start_event,bool):
            raise ValueError("skip_start_event must be bool type ")
        self.__skip_start_event__=skip_start_event
        
    @property
    def one_shot(self):
        return self.__one_shot__

    @one_shot.setter
    def one_shot(self, one_shot:bool):
        if not isinstance(one_shot,bool):
            raise ValueError("one_shot must be bool type ")
        self.__one_shot__=one_shot

    # Overriden start
    async def start(self):
        """
        Starts scheduling events
        """
        if self.__running__:
            return
        first_task=self.__trigger_event__() if not(self.skip_start_event) else asyncio.sleep(0)
        await asyncio.wait([asyncio.create_task(first_task),
                            asyncio.create_task(self.__schedule_timer__())],return_when=asyncio.FIRST_COMPLETED)
#        print("start completed")

    # Overriden stop
    async def stop(self):
        """
       Stops scheduling events
        """
        self.__stopped_event__=asyncio.Event()
        self.__stopped_event__.clear()
        #  TODO: not a good idea next two line cause lock up
        # if(self.__timer__):
        #     self.__timer__.cancel()
        # if(self.__trigger__):
        #     self.__trigger__.cancel()
        self.__running__=False
        await self.__stopped_event__.wait()
#        print("stop completed")

    # Context manager - support for with
    async def __aenter__(self):
        return self
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.stop()

    async def __schedule_timer__(self):
        self.__running__=True
        while(self.running):
            logger.debug(f"poll started with {self.poll_secs} sec")
            # self.__timer__=asyncio.create_task(asyncio.sleep(self.poll_secs))
            # await self.__timer__
            await asyncio.sleep(self.__pollSecs__)
            logger.debug(f"{self.poll_secs} sec elapsed")
            self.__trigger__=asyncio.create_task(self.__trigger_event__())
            await self.__trigger__
            logger.debug("poll event trigger complete")
        self.__stopped_event__.set()


# TODO: Calendar Scheduler





