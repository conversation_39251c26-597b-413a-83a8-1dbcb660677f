import pytest
from unittest.mock import MagicMock, patch
import sys
from collections import namedtuple
from sqlalchemy import MetaData, Table, Column, Integer, String, Float
from sqlalchemy.engine import Engine
import os

# Add parent directory to path to enable imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


# Use absolute import for the updated module
from calc_utils_updated import get_calcs_graph, RowTuple, columns

# Create fixtures for testing
@pytest.fixture
def mock_sqlalchemy_engine():
    """Mock SQLAlchemy engine and connection"""
    engine = MagicMock(spec=Engine)
    connection = MagicMock()
    engine.connect.return_value.__enter__.return_value = connection
    return engine, connection

@pytest.fixture
def mock_db_hook():
    """Mock database hook with cursor method"""
    hook = MagicMock()
    cursor = MagicMock()
    hook.cursor.return_value = cursor
    return hook, cursor

@pytest.fixture
def sample_table():
    """Create a sample SQLAlchemy table for testing"""
    metadata = MetaData()
    table = Table(
        'v_calculations', 
        metadata,
        Column('meas_id', Integer),
        Column('calc_tag', String),
        Column('input_label', String),
        Column('comment', String),
        Column('input_id', Integer),
        Column('constant_number', Float),
        Column('constant_string', String),
        Column('input_datasource', Integer),
        Column('expression', String),
        Column('template_id', Integer),
        Column('input_tag', String),
        Column('datasource', Integer),
        Column('poll_period', String),
        Column('data_type', String)
    )
    return table

@pytest.fixture
def sample_rows():
    """Create sample row data for testing"""
    # Main calculation row
    main_calc = RowTuple(
        meas_id=100,
        calc_tag='MainCalc',
        input_label='input1',
        comment='Test comment',
        input_id=101,
        constant_number=None,
        constant_string=None,
        input_datasource=3,
        expression='a+b',
        template_id=1,
        input_tag='InputTag1',
        datasource=3,
        poll_period='5min',
        data_type='float'
    )
    
    # Dependency calculation row
    dep_calc = RowTuple(
        meas_id=101,
        calc_tag='DepCalc',
        input_label='input2',
        comment='Dependency',
        input_id=102,
        constant_number=None,
        constant_string=None,
        input_datasource=0,
        expression='c*d',
        template_id=2,
        input_tag='InputTag2',
        datasource=0,
        poll_period='15min',
        data_type='float'
    )
    
    # Row with constant and interpolation
    calc_with_constant = RowTuple(
        meas_id=100,
        calc_tag='MainCalc',
        input_label='input3',
        comment='Constant input',
        input_id=None,
        constant_number=5.5,
        constant_string=None,
        input_datasource=None,
        expression='a+b',
        template_id=1,
        input_tag=None,
        datasource=None,
        poll_period='5min',
        data_type='float'
    )
    
    # Row with fill interpolation
    calc_with_fill = RowTuple(
        meas_id=100,
        calc_tag='MainCalc',
        input_label='input4',
        comment='Fill interpolation',
        input_id=103,
        constant_number=None,
        constant_string=None,
        input_datasource=4,
        expression='a+b',
        template_id=1,
        input_tag='InputTag4',
        datasource=None,
        poll_period='5min',
        data_type='float'
    )
    
    return [main_calc, dep_calc, calc_with_constant, calc_with_fill]

def test_get_calcs_graph_with_sqlalchemy(mock_sqlalchemy_engine, sample_table, sample_rows):
    """Test get_calcs_graph with SQLAlchemy engine"""
    engine, connection = mock_sqlalchemy_engine
    
    # Configure the mock to return sample data
    connection.execute.return_value.fetchall.return_value = sample_rows
    
    # Test the function
    calc_nodes, meas_nodes, calc_layers = get_calcs_graph(engine, [100], sample_table)
    
    # Assertions
    assert len(calc_nodes) == 2
    assert 100 in calc_nodes
    assert 101 in calc_nodes
    assert calc_nodes[100]['tag'] == 'MainCalc'
    assert calc_nodes[100]['expression'] == 'a+b'
    # Fix: The actual number of inputs is 3, not 4
    assert len(calc_nodes[100]['inputs']) == 3
    assert 'uses_factors' in calc_nodes[100]
    assert calc_nodes[100]['uses_factors'] is True
    
    # Verify the actual input keys
    assert set(calc_nodes[100]['inputs'].keys()) == {'input1', 'input3', 'input4'}
    
    # Check measurement nodes
    assert 102 in meas_nodes
    assert 103 in meas_nodes
    assert meas_nodes[103]['tag'] == 'InputTag4'
    
    # Check layers
    assert len(calc_layers) == 2
    assert 100 in calc_layers[0]
    assert 101 in calc_layers[1]

def test_get_calcs_graph_with_cursor(mock_db_hook, sample_rows):
    """Test get_calcs_graph with database hook cursor"""
    hook, cursor = mock_db_hook
    
    # Configure the mock to return sample data
    cursor.fetchall.return_value = [tuple(row) for row in sample_rows]
    
    # Test the function
    calc_nodes, meas_nodes, calc_layers = get_calcs_graph(hook, [100])
    
    # Assertions
    assert len(calc_nodes) == 2
    assert calc_nodes[100]['poll_period'] == '5min'
    assert calc_nodes[100]['data_type'] == 'float'
    assert calc_nodes[100]['inputs']['input1']['interpolate'] is None
    assert calc_nodes[100]['inputs']['input3']['constant'] == 5.5
    assert calc_nodes[100]['inputs']['input4']['interpolate'] == 'fill'
    
    # Verify SQL was executed with correct parameters
    cursor.execute.assert_called_once()
    execute_args = cursor.execute.call_args[0][0]
    assert "select" in execute_args.lower()
    assert "from v_calculations" in execute_args.lower()
    assert "where meas_id in (100)" in execute_args.lower()

def test_get_calcs_graph_linear_interpolation(mock_db_hook):
    """Test linear interpolation is correctly identified"""
    hook, cursor = mock_db_hook
    
    # Create a row with linear interpolation (input_datasource=1)
    row_with_linear = RowTuple(
        meas_id=200,
        calc_tag='LinearCalc',
        input_label='input1',
        comment='Linear interpolation',
        input_id=201,
        constant_number=None,
        constant_string=None,
        input_datasource=1,
        expression='a+b',
        template_id=1,
        input_tag='InputTag1',
        datasource=0,
        poll_period='5min',
        data_type='float'
    )
    
    # Configure the mock
    cursor.fetchall.return_value = [tuple(row_with_linear)]
    
    # Test the function
    calc_nodes, meas_nodes, calc_layers = get_calcs_graph(hook, [200])
    
    # Assertions
    assert calc_nodes[200]['inputs']['input1']['interpolate'] == 'linear'

def test_get_calcs_graph_missing_calculation(mock_db_hook):
    """Test error handling when calculation is not found"""
    hook, cursor = mock_db_hook
    
    # Configure the mock to return empty result
    cursor.fetchall.return_value = []
    
    # Test the function should raise ValueError
    with pytest.raises(ValueError) as excinfo:
        get_calcs_graph(hook, [999])
    
    assert "Calculations not found in database" in str(excinfo.value)

def test_get_calcs_graph_sqlalchemy_missing_table():
    """Test error handling when table is not provided with SQLAlchemy engine"""
    engine = MagicMock(spec=Engine)
    
    # Test the function should raise ValueError
    with pytest.raises(ValueError) as excinfo:
        get_calcs_graph(engine, [100])
    
    assert "Table parameter is required" in str(excinfo.value)

def test_get_calcs_graph_string_constant(mock_db_hook):
    """Test handling of string constants in calculation inputs"""
    hook, cursor = mock_db_hook
    
    # Create a row with string constant
    row_with_string = RowTuple(
        meas_id=300,
        calc_tag='StringConstCalc',
        input_label='input1',
        comment='String constant',
        input_id=None,
        constant_number=None,
        constant_string='test_value',
        input_datasource=None,
        expression='a+"string"',
        template_id=1,
        input_tag=None,
        datasource=None,
        poll_period='5min',
        data_type='string'
    )
    
    # Configure the mock
    cursor.fetchall.return_value = [tuple(row_with_string)]
    
    # Test the function
    calc_nodes, meas_nodes, calc_layers = get_calcs_graph(hook, [300])
    
    # Assertions
    assert calc_nodes[300]['inputs']['input1']['constant'] == 'test_value'
    assert calc_nodes[300]['data_type'] == 'string'