import unittest
from unittest.mock import patch, Mock
import sys
from typing import Any
import sqlalchemy

class TestCalcUtilsImportHandling(unittest.TestCase):
    def setUp(self):
        """Clear any existing SQLAlchemy imports before each test"""
        for mod in list(sys.modules.keys()):
            if mod.startswith('sqlalchemy'):
                del sys.modules[mod]

    def test_successful_sqlalchemy_import(self):
        """Test that SQLAlchemy Engine is properly imported when available"""
        # Create mock module with Engine attribute
        mock_sqlalchemy = Mock()
        mock_engine = Mock(name='Engine')
        mock_sqlalchemy.Engine = mock_engine
        
        # Replace sqlalchemy module in sys.modules
        with patch.dict('sys.modules', {'sqlalchemy': mock_sqlalchemy}):
            import importlib
            import calc_utils_updated
            importlib.reload(calc_utils_updated)
            self.assertIs(calc_utils_updated.Engine, mock_engine)

    def test_sqlalchemy_import_failure(self):
        """Test fallback to Any type when SQLAlchemy Engine import fails"""
        # Create mock module that raises ImportError when accessing Engine
        mock_sqlalchemy = Mock()
        
        def raise_import_error(*args):
            raise ImportError("Cannot import Engine")
            
        type(mock_sqlalchemy).Engine = property(raise_import_error)
        mock_sqlalchemy.Table = Mock()
        mock_sqlalchemy.select = Mock()
        mock_sqlalchemy.and_ = Mock()
        
        # Replace sqlalchemy module in sys.modules
        with patch.dict('sys.modules', {'sqlalchemy': mock_sqlalchemy}):
            import importlib
            import calc_utils_updated
            importlib.reload(calc_utils_updated)
            from calc_utils_updated import Engine
            self.assertIs(Engine, Any)

    def test_other_sqlalchemy_imports_preserved(self):
        """Test that other SQLAlchemy imports are still available after Engine import failure"""
        # Create mock module without Engine but with other components
        mock_sqlalchemy = Mock()
        mock_sqlalchemy.Engine = None
        mock_sqlalchemy.Table = Mock(name='Table')
        mock_sqlalchemy.select = Mock(name='select')
        mock_sqlalchemy.and_ = Mock(name='and_')
        
        # Replace sqlalchemy module in sys.modules
        with patch.dict('sys.modules', {'sqlalchemy': mock_sqlalchemy}):
            
            import importlib
            import calc_utils_updated
            importlib.reload(calc_utils_updated)

            # Verify we can still use other SQLAlchemy components
            self.assertTrue(hasattr(calc_utils_updated, 'Table'))
            self.assertTrue(hasattr(calc_utils_updated, 'select'))
            self.assertTrue(hasattr(calc_utils_updated, 'and_'))

if __name__ == '__main__':
    unittest.main()