import asyncio
import datetime

from .asyncevent import *
from .asynceventtasks import *
from typing import Dict,Tuple
import requests
from enum import Enum

class AggregatePeriod(str,Enum):
    _1M = '1min'
    _2M = '2min'
    _5M = '5min'
    _10M = '10min'
    _15M = '15min'
    _20M = '20min'
    _30M = '30min'
    _1H = '1hr'
    _2H = '2hr'
    _4H = '4hr'
    _6H = '6hr'
    _8H = '8hr'
    _12H = '12hr'
    DAILY = 'DAILY'
    WEEKLY = 'WEEKLY'
    _30d = '30day'
    MONTHLY = 'MONTHLY'

class Aggregate(str,Enum):
    NONE = 'none'
    TWA = 'twa'
    MAX = 'max'
    MIN = 'min'
    AVG = 'avg'
    STD = 'std.p'
    RATETOTAL= 'ratetotal' # Will check if the measurements are rates and compute the twa*factor where factor=AggregatePeriod/ratedivisor
        # Example: aggperiod is 1 week, rate  is m3/hr --> divisor is 1 hr (60000)
    DELTATWA= 'deltatwa'
    DELTAAVG= 'deltaavg'
    DELTAMAX= 'deltamax'
    DELTAMIN= 'deltamin'
    DELTASTD='deltastd'
class BucketSize(Enum):
    _1M = 60 * 1000
    _2M = 2 * 60 * 1000
    _5M = 5 * 60 * 1000
    _10M = 10 * 60 * 1000
    _15M = 15 * 60 * 1000
    _20M = 20 * 60 * 1000
    _30M = 30 * 60 * 1000
    _1H = 60 * 60 * 1000
    _2H = 2 * 60 * 60 * 1000
    _4H = 4 * 60 * 60 * 1000
    _6H = 6 * 60 * 60 * 1000
    _8H = 8 * 60 * 60 * 1000
    _12H = 12 * 60 * 60 * 1000
    DAILY = 24 * 60 * 60 * 1000
    WEEKLY = 7 * 24 * 60 * 60 * 1000
    _30d = 30 * 24 * 60 * 60 * 1000
    MONTHLY=31 * 24 * 60 * 60 * 1000 # will aggregate daily from Aggregator and apply month groupby locally

BucketSize_Dict={}
for size in BucketSize:
    BucketSize_Dict[size.name] = BucketSize(size.value)

# Task is given a list of meas_ids to read requested agg and period
# Reads at least the largest of now-MINSPAN minutes or now-2 periods and uses the last value given
# Agg can also be none in which case it reads now-MINSPAN
# measurements are AssetPath -> {Tag: id}
# api = url to api
MINSPAN=300000  # in milliseconds
class AsyncTSDBReaderTask(AsyncTaskWithContextualEvents):
    def __init__(self,api:str,customer:int,measurements:Dict[Tuple[str,int],List[Tuple[str,int]]],aggregate:Aggregate,period:AggregatePeriod,completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        self.api=api
        self.customer=customer
        self.measurements=measurements
        self.aggregate=aggregate
        self.period=period
        self.__data_read__=asyncio.Event()
        # Create contexts (i.e. the measurement ids
        self.__event_descriptive_contexts__={}  # meas_id->{asset:,tag:,asset_id:}
        event_contexts=[] # meas_ids
        for asset,meas_list in measurements.items():
            for meas in meas_list:
                meas_id=meas[1]
                event_contexts.append(meas_id)
                self.__event_descriptive_contexts__[meas_id]={"asset":{"id":asset[1], "tag":asset[0]}, "measurement":{"id":meas_id, "tag":meas[0]}}
        super().__init__(event_contexts=event_contexts,completion_callback=completion_callback,timeout=timeout,persist_obj=persist_obj)

    @property
    def api(self):
        return self.__api__

    @api.setter
    def api(self, api):
        self.__api__ = api

    @property
    def customer(self):
        return self.__customer__

    @customer.setter
    def customer(self,customer:int):
        self.__customer__=customer

    @property
    def measurements(self):
        return self.__measurements__

    @measurements.setter
    def measurements(self,measurements:Dict[Tuple[str,int],List[Tuple[str,int]]]):
        self.__measurements__=measurements
        ids=[]
        for _,meas in self.__measurements__.items():
            for tup in meas:
                ids.append(tup[1])
        self.__meas_ids__=",".join([str(id) for id in ids])

    @property
    def aggregate(self):
        return self.__aggregate__

    @aggregate.setter
    def aggregate(self,aggregate:Aggregate):
        self.__aggregate__=aggregate

    @property
    def period(self):
        return self.__period__

    @period.setter
    def period(self,period:AggregatePeriod):
        self.__period__=period

    # overriden execute method
    async def __execute__(self, state:EventState, *args, **kwargs):
        end=round(datetime.now().timestamp()*1000)
        start = end - MINSPAN
        # call the api
        if(self.__aggregate__==Aggregate.NONE):
            url=f"{self.api}/timeseries/history/{self.customer}?meas_id={self.__meas_ids__}&start={start}&end={end}&asset_tz=false"
        else:
            start=max(start,end-BucketSize_Dict[self.period.name].value)
            url=f"{self.api}/timeseries/agg/{self.customer}?meas_id={self.__meas_ids__}&start={start}&end={end}&asset_tz=false"
        r=requests.get(url)
        if (r.status_code == 200):
            obj=r.json()
            ret=[]
            # Parallel executions
            parallel_asyncs=[]
            for meas_obj in obj:
                context=(int(meas_obj['tag']))
                if('ts,val' in meas_obj):
                    output_state=EventState()
                    output_state.context=context
                    output_state.source=self.__event_descriptive_contexts__[context]
                    if(meas_obj['ts,val']):
                        # Only send last value
                        tv=meas_obj['ts,val'][-1]
                        output_state.timestamp=tv[0]/1000
                        output_state.state=tv[1]
                    else:
                        output_state.timestamp=datetime.now().timestamp()
                        output_state.state=None
                    parallel_asyncs.append(self.__trigger_event__(context, output_state))  # sends data as eventstate object
                # raise exception for on_error listeners
                elif('error' in meas_obj):
                    exception=Exception(f"Exception:\n{meas_obj['error']}\n ... while retrieving values for {self.__event_descriptive_contexts__[context]}")
                    parallel_asyncs.append(self.__trigger_on_error__(context,exception))
                ret.append(meas_obj)
            await asyncio.gather(*parallel_asyncs,return_exceptions=False)
        else:
            exception=Exception(f"Error retrieving data with url {url}: {r.text}")
            # for context,event in self.__events__.items():
            #     await self.__trigger_on_error__(context,exception)
            raise exception
        return ret


# Read values from api and passes them to the corresponding LimitCheckTask Property
# Task,Property,MeasId
class AsyncLimitsCheckerSettingsTask(AsyncTask):
    def __init__(self,api:str,customer:int,settings:Dict[AsyncLimitCheckerTask,List[Tuple[LimitSetting,int]]],completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        self.api=api
        self.customer=customer
        self.settings=settings
        self.__prev_values__={} # to track limit changes
        self.__data_read__=asyncio.Event()
        # Create contexts (i.e. the measurement ids
        self.__event_descriptive_contexts__={}  # limittask ->(limit,meas_id)
        self.__task_index__={}  # measid --> {task:limitsetting}
        for task,settings_list in settings.items():
            for setting in settings_list:
                meas_id=setting[1]
                limitSetting=setting[0]
                self.__event_descriptive_contexts__[meas_id]={"limit":{"context":task.state,"setting":limitSetting,"measurement":meas_id}}
                self.__task_index__[meas_id]=(task,limitSetting)
        super().__init__(completion_callback=completion_callback,timeout=timeout,persist_obj=persist_obj)

    @property
    def api(self):
        return self.__api__

    @api.setter
    def api(self, api):
        self.__api__ = api

    @property
    def customer(self):
        return self.__customer__

    @customer.setter
    def customer(self,customer:int):
        self.__customer__=customer

    @property
    def settings(self):
        return self.__settings__

    @settings.setter
    def settings(self,settings:Dict[AsyncLimitCheckerTask,List[Tuple[LimitSetting,int]]]):
        self.__settings__=settings
        ids=[]
        for _,s in self.__settings__.items():
            for tup in s:
                ids.append(tup[1])
        self.__meas_ids__=",".join([str(id) for id in ids])

    # overriden execute method
    async def __execute__(self, state:EventState, *args, **kwargs):
        # call the api
        url=f"{self.api}/timeseries/current/{self.customer}?meas_id={self.__meas_ids__}"
        r=requests.get(url)
        now=datetime.datetime.now().timestamp() # Use now as timestamp
        if (r.status_code == 200):
            obj=r.json()
            ret=[] # list of context:result or exception
            for meas_obj in obj:
                context=(int(meas_obj['tag']))
                if(not 'error' in meas_obj['val']):
                    output_state=EventState()
                    output_state.context=context
                    output_state.timestamp=now
                    output_state.source=self.__event_descriptive_contexts__[context]
                    output_state.state = meas_obj['val']
                    if (not context in self.__prev_values__ or meas_obj['val']!=self.__prev_values__[context]):
                        # Set limit
                        limitTask_setting=self.__task_index__[context]
                        limitTask=limitTask_setting[0]
                        limitSetting=limitTask_setting[1]
                        if(limitSetting==LimitSetting.LIMIT):
                            limitTask.limit=output_state.state
                        elif(limitSetting==LimitSetting.DEADBAND and isinstance(limitTask,AsyncLimitCheckerWithResetTask)):
                            limitTask.deadband=output_state.state
                        else:
                            # Return exception for unfamiliar setting
                            ret.append(Exception(
                                f"Exception:\nUnknown setting for limit task\n{self.__event_descriptive_contexts__[context]}"))
                        # Update previous value tracking
                        self.__prev_values__[context]=output_state.state
                # Return exception for the limit setting
                else:
                    ret.append(Exception(f"Exception:\n{meas_obj['error']}\n ... while retrieving values for {self.__event_descriptive_contexts__[context]}"))
        else:
            exception=Exception(f"Error retrieving data with url {url}: {r.text}")
            raise exception
        await asyncio.sleep(0) # To make this look like async
        return ret
