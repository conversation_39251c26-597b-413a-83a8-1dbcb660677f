services:
  redis:
    image: redis:latest
    container_name: redis
    networks:
      - custom_network
    ports:
      - "6379:6379"

  api:
    build:
      context: ..
      dockerfile: Dockerfile
    container_name: fastapi
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000
    ports:
      - "8000:8000"
    depends_on:
      - redis
      - celery_beat
    networks:
      - custom_network
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379

  celery_worker:
    build:
      context: ..
      dockerfile: Dockerfile
    container_name: celery_worker
    command: celery -A app.celery_app worker --loglevel=info
    depends_on:
      - redis
      - celery_beat
      - api
    networks:
      - custom_network
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379

  celery_beat:
    build:
      context: ..
      dockerfile: Dockerfile
    container_name: celery_beat
    command: celery -A app.celery_app beat --loglevel=info
    depends_on:
      - redis
    networks:
      - custom_network
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379

networks:
  custom_network:
    driver: bridge