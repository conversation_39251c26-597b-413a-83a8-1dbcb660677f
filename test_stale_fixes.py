#!/usr/bin/env python3
"""
Test script to verify stale alert fixes work correctly.
This script tests the fixed StaleCheckerService with various scenarios.
"""

import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Mock the Redis module before importing
sys.modules['app.redis'] = MagicMock()
sys.modules['BromptonPythonUtilities.Redis_Connection_Utility.tsdb_connection'] = MagicMock()
sys.modules['BromptonPythonUtilities.Logging_Utility.logging_config'] = MagicMock()

# Mock the setup_logging function
mock_logger = MagicMock()
sys.modules['BromptonPythonUtilities.Logging_Utility.logging_config'].setup_logging.return_value = mock_logger

from app.StaleCheckerService import check_stale_measurement
from app.enums import CompareOperation, LimitState

def test_basic_stale_detection():
    """Test basic stale detection functionality."""
    print("=== Testing Basic Stale Detection ===")
    
    with patch('app.StaleCheckerService.master') as mock_redis:
        # Mock Redis to return no previous values (first run)
        mock_redis.get.return_value = None
        
        now = datetime.now()
        measurement_id = 1001
        alert_id = 2001
        
        # Test 1: Fresh value should be NORMAL
        print("Test 1: Fresh value")
        result = check_stale_measurement(
            alert_id=alert_id,
            measurement_id=measurement_id,
            now=now,
            last_changed=None,  # Let service manage this
            stale_duration_minutes=15,
            aggregate="avg",
            period="5min",
            asset_id=1,
            comparator_id=3,
            comparison_op=CompareOperation.GT,
            input_val=10.0
        )
        print(f"  Result: {result.state} (expected: NORMAL)")
        assert result.state == "NORMAL", f"Expected NORMAL, got {result.state}"
        
        # Test 2: No data should be STALE
        print("Test 2: No data")
        result = check_stale_measurement(
            alert_id=alert_id + 1,
            measurement_id=measurement_id + 1,
            now=now,
            last_changed=None,
            stale_duration_minutes=15,
            aggregate="avg",
            period="5min",
            asset_id=1,
            comparator_id=3,
            comparison_op=CompareOperation.GT,
            input_val=None  # No data
        )
        print(f"  Result: {result.state} (expected: STALE)")
        assert result.state == "STALE", f"Expected STALE, got {result.state}"
        
        print("✅ Basic stale detection tests passed!")

def test_time_based_staleness():
    """Test time-based staleness detection."""
    print("\n=== Testing Time-Based Staleness ===")
    
    with patch('app.StaleCheckerService.master') as mock_redis:
        now = datetime.now()
        old_time = now - timedelta(minutes=20)  # 20 minutes ago
        measurement_id = 1002
        alert_id = 2002
        
        # Mock Redis to return previous value and old timestamp
        def mock_get(key):
            if "last_value" in key:
                return "10.0"  # Same value as current
            elif "last_changed" in key:
                return str(old_time.timestamp())  # 20 minutes ago
            return None
        
        mock_redis.get.side_effect = mock_get
        
        # Test: Same value after 20 minutes with 15-minute threshold should be STALE
        print("Test: Same value after threshold time")
        result = check_stale_measurement(
            alert_id=alert_id,
            measurement_id=measurement_id,
            now=now,
            last_changed=None,  # Let service get from Redis
            stale_duration_minutes=15,
            aggregate="avg",
            period="5min",
            asset_id=1,
            comparator_id=3,
            comparison_op=CompareOperation.GT,
            input_val=10.0  # Same value
        )
        print(f"  Result: {result.state} (expected: STALE)")
        assert result.state == "STALE", f"Expected STALE, got {result.state}"
        
        print("✅ Time-based staleness tests passed!")

def test_value_change_recovery():
    """Test that value changes reset stale state."""
    print("\n=== Testing Value Change Recovery ===")
    
    with patch('app.StaleCheckerService.master') as mock_redis:
        now = datetime.now()
        old_time = now - timedelta(minutes=20)  # 20 minutes ago
        measurement_id = 1003
        alert_id = 2003
        
        # Mock Redis to return different previous value
        def mock_get(key):
            if "last_value" in key:
                return "10.0"  # Previous value
            elif "last_changed" in key:
                return str(old_time.timestamp())  # 20 minutes ago
            return None
        
        mock_redis.get.side_effect = mock_get
        
        # Test: Different value should be NORMAL even if time threshold exceeded
        print("Test: Value change should reset to NORMAL")
        result = check_stale_measurement(
            alert_id=alert_id,
            measurement_id=measurement_id,
            now=now,
            last_changed=None,  # Let service get from Redis
            stale_duration_minutes=15,
            aggregate="avg",
            period="5min",
            asset_id=1,
            comparator_id=3,
            comparison_op=CompareOperation.GT,
            input_val=15.0  # Different value
        )
        print(f"  Result: {result.state} (expected: NORMAL)")
        assert result.state == "NORMAL", f"Expected NORMAL, got {result.state}"
        
        print("✅ Value change recovery tests passed!")

def test_stale_band_detection():
    """Test stale band detection functionality."""
    print("\n=== Testing Stale Band Detection ===")
    
    with patch('app.StaleCheckerService.master') as mock_redis:
        now = datetime.now()
        old_time = now - timedelta(minutes=20)  # 20 minutes ago
        measurement_id = 1004
        alert_id = 2004
        
        # Mock Redis for band detection
        def mock_get(key):
            if "initial_value" in key:
                return "10.0"  # Initial reference value
            elif "last_changed" in key:
                return str(old_time.timestamp())  # 20 minutes ago
            return None
        
        mock_redis.get.side_effect = mock_get
        
        # Test 1: Value within band for too long should be STALE
        print("Test 1: Value within band for too long")
        result = check_stale_measurement(
            alert_id=alert_id,
            measurement_id=measurement_id,
            now=now,
            last_changed=None,
            stale_duration_minutes=15,
            aggregate="avg",
            period="5min",
            asset_id=1,
            comparator_id=3,
            comparison_op=CompareOperation.GT,
            input_val=10.2,  # Within ±0.5 band of 10.0
            stale_band=0.5
        )
        print(f"  Result: {result.state} (expected: STALE)")
        assert result.state == "STALE", f"Expected STALE, got {result.state}"
        
        # Test 2: Value outside band should be NORMAL
        print("Test 2: Value outside band should be NORMAL")
        result = check_stale_measurement(
            alert_id=alert_id + 1,
            measurement_id=measurement_id + 1,
            now=now,
            last_changed=None,
            stale_duration_minutes=15,
            aggregate="avg",
            period="5min",
            asset_id=1,
            comparator_id=3,
            comparison_op=CompareOperation.GT,
            input_val=11.0,  # Outside ±0.5 band of 10.0
            stale_band=0.5
        )
        print(f"  Result: {result.state} (expected: NORMAL)")
        assert result.state == "NORMAL", f"Expected NORMAL, got {result.state}"
        
        print("✅ Stale band detection tests passed!")

def test_comparison_operations():
    """Test different comparison operations (GT vs GE)."""
    print("\n=== Testing Comparison Operations ===")
    
    with patch('app.StaleCheckerService.master') as mock_redis:
        now = datetime.now()
        # Exactly 15 minutes ago
        exact_time = now - timedelta(minutes=15)
        measurement_id = 1005
        alert_id = 2005
        
        def mock_get(key):
            if "last_value" in key:
                return "10.0"
            elif "last_changed" in key:
                return str(exact_time.timestamp())
            return None
        
        mock_redis.get.side_effect = mock_get
        
        # Test GT: Exactly 15 minutes should be NORMAL (not > 15)
        print("Test GT: Exactly at threshold")
        result = check_stale_measurement(
            alert_id=alert_id,
            measurement_id=measurement_id,
            now=now,
            last_changed=None,
            stale_duration_minutes=15,
            aggregate="avg",
            period="5min",
            asset_id=1,
            comparator_id=3,
            comparison_op=CompareOperation.GT,
            input_val=10.0
        )
        print(f"  Result: {result.state} (expected: NORMAL)")
        assert result.state == "NORMAL", f"Expected NORMAL with GT, got {result.state}"
        
        # Test GE: Exactly 15 minutes should be STALE (>= 15)
        print("Test GE: Exactly at threshold")
        result = check_stale_measurement(
            alert_id=alert_id + 1,
            measurement_id=measurement_id + 1,
            now=now,
            last_changed=None,
            stale_duration_minutes=15,
            aggregate="avg",
            period="5min",
            asset_id=1,
            comparator_id=4,
            comparison_op=CompareOperation.GE,
            input_val=10.0
        )
        print(f"  Result: {result.state} (expected: STALE)")
        assert result.state == "STALE", f"Expected STALE with GE, got {result.state}"
        
        print("✅ Comparison operations tests passed!")

def main():
    """Run all tests."""
    print("🧪 Testing Fixed Stale Alert Implementation")
    print("=" * 50)
    
    try:
        test_basic_stale_detection()
        test_time_based_staleness()
        test_value_change_recovery()
        test_stale_band_detection()
        test_comparison_operations()
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED! Stale alert fixes are working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
