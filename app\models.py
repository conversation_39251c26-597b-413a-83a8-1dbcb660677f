from typing import List, Optional

from pydantic import BaseModel

from app.enums import Aggregate, AggregatePeriod

class CreateAlertTask(BaseModel):
    customer_id: int
    alert_id: int
    aggregate: Aggregate
    aggregate_period: Optional[AggregatePeriod] = None

    class Config:
        json_schema_extra = {
            "example": {
                "customer_id": 8,
                "alert_id": 83,
                "aggregate": Aggregate.TWA,
                "aggregate_period": AggregatePeriod._15M,
            }
        }


class UpdateAlertTask(BaseModel):
    customer_id: int
    alert_id: int
    previous_aggregate: Aggregate
    previous_aggregate_period: Optional[AggregatePeriod] = None
    aggregate: Aggregate
    aggregate_period: Optional[AggregatePeriod] = None

    class Config:
        json_schema_extra = {
            "example": {
                "customer_id": 8,
                "alert_id": 83,
                "aggregate": Aggregate.TWA,
                "previous_aggregate": Aggregate.TWA,
                "aggregate_period": AggregatePeriod._15M,
                "previous_aggregate_period": AggregatePeriod._15M,
            }
        }

class DeleteAlertTask(BaseModel):
    customer_id: int
    alert_id: int
    aggregate: Aggregate
    aggregate_period: Optional[AggregatePeriod] = None

    class Config:
        json_schema_extra = {
            "example": {
                "customer_id": 8,
                "alert_id": 83,
                "aggregate": Aggregate.TWA,
                "aggregate_period": AggregatePeriod._15M,
            }
        }

class EvaluateAlertTask(BaseModel):
    customer_id: int
    aggregate: Aggregate
    aggregate_period: Optional[AggregatePeriod] = None
    alert_ids: List[int]