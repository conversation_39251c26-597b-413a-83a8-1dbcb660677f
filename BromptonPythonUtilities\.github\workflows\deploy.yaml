name: Deployment

on:
  pull_request:
    types: [opened, reopened, edited, synchronize]

jobs:
  test:
    name: Run TSDB Utility Tests
    runs-on: on-prem

    services:
      redis:
        image: redis/redis-stack-server:7.4.0-v0
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install dependencies and Run Test Cases
        run: |
          python -m venv .venv
          source .venv/bin/activate
          pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-html pytest-asyncio pytest-mock testcontainers docker
          export PYTHONPATH=$PWD
          pytest TSDB_Utility/tests \
            --cov=TSDB_Utility \
            --cov-report=html \
            --cov-report=term \
            --html=pytest-report.html \
            --self-contained-html \
            --junitxml=pytest-report.xml

      - name: Upload Pytest HTML Report
        uses: actions/upload-artifact@v4
        with:
            name: pytest-report
            path: pytest-report.html
    
      - name: Upload Coverage Report
        uses: actions/upload-artifact@v4
        with:
            name: coverage-html
            path: htmlcov

      - name: Publish Test Results to PR
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always()
        with:
            files: pytest-report.xml
            comment_mode: always
            check_name: TSDB Utility Test Results

      - name: Ensure report is in workspace root
        run: |
            if [ -f TSDB_Utility/tests/pytest-report.xml ]; then
                cp TSDB_Utility/tests/pytest-report.xml .
            fi

      - name: List Workspace Contents
        run: |
            echo "Current directory: $(pwd)"
            echo "Listing files:"
            ls -lah
          
      - name: Get current time (IST)
        id: current_time
        run: echo "time=$(TZ=Asia/Kolkata date)" >> $GITHUB_OUTPUT
        
      - name: Set shared variables
        run: |
            echo "GH_SHA=${GITHUB_SHA}" >> $GITHUB_ENV
            echo "GH_REPO=${{ github.repository }}" >> $GITHUB_ENV
            echo "GH_RUN_ID=${{ github.run_id }}" >> $GITHUB_ENV
            echo "GH_ACTOR=${GITHUB_ACTOR}" >> $GITHUB_ENV
            echo "ENVIRONMENT=${{ github.event.inputs.environment || 'N/A' }}" >> $GITHUB_ENV
            echo "BRANCH_NAME=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_ENV

        # === Step for PR-based test summary ===
      - name: Send PR Test Summary to Teams
        if: ${{ github.event_name == 'pull_request' }}
        env:
            TEAMS_WEBHOOK_URL: ${{ secrets.GH_TEAMS_INTEGRATION }}
        run: |
            python3 - <<'EOF'
            import xml.etree.ElementTree as ET
            import json, os

            xml_path = "pytest-report.xml"
            if not os.path.exists(xml_path):
                raise FileNotFoundError(f"{xml_path} not found.")

            tree = ET.parse(xml_path)
            suite = tree.getroot()
            if suite.tag != "testsuite":
                suite = suite.find("testsuite")

            tests = int(suite.attrib.get("tests", 0))
            errors = int(suite.attrib.get("errors", 0))
            failures = int(suite.attrib.get("failures", 0))
            skipped = int(suite.attrib.get("skipped", 0))
            passed = tests - errors - failures - skipped

            summary = f"""
            **Total:** {tests}  
            ✅ **Passed:** {passed}  
            ❌ **Failed:** {failures}  
            ⚠️ **Errors:** {errors}  
            🚫 **Skipped:** {skipped}
            """

            card = {
                "@type": "MessageCard",
                "@context": "http://schema.org/extensions",
                "summary": "PR Pytest Results",
                "themeColor": "0076D7",
                "title": "📦 PR Test Summary",
                "sections": [{
                    "activityTitle": "Pull Request Test Results",
                    "text": summary
                }],
                "potentialAction": [{
                    "@type": "OpenUri",
                    "name": "View Workflow",
                    "targets": [{
                        "os": "default",
                        "uri": "https://github.com/${GH_REPO}/actions/runs/${GH_RUN_ID}"
                    }]
                }]
            }

            with open("summary.json", "w") as f:
                json.dump(card, f, indent=2)
            EOF

            curl -H "Content-Type: application/json" \
                -d @summary.json \
                "$TEAMS_WEBHOOK_URL"

        # === Step for deploy success ===
      - name: Send Teams Deploy Success Card
        if: ${{ github.event_name != 'pull_request' && needs.build.result == 'success' && needs.deploy.result == 'success' }}
        run: |
            echo "Sending deploy success card..."
            cat <<EOF > card.json
            {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "summary": "Deployment Success",
            "themeColor": "00FF00",
            "title": "✅ Deployment Succeeded",
            "sections": [{
                "facts": [
                { "name": "Branch", "value": "${BRANCH_NAME}" },
                { "name": "Environment", "value": "${ENVIRONMENT}" },
                { "name": "Author", "value": "${GH_ACTOR}" },
                { "name": "Commit", "value": "[${GH_SHA}](https://github.com/${GH_REPO}/commit/${GH_SHA})" }
                ]
            }],
            "potentialAction": [{
                "@type": "OpenUri",
                "name": "View Workflow Run",
                "targets": [{
                "os": "default",
                "uri": "https://github.com/${GH_REPO}/actions/runs/${GH_RUN_ID}"
                }]
            }]
            }
            EOF

            curl -H "Content-Type: application/json" \
                -d @card.json \
                "${{ secrets.GH_TEAMS_INTEGRATION }}"

        # === Step for deploy failure ===
      - name: Send Teams Deploy Failure Card
        if: ${{ github.event_name != 'pull_request' && (needs.build.result == 'failure' || needs.deploy.result == 'failure') }}
        run: |
            echo "Sending deploy failure card..."
            cat <<EOF > card.json
            {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "summary": "Deployment Failed",
            "themeColor": "FF0000",
            "title": "❌ Deployment Failed",
            "sections": [{
                "facts": [
                { "name": "Branch", "value": "${BRANCH_NAME}" },
                { "name": "Environment", "value": "${ENVIRONMENT}" },
                { "name": "Author", "value": "${GH_ACTOR}" },
                { "name": "Commit", "value": "[${GH_SHA}](https://github.com/${GH_REPO}/commit/${GH_SHA})" }
                ]
            }],
            "potentialAction": [{
                "@type": "OpenUri",
                "name": "View Workflow Run",
                "targets": [{
                "os": "default",
                "uri": "https://github.com/${GH_REPO}/actions/runs/${GH_RUN_ID}"
                }]
            }]
            }
            EOF

            curl -H "Content-Type: application/json" \
                -d @card.json \
                "${{ secrets.GH_TEAMS_INTEGRATION }}"