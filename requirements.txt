aiohappyeyeballs==2.3.4
aiohttp==3.10.0
aiohttp-retry==2.8.3
aiosignal==1.3.1
amqp==5.2.0
annotated-types==0.7.0
anyio==4.4.0
attrs==24.1.0
billiard==4.2.0
celery==5.4.0
celery-redbeat==2.2.0
certifi==2024.7.4
charset-normalizer==3.3.2
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
fastapi==0.112.0
flower==2.0.1
frozenlist==1.4.1
h11==0.14.0
httptools==0.6.1
humanize==4.10.0
idna==3.7
kombu==5.3.7
multidict==6.0.5
numpy==2.0.1
paho-mqtt==2.1.0
pandas==2.2.2
prometheus_client==0.20.0
prompt_toolkit==3.0.47
psycopg2-binary==2.9.9
pydantic==2.8.2
pydantic_core==2.20.1
PyJWT==2.9.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-http-client==3.3.7
pytz==2024.1
PyYAML==6.0.1
redis==5.0.8
requests==2.32.3
sendgrid==6.11.0
six==1.16.0
sniffio==1.3.1
SQLAlchemy==2.0.32
starkbank-ecdsa==2.2.0
starlette==0.37.2
tenacity==9.0.0
tornado==6.4.1
twilio==9.2.3
typing_extensions==4.12.2
tzdata==2024.1
urllib3==2.2.2
uvicorn==0.30.5
# uvloop==0.19.0
vine==5.1.0
watchfiles==0.22.0
wcwidth==0.2.13
websockets==12.0
yarl==1.9.4
confluent-kafka
asgiref~=3.8.1
prometheus-fastapi-instrumentator==5.9.0
prometheus-client==0.20.0
python-json-logger==3.2.0
opentelemetry-api
opentelemetry-sdk
# Test dependencies
pytest==8.0.0
pytest-asyncio==0.23.5
pytest-cov==4.1.0
pytest-mock==3.12.0
freezegun==1.4.0  # For time-based testing
pika==1.3.2
httpx==0.28.1
aioresponses==0.7.8
js2py==0.74
docker==7.1.0
pendulum
testcontainers==4.10.0