import asyncio,concurrent.futures
from datetime import datetime
import inspect
from pydantic import BaseModel
from typing import Set,List,Any
from uuid import *
import logging
from queue import Queue
import logging.handlers
import os
from typing import Tuple

class PersistentObject(BaseModel):
    id:UUID
    description:str=None
    createtime:float # unix seconds
    create_user:str # userid
    updatetime:float=None # unix seconds
    update_user:str=None # userid
    deletetime:float=None # unix seconds
    delete_user:str=None

# Placeholder for any information to be recorded with the event
class EventState(BaseModel):
    timestamp:float=None  # unix seconds
    context:object=None # defines the context of the event, for example the asset or measurement, etc.
    state:object=None
    source:PersistentObject=None  # anything that provides a reference to source

# TODO: this is an aspect that needs to be implemented in a library
class LogHandler:
    logger:logging.Logger
    listener:logging.handlers.QueueListener

    @classmethod
    def configure_logging(cls,logroot=None, format='%(asctime)s:%(levelname)s:%(message)s', daystokeep=7, level=logging.DEBUG):
        name = os.path.basename(__file__)
        cls.logger = logging.getLogger(name[:-3])  # only the file name minus .py
        cls.logger.setLevel(level)
        formatter = logging.Formatter(format)
        handlers_list=[]
        if(not logroot is None):
            logfile = name[:-3]
            if (not (os.path.exists(logroot))):
                os.makedirs(logroot)
            fileHandler = logging.handlers.TimedRotatingFileHandler("{0}/{1}.log".format(logroot, logfile), when='D',
                                                                    backupCount=10, interval=daystokeep)
            fileHandler.setFormatter(formatter)
            handlers_list.append(fileHandler)
        #    consoleHandler=logging.StreamHandler(sys.stdout)
        consoleHandler = logging.StreamHandler()
        consoleHandler.setFormatter(formatter)
        handlers_list.append(consoleHandler)
        # create que
        que=Queue(-1)
        cls.logger.addHandler(logging.handlers.QueueHandler(que))
        cls.listener=logging.handlers.QueueListener(que,*handlers_list)
        cls.listener.start()

    @classmethod
    def stop_logging(cls): # call if you wish to flush queue before sys.exit()
        cls.logger.debug("Stopping logging")
        cls.listener.stop()

# Wrapper for callback including timeout
async def wrapper_w_timeout(callback, timeout: float):
    try:
        result = await asyncio.wait_for(callback, timeout=timeout)
    except asyncio.TimeoutError:
        result = Exception("Coroutine timed out")
    except Exception as e:
        result = e
    return result

MAXCALLBACKTIMEOUT=30
class AsyncTask():
    """
    A task represents an object that performs a function and may yield a result.
    Attributes:
        timeout - a float number represention the number of seconds after which the execute function is considered
                    to have failed (taken too long)
        execute - a function that takes a single parameter that will be called to execute the task
                    this function receives a single parameter "state" which used to pass information
                    to the task and which returns the result of the task.  This is an abstract function
                    which must be implemented by derived classes
        result - an object that holds the result, if any, of executing the task
    """
    def __init__(self,completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        self.__persist_obj__=persist_obj
        self.__result__=None
        self.callback=completion_callback
        self.timeout=timeout
        self.__logger__=logging.getLogger("asyncevent")
        self.__pool__= concurrent.futures.ThreadPoolExecutor(max_workers=2)  # in case need to execute async methods from sync calls like properties

    def __log_exception__(self,result_state:Tuple[Exception,EventState]):
        self.__logger__.exception("Exception processing task",result_state[0],exc_info=True,stack_info=True)

    def __log_task_completion__(self,result_state:Tuple[Any,EventState]):
        if(not isinstance(result_state[0],Exception)):
            self.__logger__.debug(f"Task completed with state:\n{result_state}")
        else:
            self.__log_exception__(result_state)

    @property
    def persist_obj(self):
        return self.__persist_obj__

    @property
    def callback(self):
        return self.__completion_callback__

    @callback.setter
    def callback(self, completion_callback):
        if(completion_callback):
            args=inspect.signature(completion_callback).parameters
            if not inspect.iscoroutinefunction(completion_callback) or len(args)<0:
                raise ValueError("callback must be an async couroutine with at least single parameter to receive the state")
        self.__completion_callback__=completion_callback

    @property
    def timeout(self):
        return self.__timeout__

    @timeout.setter
    def timeout(self, timeout:float):  # timeout is used by caller but owned by task
        if(timeout and timeout<=0):
             raise ValueError("timeout must be an positive float > 0")
        self.__timeout__=timeout

    @property
    def result(self):
        return self.__result__

    # Execute function
    async def execute(self, state:EventState, *args, **kwargs):
        try:
            self.__result__=await self.__execute__(state,*args,**kwargs)
            self.__log_task_completion__((self.result,state))
        except Exception as e:
            self.__result__=e
            self.__log_exception__((self.result,state))
        # Notify completion callbacks even if result is an exception
        if(self.callback):
            await wrapper_w_timeout(self.callback((self.result,state)),timeout=MAXCALLBACKTIMEOUT)
        return (self.result,state)

    # Abstract execute function
    async def __execute__(self, state:EventState, *args, **kwargs):
        raise NotImplementedError("This method is Abstract.  Must implement in derived class")

MAXONERRORTIMEOUT=30
class AsyncEvent():
    """
    Define events to be generated/raised/triggered by other objects such as schedulers
    When triggered, sends its state to one or many registered async callbacks
    Attributes:
        callbacks - a set of AsyncTasks whose execute funtion will be called when the event is triggered
        state - an object to be passed to the callback function
        timeout - number of seconds after which callback should complete
        onerrors - a set of functions that take a single parameter that will be called if there are any exceptions
        including timeout of the event callback
    """
    def __init__(self, state:EventState, persist_obj:PersistentObject=None):
        if(not(state) or not(isinstance(state, EventState))):
            raise ValueError("Event cannot be created without a state to define its context")
        self.state=state
        self.__persist_obj__=persist_obj
        self.__callbacks__:Set[AsyncTask]=set()
        self.__onerrors__={}

    @property
    def persists_obj(self):
        return self.__persist_obj__

    @property
    def state(self):
        return self.__state__

    @state.setter
    def state(self, state):
        if(not isinstance(state, EventState)):
            raise ValueError("State must be of type Event State")
        self.__state__=state

    ## Public methods
    def add_callback(self,task:AsyncTask):
        if(not task in self.__callbacks__):
            self.__callbacks__.add(task)

    def remove_callback(self,task:AsyncTask):
        if(task in self.__callbacks__):
            self.__callbacks__.remove(task)

    # This is useful for error collection, i.e. to send to observability platform
    # Add listener to errors in specific tasks or all tasks (task=None)
    def add_onerror(self,onerror,task:AsyncTask=None):
        if(not task is None and not task in self.__callbacks__):
            raise ValueError("Callback is not registered as a listener to this event")
        args=inspect.signature(onerror).parameters
        if not inspect.iscoroutinefunction(onerror) or len(args)<0:
            raise ValueError("onerror must be function with at least one parameter to receive the exception")
        # if callback is none this is a listener to all errors (errors of all tasks)
        if(task is None):
            if(not self.__callbacks__):
                raise ValueError("No callbacks defined. Define callbacks before defining their on error listeners")
            # All tasks
            for task in self.__callbacks__:
                if(not task in self.__onerrors__):
                    self.__onerrors__[task]=set()
                if (not onerror in self.__onerrors__[task]):
                    self.__onerrors__[task].add(onerror)
        else: # Othewise attach to single task
            if (not task in self.__onerrors__):
                self.__onerrors__[task] = set()
            if (not onerror in self.__onerrors__[task]):
                self.__onerrors__[task].add(onerror)

    def remove_onerror(self, onerror, task:AsyncTask=None):
        if(not task is None and not task in self.__callbacks__):
            raise ValueError("Callback is not registered as a listener to this event")
        if (task is None):
            for task in self.__callbacks__:
                if(onerror in self.__onerrors__[task]):
                    self.__onerrors__[task].remove(onerror)
        else:
            if(onerror in self.__onerrors__[task]):
                self.__onerrors__[task].remove(onerror)

    async def trigger(self, state:EventState):
        # Try all callbacks at once
        parallel_asyncs=[]
        for task in self.__callbacks__:
            if(task.timeout):
                # state can be passed on trigger or set before triggering
                parallel_asyncs.append(wrapper_w_timeout(task.execute(state if state else self.state),timeout=task.timeout))
            else:
                parallel_asyncs.append(task.execute(state if state else self.state))
        results = await asyncio.gather(*parallel_asyncs,return_exceptions=True)
        # notify listeners of error in executing tasks
        parallel_errors=[]
        for result,task in zip(results,self.__callbacks__):
            if isinstance(result,Exception) or ((isinstance(result,list) or isinstance(result,tuple)) and isinstance(result[0],Exception)):
                if (task in self.__onerrors__):
                    for onerror in self.__onerrors__[task]:
                        parallel_errors.append(wrapper_w_timeout(onerror(result), timeout=MAXONERRORTIMEOUT))
        if(parallel_errors):
            await asyncio.gather(*parallel_errors,return_exceptions=False)

    async def notify_all_on_error_listeners(self,exception:Exception):
        parallel_errors=[]
        for task in self.__onerrors__:
            for onerror in self.__onerrors__[task]:
                parallel_errors.append(wrapper_w_timeout(onerror(exception), timeout=MAXONERRORTIMEOUT))
        if (parallel_errors):
            await asyncio.gather(*parallel_errors, return_exceptions=False)

class AsyncTaskWithEvent(AsyncTask):
    def __init__(self,event_context:str,completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        super().__init__(completion_callback=completion_callback, timeout=timeout, persist_obj=persist_obj)
        evt_state=EventState()
        evt_state.context=event_context
        evt_state.source=persist_obj
        self.__event__ = AsyncEvent(evt_state)

    ## Public methods
    def add_event_callback(self, task:AsyncTask):
        self.__event__.add_callback(task)

    def remove_event_callback(self, task:AsyncTask):
        self.__event__.remove_callback(task)

    def add_event_onerror(self, onerror, task:AsyncTask=None):
        self.__event__.add_onerror(onerror,task)

    def remove_event_onerror(self, onerror, task: AsyncTask = None):
        self.__event__.remove_onerror(onerror,task)

    async def __trigger_event__(self,state):  # State can be anything
        self.__event__.state.state=state
        self.__event__.state.timestamp = datetime.now().timestamp()
        await self.__event__.trigger(state)

class AsyncTaskWithContextualEvents(AsyncTask):
    def __init__(self,event_contexts:List[Any],completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        events={}
        for event_context in event_contexts:
            evt_state=EventState()
            evt_state.context=event_context
            evt_state.source=persist_obj
            events[event_context]=AsyncEvent(evt_state)
        self.__events__ = events
        super().__init__(completion_callback=completion_callback,timeout=timeout,persist_obj=persist_obj)

    ## Public methods
    def add_event_callback(self, task:AsyncTask, event_contex:Any):
        self.__events__[event_contex].add_callback(task)

    def remove_event_callback(self, task:AsyncTask, event_contex:Any):
        self.__events__[event_contex].remove_callback(task)

    def add_event_onerror(self, onerror, task:AsyncTask=None):
        for context,event in self.__events__.items():
            self.__events__[context].add_onerror(onerror,task)

    def remove_event_onerror(self, onerror, task: AsyncTask = None):
        for context,event in self.__events__.items():
            self.__events__[context].remove_onerror(onerror,task)

    async def __trigger_event__(self,event_context:Any,state:Any):  # State can be anything
        self.__events__[event_context].state.state=state
        self.__events__[event_context].state.timestamp = datetime.now().timestamp()
        await self.__events__[event_context].trigger(state)

    async def __trigger_on_error__(self,event_context:Any,exception:Exception):  # State can be anything
        self.__events__[event_context].state.state=exception
        self.__events__[event_context].state.timestamp = datetime.now().timestamp()
        await self.__events__[event_context].notify_all_on_error_listeners(exception)

# Waste Time Task - good for testing
class AsyncWaitTimeTask(AsyncTask):
    # overrided methods
    def __init__(self,time_to_wait:float,completion_callback=None,persist_obj=None):
        self.__time_to_wait=time_to_wait
        super().__init__(completion_callback=completion_callback,persist_obj=persist_obj)

    # Overrided execute function
    async def __execute__(self,state,*args,**kwargs):
        await asyncio.sleep(self.__time_to_wait)
        return {"waited":self.__time_to_wait}

# Say Something Task - good for testing
class AsyncPrintSomethingTask(AsyncTask):
    # overrided methods
    def __init__(self, input, completion_callback=None, persist_obj:PersistentObject=None):
        self.__input__=input
        super().__init__(timeout=None,completion_callback=completion_callback,persist_obj=persist_obj)
    @property
    def input(self):
        return self.__input__
    @input.setter
    def input(self,input):
        self.__input__=input

    # Overrided execute function
    async def __execute__(self,state,*args,**kwargs):
        await asyncio.sleep(0)
        print(self.input)
        return {"printed": self.input}

# Echo Task - good for testing
class AsyncEchoTask(AsyncTask):
    # overrided methods
    def __init__(self, input, completion_callback=None, persist_obj:PersistentObject=None):
        self.__input__=input
        super().__init__(timeout=None,completion_callback=completion_callback,persist_obj=persist_obj)
    @property
    def input(self):
        return self.__input__
    @input.setter
    def input(self,input):
        self.__input__=input

    def __echo__(self):
        return self.input

    # Overrided execute function
    async def __execute__(self, state:EventState, *args, **kwargs):
        await asyncio.sleep(0)
        return self.__echo__()

class AsyncDictWrapperTask(AsyncTask):
    # overrided methods
    def __init__(self, keys:List[str],inputs:list=None, completion_callback=None, persist_obj:PersistentObject=None):
        self.keys=keys
        self.inputs=inputs
        super().__init__(timeout=None,completion_callback=completion_callback,persist_obj=persist_obj)
    @property
    def keys(self):
        return self.__keys__

    @keys.setter
    def keys(self,keys):
        if(not(keys) or not(isinstance(keys,list)) or not(isinstance(keys[0],str))):
            raise ValueError("keys must be an list of strings containing key names")
        self.__keys__=keys

    @property
    def inputs(self):
        return self.__inputs__

    @inputs.setter
    def inputs(self,inputs:list):
        if(not(inputs)):
            self.__inputs__=[None for _ in range(len(self.keys))]
        else:
            if(not(isinstance(inputs,list))):
                raise ValueError("Inputs must be a list")
            self.__inputs__=[input for i,input in enumerate(inputs) if i<len(self.__keys__)]
            if(len(self.__inputs__)<len(self.keys)):
                for i in range(len(self.__inputs__),len(self.__keys__)):
                    self.__inputs__[i]=None

    def __wrap__(self):
        wrap_dict={}
        for key,input in zip(self.keys,self.inputs):
            wrap_dict[key]=input
        return wrap_dict

    # Overrided execute function
    async def __execute__(self, state:EventState, *args, **kwargs):
        await asyncio.sleep(0)
        return self.__wrap__()

# Performs calculation given an expression and Dict of inputs
class AsyncCalculationTask(AsyncTask):
    def __init__(self,expression:str,completion_callback=None,timeout:float=None,persist_obj:PersistentObject=None):
        self.expression=expression
        super().__init__(completion_callback=completion_callback,timeout=timeout,persist_obj=persist_obj)

    @property
    def expression(self):
        return self.__expression__

    @expression.setter
    def expression(self,expression:str):
        if(expression is None):
            raise ValueError("Expression is required")
        if(not isinstance(expression,str)):
            raise ValueError("Expression must be of type string")
        # validate expression
        try:
            eval(expression)
        except NameError:
            pass
        except Exception as e:
            raise(e)
        self.__expression__=expression

    # Overrided method
    async def __execute__(self, state:EventState, *args, **kwargs):
        if(not(args) or not(isinstance(args[0],dict)) or not(isinstance(list(args[0].keys())[0],str))):
            raise ValueError("Execute requires a second positional argument containing a dictionay of the expression variables and their values")
        inputs=args[0]
        exp=self.expression
        for k,v in inputs.items():
            exp=exp.replace(k,str(v))
        return eval(exp)

# Useful for debugging
class AsyncEventStatePrintTask(AsyncTask):
    # overrided methods
    def __init__(self, completion_callback=None, persist_obj:PersistentObject=None):
        super().__init__(timeout=None,completion_callback=completion_callback,persist_obj=persist_obj)

    # Overrided execute function
    async def __execute__(self, state:EventState, *args, **kwargs):
        await asyncio.sleep(0)
        print(state)
