apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-beat-scheduler
  namespace: application
  labels:
    app: celery-beat-scheduler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: celery-beat-scheduler
  template:
    metadata:
      labels:
        app: celery-beat-scheduler
      annotations:
        instrumentation.opentelemetry.io/inject-python: openobserve-collector/openobserve-python
    spec:
      containers:
        - name: celery-beat-scheduler-${ENV}
          image: ${ECR_REGISTRY}/${ECR_REPO_NAME}:${IMAGE_TAG}
          command:
            - celery
            - '-A'
            - app.celery_app
            - beat
            - '--loglevel=info'
          ports:
            - containerPort: 5556
              protocol: TCP
          env:
            - name: sentinel
              value: 'false' 
          volumeMounts:
            - name: config-volume
              mountPath: /app/config.ini
              subPath: config.ini
          resources:
            limits:
              cpu: 500m
              memory: 500Mi
            requests:
              cpu: 200m
              memory: 200Mi
      volumes:
        - name: config-volume
          configMap:
            name: celery-config-${ENV}
